# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目架构

EdgeMind是一个AI驱动的知识管理和浏览器自动化系统，采用多模块Maven项目结构：

### 核心模块

- **edgemind-cortex**: 主应用模块，提供AI聊天、知识库管理、浏览器自动化等核心功能
  - Spring Boot 3.5.0 + Java 17
  - 集成LangChain4j、Lucene向量存储、Sa-Token权限管理
  - 使用H2数据库作为嵌入式数据库
  - 支持本地ONNX模型（BGE中文嵌入）和远程模型（Ollama、OpenAI等）

- **edgemind-server**: 许可证管理和下载服务器
  - 提供软件许可验证、统计分析等功能

- **edgemind-licence**: 许可证验证模块
  - 处理软件授权和完整性校验

- **edgemind-plugin**: Chrome浏览器扩展
  - WebSocket通信实现浏览器自动化
  - 支持页面导航、元素交互、内容提取、截图等功能

- **mcp-chrome**: Chrome MCP服务器
  - 基于Model Context Protocol的Chrome扩展
  - 提供20+浏览器自动化工具
  - 支持语义搜索、网络监控、智能交互

## 常用开发命令

### Java/Maven项目（edgemind-cortex等）

```bash
# 构建整个项目
mvn clean compile

# 运行Cortex主应用（开发环境）
cd edgemind-cortex
mvn spring-boot:run

# 打包生产版本
mvn clean package -Pprod

# 运行测试
mvn test

# 检查代码完整性
mvn exec:java -Dexec.mainClass="com.zibbava.edgemind.licence.integrity.HashGenerator"
```

### Node.js/TypeScript项目（mcp-chrome）

```bash
# 安装依赖
cd mcp-chrome
pnpm install

# 开发模式（Chrome扩展）
pnpm dev:extension

# 开发模式（Native服务器）
pnpm dev:native

# 构建所有模块
pnpm build

# 代码检查
pnpm lint

# 类型检查
pnpm typecheck

# 格式化代码
pnpm format
```

### 浏览器插件（edgemind-plugin）

浏览器插件为纯JavaScript实现，无需构建步骤：
1. 在Chrome中打开 `chrome://extensions/`
2. 启用开发者模式
3. 点击"加载已解压的扩展程序"
4. 选择 `edgemind-plugin` 目录

## 关键技术栈

### 后端技术
- **Spring Boot 3.5.0**: Web框架和依赖注入
- **LangChain4j**: AI集成框架，支持多种大模型
- **Apache Lucene 9.x**: 全文搜索和向量存储
- **MyBatis Plus**: ORM框架，数据库操作
- **Sa-Token**: 权限认证框架
- **H2 Database**: 嵌入式数据库
- **WebSocket**: 实时通信（浏览器自动化）

### 前端技术
- **Thymeleaf**: 服务端模板引擎
- **Vue.js 3**: Chrome扩展UI组件
- **WebExtension API**: 浏览器扩展开发
- **MCP Protocol**: 模型上下文协议

### AI技术
- **BGE Small Chinese**: 本地中文嵌入模型（ONNX）
- **Ollama**: 本地大语言模型支持
- **OpenAI API**: 远程模型集成
- **向量搜索**: 语义检索和RAG

## 配置文件说明

### Cortex应用配置（application.properties）
- 服务端口: 8080，上下文路径: `/wkg`
- 默认AI模型: Ollama qwen3:14b (localhost:11434)
- 数据库: H2文件模式，存储在 `./data/edgemind`
- 向量数据库: Lucene存储在 `./data/lucene-index`
- 浏览器自动化WebSocket: `ws://localhost:8080/wkg/browser-automation`

### Server应用配置
- 服务端口: 8081，上下文路径: `/aistudio`
- 数据库: MySQL（**************:3306/aistudio）

## 开发环境设置

### 必要组件
- Java 17+ (构建和运行)
- Node.js 18.19.0+ 和 pnpm (MCP Chrome开发)
- Chrome浏览器 (测试浏览器扩展)
- Ollama (本地AI模型，可选)

### 开发流程
1. 启动Cortex应用: `cd edgemind-cortex && mvn spring-boot:run`
2. 如果需要MCP功能，启动Chrome扩展和Native服务器
3. 加载浏览器插件进行浏览器自动化测试
4. 访问 `http://localhost:8080/wkg` 使用主界面

### 数据目录结构
```
./data/
├── edgemind.mv.db          # H2数据库文件
├── edgemind.trace.db       # H2跟踪文件
└── lucene-index/           # Lucene向量索引
    ├── segments_*
    └── write.lock
```

## 集成开发指南

### 添加新的AI模型
1. 在 `RemoteModelConfig` 实体中配置模型信息
2. 在 `ModelService` 中实现模型加载逻辑
3. 更新 `UnifiedChatService` 支持新模型

### 扩展浏览器工具
1. 在 `edgemind-plugin` 中添加新的注入脚本
2. 在 `background.js` 中实现WebSocket消息处理
3. 在Cortex的 `BrowserToolExecutor` 中添加对应工具

### 知识库功能开发
1. 文档解析: `DocumentParsingStrategy` 接口
2. 向量存储: `LuceneEmbeddingStore` 类
3. 检索增强: `QueryExpansionService` 和 `EnhancedRetrievalService`
