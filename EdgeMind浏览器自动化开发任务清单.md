# EdgeMind浏览器自动化开发任务清单

基于对mcp-chrome项目的深入分析，以下是完整的开发任务清单，确保功能完整性和性能优化。

## Phase 1: 核心架构搭建 (预计2周)

### 1.1 Java后端基础架构 (3天)

#### 任务1.1.1: 创建浏览器自动化模块结构 ✅
- [x] 在`edgemind-cortex/src/main/java/com/zibbava/edgemind/cortex/`下创建`browser`包 ✅
- [x] 创建以下子包结构： ✅
  ```
  browser/
  ├── strategy/BrowserAutomationStrategy.java
  ├── tools/BrowserToolProvider.java
  ├── tools/BrowserToolRequest.java
  ├── tools/BrowserToolResponse.java
  ├── tools/impl/NavigateTool.java
  ├── tools/impl/ClickTool.java
  ├── tools/impl/FillTool.java
  ├── tools/impl/ScreenshotTool.java
  ├── tools/impl/ContentTool.java
  ├── websocket/WebSocketManager.java
  ├── websocket/BrowserWebSocketHandler.java
  ├── websocket/WebSocketConfig.java
  └── service/BrowserAutomationService.java
  ```

#### 任务1.1.2: 实现BrowserAutomationStrategy
- [ ] 继承现有ChatStrategy接口
- [ ] 实现浏览器意图检测逻辑（关键词：浏览器、网页、打开、点击、搜索等）
- [ ] 集成到现有ChatStrategyManager中，设置最高优先级
- [ ] 复用现有的消息构建和SSE处理逻辑
- [ ] 添加浏览器特定的提示词增强

#### 任务1.1.3: 配置WebSocket支持
- [ ] 在`WebSocketConfig`中配置`/wkg/browser-automation`路径
- [ ] 实现`BrowserWebSocketHandler`处理连接生命周期
- [ ] 实现`WebSocketManager`管理会话和消息路由
- [ ] 添加CORS配置支持浏览器插件跨域访问
- [ ] 配置application.yml添加浏览器自动化相关配置

### 1.2 工具提供者实现 (4天)

#### 任务1.2.1: 实现BrowserToolProvider
- [ ] 实现LangChain4j的ToolProvider接口
- [ ] 定义20+个浏览器工具的ToolSpecification：
  - 浏览器管理：`browser_navigate`, `browser_close_tabs`, `browser_go_back_forward`, `browser_get_windows_tabs`
  - 交互操作：`browser_click`, `browser_fill`, `browser_keyboard`
  - 内容分析：`browser_get_content`, `browser_get_interactive_elements`
  - 截图功能：`browser_screenshot`
  - 网络监控：`browser_network_capture_start/stop`, `browser_network_request`
  - 数据管理：`browser_history`, `browser_bookmark_search/add/delete`
  - 语义搜索：`browser_search_tabs_content`
- [ ] 实现工具执行器，通过WebSocket调用浏览器插件
- [ ] 添加超时处理（30秒）和重试机制（3次）

#### 任务1.2.2: 实现工具请求/响应模型
- [ ] 设计BrowserToolRequest数据结构
- [ ] 设计BrowserToolResponse数据结构
- [ ] 实现JSON序列化/反序列化
- [ ] 添加请求ID追踪和响应匹配机制

#### 任务1.2.3: 实现WebSocket消息管理
- [ ] 设计WebSocket消息格式（type, requestId, payload）
- [ ] 实现异步请求-响应机制
- [ ] 添加连接状态监控和自动重连
- [ ] 实现消息队列和批处理优化

### 1.3 浏览器插件基础架构 (4天)

#### 任务1.3.1: 创建EdgeMind-Plugin项目结构
- [ ] 创建Chrome扩展manifest.json（Manifest V3）
- [ ] 配置权限：tabs, activeTab, scripting, storage, webRequest, debugger
- [ ] 设置content_security_policy支持WebSocket和WASM
- [ ] 创建以下文件结构：
  ```
  edgemind-plugin/
  ├── manifest.json
  ├── background/
  │   ├── background.js
  │   ├── websocket-client.js
  │   └── tool-executors/
  ├── content/
  │   └── content.js
  ├── inject-scripts/
  │   ├── click-helper.js
  │   ├── fill-helper.js
  │   ├── keyboard-helper.js
  │   ├── screenshot-helper.js
  │   ├── web-fetcher-helper.js
  │   └── network-helper.js
  ├── popup/
  │   ├── popup.html
  │   └── popup.js
  └── utils/
      └── common.js
  ```

#### 任务1.3.2: 实现WebSocket客户端
- [ ] 创建WebSocketClient类连接`ws://localhost:8080/wkg/browser-automation`
- [ ] 实现自动重连机制（最多5次，指数退避）
- [ ] 实现消息处理和工具调用分发
- [ ] 添加连接状态管理和错误处理
- [ ] 实现握手协议和能力声明

#### 任务1.3.3: 实现基础工具执行器
- [ ] 创建BaseTool抽象类
- [ ] 实现NavigateTool（chrome.tabs.update）
- [ ] 实现ClickTool（脚本注入+元素点击）
- [ ] 实现FillTool（脚本注入+表单填写）
- [ ] 实现GetContentTool（脚本注入+内容提取）
- [ ] 添加工具执行结果标准化

### 1.4 注入脚本系统 (3天)

#### 任务1.4.1: 移植mcp-chrome注入脚本
- [ ] 移植click-helper.js：支持CSS选择器和坐标点击
- [ ] 移植fill-helper.js：支持表单填写和选择器匹配
- [ ] 移植keyboard-helper.js：支持键盘模拟和快捷键
- [ ] 移植web-fetcher-helper.js：支持HTML/文本内容提取
- [ ] 移植screenshot-helper.js：支持页面截图准备
- [ ] 移植network-helper.js：支持网络请求发送

#### 任务1.4.2: 实现脚本注入管理
- [ ] 实现动态脚本注入机制（chrome.scripting.executeScript）
- [ ] 支持ISOLATED和MAIN世界注入
- [ ] 实现脚本生命周期管理和清理
- [ ] 添加脚本注入状态追踪
- [ ] 实现脚本间通信机制

#### 任务1.4.3: 实现元素定位算法
- [ ] 实现CSS选择器优先级策略（ID > name > class > position）
- [ ] 实现智能元素匹配算法
- [ ] 添加元素可见性和可交互性检测
- [ ] 实现元素信息提取（位置、属性、文本等）

## Phase 2: 高级功能实现 (预计2周)

### 2.1 截图功能实现 (2天)

#### 任务2.1.1: 实现ScreenshotTool ✅
- [x] 使用chrome.tabs.captureVisibleTab API ✅
- [x] 支持全页面截图（滚动拼接） ✅
- [x] 支持元素截图（定位+裁剪） ✅
- [x] 支持自定义尺寸和格式 ✅
- [x] 实现Base64编码和文件存储选项 ✅

#### 任务2.1.2: 优化截图性能 ✅
- [x] 实现页面准备逻辑（隐藏固定元素） ✅
- [x] 添加截图缓存机制 ✅
- [x] 优化大页面截图内存使用 ✅
- [x] 实现截图质量和压缩选项 ✅

### 2.2 网络监控功能 (3天)

#### 任务2.2.1: 实现WebRequest网络捕获 ✅
- [x] 使用chrome.webRequest API监听网络请求 ✅
- [x] 实现NetworkCaptureTool（start/stop） ✅
- [x] 支持请求过滤（静态资源、广告域名） ✅
- [x] 实现请求数量限制和超时机制 ✅
- [x] 添加公共头部过滤优化 ✅

#### 任务2.2.2: 实现Debugger网络捕获 ✅
- [x] 使用chrome.debugger API获取完整请求/响应 ✅
- [x] 实现NetworkDebuggerTool（start/stop） ✅
- [x] 支持响应体捕获（大小限制1MB） ✅
- [x] 实现网络事件监听和数据收集 ✅
- [x] 添加调试器生命周期管理 ✅

#### 任务2.2.3: 实现网络请求工具 ✅
- [x] 实现NetworkRequestTool发送自定义HTTP请求 ✅
- [x] 支持各种HTTP方法和头部设置 ✅
- [x] 保持原始cookie和认证状态 ✅
- [x] 添加请求超时和错误处理 ✅

### 2.3 语义搜索集成 (4天)

#### 任务2.3.1: 集成Lucene向量搜索 ✅
- [x] 利用现有的EmbeddingStore和EmbeddingModel ✅
- [x] 实现BrowserSemanticSearchTool ✅
- [x] 集成标签页内容提取和向量化 ✅
- [x] 实现语义相似度搜索算法 ✅
- [x] 添加搜索结果排序和过滤 ✅

#### 任务2.3.2: 实现内容索引系统 ✅
- [x] 创建ContentIndexer类提取标签页内容 ✅
- [x] 实现文本分块算法（使用LangChain4j DocumentSplitters） ✅
- [x] 集成到现有向量数据库 ✅
- [x] 实现增量索引和缓存机制 ✅
- [x] 添加内容变化检测 ✅

#### 任务2.3.3: 优化搜索性能
- [ ] 实现批量向量化处理
- [ ] 添加搜索结果缓存
- [ ] 优化相似度计算算法
- [ ] 实现搜索历史和推荐

### 2.4 数据管理功能 (2天)

#### 任务2.4.1: 实现历史记录工具
- [ ] 使用chrome.history API
- [ ] 实现HistoryTool支持时间范围和关键词搜索
- [ ] 添加结果数量限制和排序
- [ ] 实现历史记录过滤和去重

#### 任务2.4.2: 实现书签管理工具
- [ ] 使用chrome.bookmarks API
- [ ] 实现BookmarkSearchTool、BookmarkAddTool、BookmarkDeleteTool
- [ ] 支持文件夹路径和层级搜索
- [ ] 添加书签验证和冲突处理

### 2.5 高级交互功能 (3天)

#### 任务2.5.1: 实现智能元素识别
- [ ] 实现GetInteractiveElementsTool
- [ ] 支持多种元素类型识别（按钮、链接、输入框等）
- [ ] 添加元素文本和属性提取
- [ ] 实现元素坐标和可见性检测

#### 任务2.5.2: 实现键盘模拟
- [ ] 实现KeyboardTool支持各种按键组合
- [ ] 支持快捷键和特殊键（Ctrl+C、Enter等）
- [ ] 添加按键延迟和序列控制
- [ ] 实现焦点管理和输入验证

#### 任务2.5.3: 实现窗口管理
- [ ] 实现GetWindowsAndTabsTool
- [ ] 实现CloseTabsTool支持批量关闭
- [ ] 实现GoBackForwardTool历史导航
- [ ] 添加窗口状态管理和恢复

## Phase 3: 性能优化和测试 (预计1周)

### 3.1 性能优化 (3天)

#### 任务3.1.1: WebSocket性能优化
- [ ] 实现连接池管理
- [ ] 添加消息队列和批处理
- [ ] 优化消息序列化性能
- [ ] 实现连接负载均衡

#### 任务3.1.2: 内存优化
- [ ] 实现对象池管理
- [ ] 添加内存使用监控
- [ ] 优化大数据传输
- [ ] 实现垃圾回收优化

#### 任务3.1.3: 并发处理优化
- [ ] 实现异步工具执行
- [ ] 添加线程池配置
- [ ] 优化并发请求处理
- [ ] 实现请求优先级管理

### 3.2 错误处理和监控 (2天)

#### 任务3.2.1: 完善错误处理
- [ ] 实现统一异常处理机制
- [ ] 添加详细错误日志记录
- [ ] 实现错误恢复策略
- [ ] 添加用户友好的错误提示

#### 任务3.2.2: 实现监控和日志
- [ ] 添加工具执行性能监控
- [ ] 实现操作审计日志
- [ ] 添加系统健康检查
- [ ] 实现告警和通知机制

### 3.3 集成测试 (2天)

#### 任务3.3.1: 单元测试
- [ ] 为所有工具类编写单元测试
- [ ] 测试WebSocket通信机制
- [ ] 测试错误处理逻辑
- [ ] 验证性能优化效果

#### 任务3.3.2: 端到端测试
- [ ] 测试完整的AI对话到浏览器操作流程
- [ ] 验证"打开百度搜索qq邮箱"等典型场景
- [ ] 测试并发用户和高负载情况
- [ ] 验证与现有edgemind-cortex功能的兼容性

## 开发注意事项

### 技术要求
1. **Java版本**: 使用Java 17+，与现有项目保持一致
2. **Spring Boot**: 集成到现有Spring Boot架构
3. **WebSocket**: 使用Spring WebSocket支持
4. **Chrome扩展**: 使用Manifest V3规范
5. **性能目标**: 工具调用延迟<2秒，支持并发50+用户

### 质量标准
1. **代码覆盖率**: 单元测试覆盖率>80%
2. **性能基准**: 与mcp-chrome性能持平或更优
3. **错误处理**: 所有异常情况都有适当处理
4. **文档完整**: 每个工具都有详细的使用说明
5. **安全性**: 遵循Chrome扩展安全最佳实践

### 验收标准
1. **功能完整性**: 实现mcp-chrome的所有核心功能
2. **用户体验**: 在现有聊天界面中无缝使用
3. **稳定性**: 7x24小时稳定运行
4. **可扩展性**: 支持新工具的快速添加
5. **兼容性**: 与现有edgemind-cortex功能完全兼容

这个任务清单确保了从mcp-chrome到edgemind架构的完整迁移，保持了所有核心功能的同时，充分利用了Java后端的优势和现有架构的能力。
