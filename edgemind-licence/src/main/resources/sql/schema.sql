/*
 Navicat Premium Data Transfer

 Source Server         : local-mysql8
 Source Server Type    : MySQL
 Source Server Version : 80041
 Source Host           : localhost:3306
 Source Schema         : wkg0502

 Target Server Type    : MySQL
 Target Server Version : 80041
 File Encoding         : 65001

 Date: 11/05/2025 23:45:01
*/

SET MODE MySQL;

DROP TABLE IF EXISTS `chat_conversation`;
CREATE TABLE `chat_conversation`  (
                                      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '会话ID',
                                      `user_id` bigint NOT NULL COMMENT '用户ID',
                                      `title` varchar(255)  NOT NULL COMMENT '会话标题',
                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                      `update_time` datetime NOT NULL COMMENT '最后更新时间',
                                      `status` tinyint NULL DEFAULT 1 COMMENT '状态 1-正常 0-已删除',
                                      PRIMARY KEY (`id`),
                                      INDEX `idx_conv_user_id`(`user_id`),
                                      INDEX `idx_conv_create_time`(`create_time`)
);


DROP TABLE IF EXISTS `chat_conversation_tag`;
CREATE TABLE `chat_conversation_tag`  (
                                          `conversation_id` bigint NOT NULL COMMENT '会话ID',
                                          `tag_id` bigint NOT NULL COMMENT '标签ID',
                                          PRIMARY KEY (`conversation_id`, `tag_id`),
                                          INDEX `tag_id`(`tag_id`)
);


DROP TABLE IF EXISTS `chat_message`;
CREATE TABLE `chat_message`  (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '消息ID',
                                 `conversation_id` bigint NOT NULL COMMENT '会话ID',
                                 `sender` enum('user','ai')  NOT NULL COMMENT '发送者类型',
                                 `content` text  NOT NULL COMMENT '消息内容',
                                 `thinking_content` text  NULL COMMENT 'AI思考过程内容',
                                 `image_path` varchar(255)  DEFAULT NULL COMMENT '图片路径(如果有)',
                                 `token_count` int NULL DEFAULT 0 COMMENT '消息Token数量',
                                 `create_time` datetime NOT NULL COMMENT '创建时间',
                                 `model_name` varchar(50)  DEFAULT NULL COMMENT '使用的模型名称',
                                 PRIMARY KEY (`id`),
                                 INDEX `idx_conversation_id`(`conversation_id`),
                                 INDEX `idx_msg_create_time`(`create_time`)
);


DROP TABLE IF EXISTS `chat_tag`;
CREATE TABLE `chat_tag`  (
                             `id` bigint NOT NULL AUTO_INCREMENT COMMENT '标签ID',
                             `name` varchar(50)  NOT NULL COMMENT '标签名称',
                             `color` varchar(20)  NULL DEFAULT '#1a73e8' COMMENT '标签颜色',
                             `create_time` datetime NOT NULL COMMENT '创建时间',
                             PRIMARY KEY (`id`),
                             UNIQUE INDEX `uk_tag_name`(`name`)
);


DROP TABLE IF EXISTS `kb_document_chunks`;
CREATE TABLE `kb_document_chunks`  (
                                       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                       `document_id` varchar(36)  NOT NULL COMMENT '关联的文档ID',
                                       `node_id` varchar(36)  NOT NULL COMMENT '关联的节点ID',
                                       `chunk_id` varchar(64)  NOT NULL COMMENT '向量存储中的分片ID',
                                       `chunk_index` int NOT NULL COMMENT '分片索引号',
                                       `chunk_size` int NOT NULL COMMENT '分片文本长度',
                                       `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       PRIMARY KEY (`id`),
                                       INDEX `idx_document_chunks_doc_id`(`document_id`),
                                       INDEX `idx_document_chunks_node_id`(`node_id`),
                                       INDEX `idx_document_chunks_chunk_id`(`chunk_id`)
);


DROP TABLE IF EXISTS `kb_knowledge_documents`;
CREATE TABLE `kb_knowledge_documents`  (
                                           `document_id` varchar(36)  NOT NULL COMMENT '文档ID (UUID)',
                                           `node_id` varchar(36)  NOT NULL COMMENT '关联的节点 ID',
                                           `mime_type` varchar(100)  DEFAULT NULL COMMENT '文件MIME类型',
                                           `file_size_bytes` bigint DEFAULT NULL COMMENT '文件大小（字节）',
                                           `vector_status` varchar(20)  NULL DEFAULT 'PENDING' COMMENT '向量化状态: PENDING, PROCESSING, INDEXED, FAILED',
                                           `vector_error_message` text  NULL COMMENT '记录向量化失败的原因',
                                           `last_indexed_time` datetime DEFAULT NULL COMMENT '最后成功索引的时间',
                                           `content_hash` varchar(64)  DEFAULT NULL COMMENT '文件内容的哈希值',
                                           `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                           PRIMARY KEY (`document_id`),
                                           UNIQUE INDEX `uq_kb_docs_node_id`(`node_id`),
                                           INDEX `idx_kb_docs_vector_status`(`vector_status`)
);


DROP TABLE IF EXISTS `kb_knowledge_node_closure`;
CREATE TABLE `kb_knowledge_node_closure`  (
                                              `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                              `ancestor_id` varchar(36)  NOT NULL COMMENT '祖先节点ID',
                                              `descendant_id` varchar(36)  NOT NULL COMMENT '后代节点ID',
                                              `depth` int NOT NULL COMMENT '层级深度 (0表示自身)',
                                              `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              PRIMARY KEY (`id`),
                                              UNIQUE INDEX `uq_kb_node_closure_rel`(`ancestor_id`, `descendant_id`),
                                              INDEX `idx_kb_node_closure_ancestor`(`ancestor_id`),
                                              INDEX `idx_kb_node_closure_descendant`(`descendant_id`),
                                              INDEX `idx_kb_node_closure_depth`(`depth`)
);


DROP TABLE IF EXISTS `kb_knowledge_nodes`;
CREATE TABLE `kb_knowledge_nodes`  (
                                       `node_id` varchar(36)  NOT NULL COMMENT '节点ID (UUID)',
                                       `space_id` varchar(36)  NOT NULL COMMENT '所属知识空间 ID',
                                       `parent_node_id` varchar(36)  DEFAULT NULL COMMENT '父节点 ID，根节点为 NULL',
                                       `name` varchar(255)  NOT NULL COMMENT '节点名称',
                                       `type` varchar(10)  NOT NULL COMMENT '节点类型: FOLDER, FILE',
                                       `create_by` bigint DEFAULT NULL COMMENT '创建者用户ID (sys_user.id)',
                                       `update_by` bigint DEFAULT NULL COMMENT '最后更新者用户ID (sys_user.id)',
                                       `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                       PRIMARY KEY (`node_id`),
                                       UNIQUE INDEX `uq_kb_nodes_parent_name`(`space_id`, `parent_node_id`, `name`),
                                       INDEX `idx_kb_nodes_space_id`(`space_id`),
                                       INDEX `idx_kb_nodes_parent_id`(`parent_node_id`)
);


DROP TABLE IF EXISTS `kb_knowledge_spaces`;
CREATE TABLE `kb_knowledge_spaces`  (
                                        `space_id` varchar(36)  NOT NULL COMMENT '知识空间ID (UUID)',
                                        `owner_user_id` bigint DEFAULT NULL COMMENT '私人空间的所有者 ID (sys_user.id)，团队空间时为 NULL',
                                        `name` varchar(255)  NOT NULL COMMENT '空间名称',
                                        `description` text  NULL COMMENT '空间描述',
                                        `access_type` varchar(20)  DEFAULT 'ROLE_BASED' COMMENT '访问类型：PUBLIC(公开), PRIVATE(私有), ROLE_BASED(基于角色)',
                                        `create_by` bigint DEFAULT NULL COMMENT '创建者用户ID (sys_user.id)',
                                        `update_by` bigint DEFAULT NULL COMMENT '最后更新者用户ID (sys_user.id)',
                                        `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                        PRIMARY KEY (`space_id`),
                                        INDEX `idx_kb_spaces_owner`(`owner_user_id`),
                                        INDEX `idx_kb_spaces_access_type`(`access_type`)
);


DROP TABLE IF EXISTS `kb_space_roles`;
CREATE TABLE `kb_space_roles` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `space_id` varchar(36)  NOT NULL COMMENT '知识空间ID',
    `role_id` bigint NOT NULL COMMENT '角色ID',
    `permission_level` varchar(20)  DEFAULT 'read' COMMENT '权限级别：read(只读), write(读写), admin(管理)',
    `create_by` bigint COMMENT '授权人ID',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_space_role` (`space_id`, `role_id`),
    INDEX `idx_space_roles_space`(`space_id`),
    INDEX `idx_space_roles_role`(`role_id`)
);

DROP TABLE IF EXISTS `sys_department`;
CREATE TABLE `sys_department`  (
                                   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门ID',
                                   `dept_name` varchar(50)  NOT NULL COMMENT '部门名称',
                                   `dept_code` varchar(50)  DEFAULT NULL COMMENT '部门编码',
                                   `parent_id` bigint NULL DEFAULT 0 COMMENT '父部门ID (0表示顶级)',
                                   `dept_level` int NULL DEFAULT 1 COMMENT '部门层级',
                                   `dept_path` varchar(500)  DEFAULT NULL COMMENT '部门路径(用于快速查询)',
                                   `manager_id` bigint DEFAULT NULL COMMENT '部门负责人ID',
                                   `sort_order` int NOT NULL DEFAULT 0 COMMENT '显示顺序',
                                   `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 (0: 停用, 1: 正常)',
                                   `description` varchar(255)  DEFAULT NULL COMMENT '部门描述/职责',
                                   `contact_phone` varchar(20)  DEFAULT NULL COMMENT '部门联系电话',
                                   `contact_email` varchar(100)  DEFAULT NULL COMMENT '部门联系邮箱',
                                   `address` varchar(255)  DEFAULT NULL COMMENT '部门地址',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                   `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                   `deleted` tinyint DEFAULT '0' COMMENT '是否删除：0-否，1-是',
                                   PRIMARY KEY (`id`),
                                   UNIQUE INDEX `uk_dept_code`(`dept_code`),
                                   INDEX `idx_dept_parent_id`(`parent_id`),
                                   INDEX `idx_dept_status`(`status`),
                                   INDEX `idx_manager_id`(`manager_id`),
                                   INDEX `idx_sort_order`(`sort_order`)
);

INSERT INTO `sys_department` (`id`, `dept_name`, `dept_code`, `parent_id`, `dept_level`, `dept_path`, `manager_id`, `sort_order`, `status`, `description`, `contact_phone`, `contact_email`, `address`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (1, '总部', NULL, 0, 1, '/1', NULL, 0, 1, NULL, NULL, NULL, NULL, '2025-04-26 12:56:11', '2025-04-26 12:56:11', NULL, NULL, 0);

DROP TABLE IF EXISTS `sys_license`;
CREATE TABLE `sys_license`  (
                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '许可证ID',
                                `license_key` text  NOT NULL COMMENT '许可证密钥',
                                `hardware_fingerprint` varchar(255)  NOT NULL COMMENT '硬件指纹',
                                `status` tinyint NOT NULL DEFAULT 0 COMMENT '激活状态：0-未激活，1-已激活',
                                `activated_time` datetime DEFAULT NULL COMMENT '激活时间',
                                `expire_time` datetime DEFAULT NULL COMMENT '过期时间，NULL表示永不过期',
                                `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                PRIMARY KEY (`id`)
);


DROP TABLE IF EXISTS `sys_permission`;
CREATE TABLE `sys_permission`  (
                                   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
                                   `permission_name` varchar(100)  NOT NULL COMMENT '权限名称',
                                   `permission_code` varchar(100)  NOT NULL COMMENT '权限编码 (用于权限控制)',
                                   `type` varchar(10)  NOT NULL COMMENT '权限类型 (MENU, BUTTON, API, DATA)',
                                   `parent_id` bigint NULL DEFAULT 0 COMMENT '父权限ID (0表示顶级)',
                                   `resource_url` varchar(255)  DEFAULT NULL COMMENT '资源URL',
                                   `http_method` varchar(10)  DEFAULT NULL COMMENT 'HTTP方法',
                                   `icon` varchar(50)  DEFAULT NULL COMMENT '图标',
                                   `sort_order` int DEFAULT '0' COMMENT '排序',
                                   `is_external` tinyint DEFAULT '0' COMMENT '是否外部链接',
                                   `component_path` varchar(255)  DEFAULT NULL COMMENT '组件路径',
                                   `description` varchar(255)  DEFAULT NULL COMMENT '权限描述',
                                   `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 (0: 禁用, 1: 启用)',
                                   `is_dynamic` tinyint DEFAULT '0' COMMENT '是否为动态权限',
                                   `related_resource_id` varchar(100)  DEFAULT NULL COMMENT '关联资源ID（如知识空间ID）',
                                   `related_resource_type` varchar(50)  DEFAULT NULL COMMENT '关联资源类型（如KNOWLEDGE_SPACE）',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                   `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                   `deleted` tinyint DEFAULT '0' COMMENT '是否删除：0-否，1-是',
                                   PRIMARY KEY (`id`),
                                   UNIQUE INDEX `uk_permission_code`(`permission_code`),
                                   INDEX `idx_perm_parent_id`(`parent_id`),
                                   INDEX `idx_perm_type`(`type`),
                                   INDEX `idx_perm_status`(`status`),
                                   INDEX `idx_type_status`(`type`, `status`),
                                   INDEX `idx_dynamic_resource`(`related_resource_type`, `related_resource_id`),
                                   INDEX `idx_is_dynamic`(`is_dynamic`)
);

INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `is_dynamic`, `related_resource_id`, `related_resource_type`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (1, 'AI对话', 'menu:view:ai_chat', 'MENU', 0, NULL, NULL, 'bi-chat-left-text', 1, 0, NULL, 'AI对话菜单', 1, 0, NULL, NULL, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (2, 'AI知识库', 'menu:view:ai_knowledge', 'MENU', 0, NULL, NULL, 'bi-people', 2, 0, NULL, 'AI知识库菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (3, '模型中心', 'menu:view:models', 'MENU', 0, NULL, NULL, 'bi-cpu', 3, 0, NULL, '模型中心菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (4, '系统设置', 'menu:view:settings', 'MENU', 0, NULL, NULL, 'bi-gear', 4, 0, NULL, '系统设置菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (5, '权限管理', 'menu:view:permission_group', 'MENU', 0, NULL, NULL, 'bi-shield-lock', 5, 0, NULL, '权限管理菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (6, '用户管理', 'menu:view:users', 'MENU', 5, NULL, NULL, 'bi-person', 1, 0, NULL, '用户管理子菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (7, '部门管理', 'menu:view:departments', 'MENU', 5, NULL, NULL, 'bi-diagram-3', 2, 0, NULL, '部门管理子菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (8, '角色管理', 'menu:view:roles', 'MENU', 5, NULL, NULL, 'bi-people', 3, 0, NULL, '角色管理子菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (9, '权限设置', 'menu:view:permissions', 'MENU', 5, NULL, NULL, 'bi-key', 4, 0, NULL, '权限设置子菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);

INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (11, '查询用户列表', 'user:manage:list', 'API', 6, '/api/system/user/list', 'GET', NULL, 1, 0, NULL, '查询用户列表权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (12, '创建用户', 'user:manage:create', 'API', 6, '/api/system/user', 'POST', NULL, 2, 0, NULL, '创建用户权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (13, '更新用户', 'user:manage:update', 'API', 6, '/api/system/user/*', 'PUT', NULL, 3, 0, NULL, '更新用户权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (14, '删除用户', 'user:manage:delete', 'API', 6, '/api/system/user/*', 'DELETE', NULL, 4, 0, NULL, '删除用户权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (15, '分配用户角色', 'user:manage:assign-role', 'API', 6, '/api/system/user/*/roles', 'POST', NULL, 5, 0, NULL, '分配用户角色权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (16, '重置用户密码', 'user:manage:reset-password', 'API', 6, '/api/system/user/*/reset-password', 'POST', NULL, 6, 0, NULL, '重置用户密码权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (17, '导出用户数据', 'user:manage:export', 'API', 6, '/api/system/user/export', 'POST', NULL, 7, 0, NULL, '导出用户数据权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (18, '查询角色列表', 'role:manage:list', 'API', 8, '/api/system/role/list', 'GET', NULL, 1, 0, NULL, '查询角色列表权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (19, '创建角色', 'role:manage:create', 'API', 8, '/api/system/role', 'POST', NULL, 2, 0, NULL, '创建角色权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (20, '更新角色', 'role:manage:update', 'API', 8, '/api/system/role/*', 'PUT', NULL, 3, 0, NULL, '更新角色权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (21, '删除角色', 'role:manage:delete', 'API', 8, '/api/system/role/*', 'DELETE', NULL, 4, 0, NULL, '删除角色权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (22, '分配角色权限', 'role:manage:assign-permission', 'API', 8, '/api/system/role/*/permissions', 'POST', NULL, 5, 0, NULL, '分配角色权限权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (23, '查询权限列表', 'permission:manage:list', 'API', 9, '/api/system/permission/list', 'GET', NULL, 1, 0, NULL, '查询权限列表权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (24, '创建权限', 'permission:manage:create', 'API', 9, '/api/system/permission', 'POST', NULL, 2, 0, NULL, '创建权限权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (25, '更新权限', 'permission:manage:update', 'API', 9, '/api/system/permission/*', 'PUT', NULL, 3, 0, NULL, '更新权限权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (26, '删除权限', 'permission:manage:delete', 'API', 9, '/api/system/permission/*', 'DELETE', NULL, 4, 0, NULL, '删除权限权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);

INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (27, '查询部门列表', 'dept:manage:list', 'API', 7, '/api/system/dept/tree', 'GET', NULL, 1, 0, NULL, '查询部门列表权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (28, '创建部门', 'dept:manage:create', 'API', 7, '/api/system/dept', 'POST', NULL, 2, 0, NULL, '创建部门权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (29, '更新部门', 'dept:manage:update', 'API', 7, '/api/system/dept/*', 'PUT', NULL, 3, 0, NULL, '更新部门权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (30, '删除部门', 'dept:manage:delete', 'API', 7, '/api/system/dept/*', 'DELETE', NULL, 4, 0, NULL, '删除部门权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);

INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (31, '查看系统配置', 'system:config:list', 'API', 4, '/api/settings', 'GET', NULL, 1, 0, NULL, '查看系统配置权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (32, '更新系统配置', 'system:config:update', 'API', 4, '/api/settings', 'POST', NULL, 2, 0, NULL, '更新系统配置权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);

INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (33, '知识库管理', 'knowledge:manage', 'MENU', 0, NULL, NULL, 'bi-book', 100, 0, NULL, '知识库相关权限的父级菜单', 1, NOW(), NOW(), NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (34, '创建知识空间', 'knowledge:space:create', 'BUTTON', 33, NULL, NULL, NULL, 1, 0, NULL, '创建新的知识空间', 1, NOW(), NOW(), NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (35, '编辑知识空间', 'knowledge:space:edit', 'BUTTON', 33, NULL, NULL, NULL, 2, 0, NULL, '编辑知识空间信息和权限', 1, NOW(), NOW(), NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (36, '删除知识空间', 'knowledge:space:delete', 'BUTTON', 33, NULL, NULL, NULL, 3, 0, NULL, '删除知识空间', 1, NOW(), NOW(), NULL, NULL, 0);





DROP TABLE IF EXISTS `sys_permission_template`;
CREATE TABLE `sys_permission_template` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '模板ID',
    `template_name` varchar(100)  NOT NULL COMMENT '模板名称',
    `template_code` varchar(100)  NOT NULL COMMENT '模板编码',
    `permission_type` varchar(20)  NOT NULL COMMENT '权限类型',
    `resource_type` varchar(50)  NOT NULL COMMENT '资源类型',
    `description` text  COMMENT '描述',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    INDEX `idx_resource_type`(`resource_type`)
);

INSERT INTO `sys_permission_template` (`template_name`, `template_code`, `permission_type`, `resource_type`, `description`) VALUES
('知识空间查看', 'kb:space:{space_id}:view', 'DATA', 'KNOWLEDGE_SPACE', '查看指定知识空间的权限'),
('知识空间编辑', 'kb:space:{space_id}:edit', 'DATA', 'KNOWLEDGE_SPACE', '编辑指定知识空间内容的权限'),
('知识空间管理', 'kb:space:{space_id}:manage', 'DATA', 'KNOWLEDGE_SPACE', '管理指定知识空间的权限（包括权限配置）'),
('知识空间删除', 'kb:space:{space_id}:delete', 'DATA', 'KNOWLEDGE_SPACE', '删除指定知识空间的权限');

UPDATE `sys_permission` SET `is_dynamic` = 0, `related_resource_id` = NULL, `related_resource_type` = NULL WHERE `is_dynamic` IS NULL;

INSERT INTO `kb_knowledge_spaces` (`space_id`, `name`, `description`, `owner_user_id`, `access_type`, `create_by`, `update_by`) VALUES
('default-public', '公共知识库', '所有用户都可以访问的公共知识空间', NULL, 'PUBLIC', 1, 1);

DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
                             `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
                             `role_name` varchar(50)  NOT NULL COMMENT '角色名称',
                             `role_code` varchar(50)  NOT NULL COMMENT '角色编码 (用于权限控制)',
                             `description` varchar(255)  DEFAULT NULL COMMENT '角色描述',
                             `data_scope` varchar(20)  DEFAULT 'DEPT' COMMENT '数据权限范围',
                             `dept_ids` text  COMMENT '数据权限部门ID列表',
                             `sort_order` int DEFAULT '0' COMMENT '排序',
                             `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 (0: 禁用, 1: 启用)',
                             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                             `create_by` bigint DEFAULT NULL COMMENT '创建人',
                             `update_by` bigint DEFAULT NULL COMMENT '更新人',
                             `deleted` tinyint DEFAULT '0' COMMENT '是否删除：0-否，1-是',
                             PRIMARY KEY (`id`),
                             UNIQUE INDEX `uk_role_code`(`role_code`),
                             INDEX `idx_role_status`(`status`)
);

INSERT INTO `sys_role` (`id`, `role_name`, `role_code`, `description`, `data_scope`, `dept_ids`, `sort_order`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (1, '超级管理员', 'admin', '拥有系统所有权限', 'ALL', NULL, 0, 1, '2025-04-26 12:56:30', '2025-04-26 12:56:30', NULL, NULL, 0);
INSERT INTO `sys_role` (`id`, `role_name`, `role_code`, `description`, `data_scope`, `dept_ids`, `sort_order`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (2, '普通用户', 'user', '拥有基本访问和操作权限', 'DEPT', NULL, 1, 1, '2025-04-26 12:56:30', '2025-04-26 12:56:30', NULL, NULL, 0);

DROP TABLE IF EXISTS `sys_role_permission`;
CREATE TABLE `sys_role_permission`  (
                                        `role_id` bigint NOT NULL COMMENT '角色ID',
                                        `permission_id` bigint NOT NULL COMMENT '权限ID',
                                        PRIMARY KEY (`role_id`, `permission_id`),
                                        INDEX `idx_rp_permission_id`(`permission_id`),
                                        INDEX `idx_rp_role_id`(`role_id`)
);

INSERT INTO `sys_role_permission` VALUES (1, 1);
INSERT INTO `sys_role_permission` VALUES (1, 2);
INSERT INTO `sys_role_permission` VALUES (1, 3);
INSERT INTO `sys_role_permission` VALUES (1, 4);
INSERT INTO `sys_role_permission` VALUES (1, 5);
INSERT INTO `sys_role_permission` VALUES (1, 6);
INSERT INTO `sys_role_permission` VALUES (1, 7);
INSERT INTO `sys_role_permission` VALUES (1, 8);
INSERT INTO `sys_role_permission` VALUES (1, 9);
INSERT INTO `sys_role_permission` VALUES (1, 10);
INSERT INTO `sys_role_permission` VALUES (1, 11);
INSERT INTO `sys_role_permission` VALUES (1, 12);
INSERT INTO `sys_role_permission` VALUES (1, 13);
INSERT INTO `sys_role_permission` VALUES (1, 14);
INSERT INTO `sys_role_permission` VALUES (1, 15);
INSERT INTO `sys_role_permission` VALUES (1, 16);
INSERT INTO `sys_role_permission` VALUES (1, 17);
INSERT INTO `sys_role_permission` VALUES (1, 18);
INSERT INTO `sys_role_permission` VALUES (1, 19);
INSERT INTO `sys_role_permission` VALUES (1, 20);
INSERT INTO `sys_role_permission` VALUES (1, 21);
INSERT INTO `sys_role_permission` VALUES (1, 22);
INSERT INTO `sys_role_permission` VALUES (1, 23);
INSERT INTO `sys_role_permission` VALUES (1, 24);
INSERT INTO `sys_role_permission` VALUES (1, 25);
INSERT INTO `sys_role_permission` VALUES (1, 26);
INSERT INTO `sys_role_permission` VALUES (1, 27);
INSERT INTO `sys_role_permission` VALUES (1, 28);
INSERT INTO `sys_role_permission` VALUES (1, 29);
INSERT INTO `sys_role_permission` VALUES (1, 30);
INSERT INTO `sys_role_permission` VALUES (1, 31);
INSERT INTO `sys_role_permission` VALUES (1, 32);
INSERT INTO `sys_role_permission` VALUES (1, 33);
INSERT INTO `sys_role_permission` VALUES (1, 34);
INSERT INTO `sys_role_permission` VALUES (1, 35);
INSERT INTO `sys_role_permission` VALUES (1, 36);

INSERT INTO `sys_role_permission` VALUES (2, 1);
INSERT INTO `sys_role_permission` VALUES (2, 2);
INSERT INTO `sys_role_permission` VALUES (2, 3);

DROP TABLE IF EXISTS `sys_settings`;
CREATE TABLE `sys_settings`  (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '设置ID',
                                 `setting_key` varchar(100)  NOT NULL COMMENT '设置键名',
                                 `setting_value` text  NULL COMMENT '设置值',
                                 `description` varchar(255)  DEFAULT NULL COMMENT '设置描述',
                                 `setting_group` varchar(50)  DEFAULT NULL COMMENT '设置分组',
                                 `setting_type` varchar(20)  DEFAULT 'STRING' COMMENT '设置类型',
                                 `is_system` tinyint DEFAULT '0' COMMENT '是否系统内置',
                                 `sort_order` int DEFAULT '0' COMMENT '排序',
                                 `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
                                 `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                 `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                 `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                 PRIMARY KEY (`id`),
                                 UNIQUE INDEX `uk_setting_key`(`setting_key`),
                                 INDEX `idx_settings_group`(`setting_group`),
                                 INDEX `idx_settings_status`(`status`)
);

INSERT INTO `sys_settings` (`setting_key`, `setting_value`, `description`, `setting_group`, `setting_type`, `is_system`, `sort_order`, `status`) VALUES
('system.name', 'EdgeMind智能知识管理系统', '系统名称', 'SYSTEM', 'STRING', 1, 1, 1),
('system.version', '1.0.0', '系统版本', 'SYSTEM', 'STRING', 1, 2, 1),
('system.logo', '/wkg/images/logo.png', '系统Logo', 'APPEARANCE', 'IMAGE', 1, 1, 1),
('system.favicon', '/wkg/images/favicon.ico', '网站图标', 'APPEARANCE', 'IMAGE', 1, 2, 1),
('security.password.min_length', '6', '密码最小长度', 'SECURITY', 'NUMBER', 1, 1, 1),
('security.password.require_special', 'false', '密码是否需要特殊字符', 'SECURITY', 'BOOLEAN', 1, 2, 1),
('security.login.max_attempts', '5', '登录最大尝试次数', 'SECURITY', 'NUMBER', 1, 3, 1),
('security.session.timeout', '7200', '会话超时时间（秒）', 'SECURITY', 'NUMBER', 1, 4, 1),
('notification.email.enabled', 'false', '是否启用邮件通知', 'NOTIFICATION', 'BOOLEAN', 1, 1, 1),
('notification.sms.enabled', 'false', '是否启用短信通知', 'NOTIFICATION', 'BOOLEAN', 1, 2, 1);



DROP TABLE IF EXISTS `sys_login_log`;
CREATE TABLE `sys_login_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint DEFAULT NULL COMMENT '用户ID',
    `username` varchar(50)  NOT NULL COMMENT '用户名',
    `login_type` varchar(20)  NOT NULL DEFAULT 'WEB' COMMENT '登录类型：WEB,MOBILE,API',
    `ip_address` varchar(50)  DEFAULT NULL COMMENT '登录IP',
    `location` varchar(100)  DEFAULT NULL COMMENT '登录地点',
    `browser` varchar(100)  DEFAULT NULL COMMENT '浏览器',
    `os` varchar(100)  DEFAULT NULL COMMENT '操作系统',
    `device_type` varchar(20)  DEFAULT NULL COMMENT '设备类型：Desktop,Mobile,Tablet',
    `user_agent` varchar(1000)  DEFAULT NULL COMMENT '用户代理',
    `login_status` tinyint NOT NULL COMMENT '登录状态：0-失败，1-成功',
    `failure_reason` varchar(255)  DEFAULT NULL COMMENT '失败原因',
    `session_id` varchar(100)  DEFAULT NULL COMMENT '会话ID',
    `token_value` varchar(200)  DEFAULT NULL COMMENT 'Token值',
    `login_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    `logout_time` datetime DEFAULT NULL COMMENT '登出时间',
    `online_duration` bigint DEFAULT NULL COMMENT '在线时长（分钟）',
    PRIMARY KEY (`id`),
    KEY `idx_log_user_id` (`user_id`),
    KEY `idx_log_username` (`username`),
    KEY `idx_log_login_status` (`login_status`),
    KEY `idx_log_login_time` (`login_time`),
    KEY `idx_log_ip_address` (`ip_address`),
    KEY `idx_log_session_id` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='登录日志表' ROW_FORMAT=Dynamic;


DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
                             `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                             `username` varchar(255)  NOT NULL COMMENT '用户名',
                             `password` varchar(255)  NOT NULL COMMENT '密码 (存储加密后的值)',
                             `nickname` varchar(50)  DEFAULT NULL COMMENT '用户昵称/姓名',
                             `email` varchar(100)  DEFAULT NULL COMMENT '邮箱',
                             `phone` varchar(20)  DEFAULT NULL COMMENT '手机号',
                             `avatar` varchar(255)  DEFAULT NULL COMMENT '头像',
                             `status` tinyint NOT NULL DEFAULT 1 COMMENT '用户状态 (0: 禁用, 1: 启用)',
                             `dept_id` bigint DEFAULT NULL COMMENT '所属部门ID',
                             `remark` varchar(255)  DEFAULT NULL COMMENT '备注',
                             `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
                             `last_login_ip` varchar(50)  DEFAULT NULL COMMENT '最后登录IP',
                             `password_update_time` datetime DEFAULT NULL COMMENT '密码更新时间',
                             `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                             PRIMARY KEY (`id`),
                             UNIQUE INDEX `uk_username`(`username`),
                             INDEX `idx_user_username`(`username`),
                             INDEX `idx_user_status`(`status`),
                             INDEX `idx_user_dept_id`(`dept_id`),
                             INDEX `idx_user_email`(`email`),
                             INDEX `idx_user_phone`(`phone`),
                             INDEX `idx_user_create_time`(`create_time`)
);

INSERT INTO `sys_user` (`id`, `username`, `password`, `nickname`, `email`, `phone`, `avatar`, `status`, `dept_id`, `remark`, `last_login_time`, `last_login_ip`, `password_update_time`, `create_time`, `update_time`)
VALUES (1, 'admin', '944505fe59f2ff5c427ba5dbe9769eca', '管理员', '<EMAIL>', '13800000000', NULL, 1, 1, '初始超级管理员', NULL, NULL, NULL, '2025-04-26 12:57:48', '2025-04-26 12:57:48');

DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
                                  `user_id` bigint NOT NULL COMMENT '用户ID',
                                  `role_id` bigint NOT NULL COMMENT '角色ID',
                                  PRIMARY KEY (`user_id`, `role_id`),
                                  INDEX `idx_ur_role_id`(`role_id`),
                                  INDEX `idx_ur_user_id`(`user_id`)
);

INSERT INTO `sys_user_role` VALUES (1, 1);

-- 创建远程模型配置表（H2格式）
DROP TABLE IF EXISTS remote_model_config;
CREATE TABLE remote_model_config (
    id BIGINT NOT NULL AUTO_INCREMENT,
    model_id VARCHAR(100) NOT NULL,
    api_key VARCHAR(500) NOT NULL,
    enabled TINYINT DEFAULT 1,
    config_params TEXT DEFAULT NULL,
    remark VARCHAR(500) DEFAULT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used_time TIMESTAMP DEFAULT NULL,
    usage_count BIGINT DEFAULT 0,
    deleted TINYINT DEFAULT 0,

    PRIMARY KEY (id)
);

-- 创建索引
CREATE INDEX idx_model_id ON remote_model_config (model_id);
CREATE INDEX idx_enabled ON remote_model_config (enabled);
CREATE INDEX idx_deleted ON remote_model_config (deleted);

-- 创建唯一约束
ALTER TABLE remote_model_config ADD CONSTRAINT uk_model_id UNIQUE (model_id, deleted);

DROP TABLE IF EXISTS `user_preference`;
CREATE TABLE `user_preference`  (
                                    `user_id` bigint NOT NULL COMMENT '用户ID',
                                    `preferred_model` varchar(100)  DEFAULT NULL COMMENT '偏好的模型',
                                    `theme` varchar(20)  NULL DEFAULT 'light' COMMENT '主题偏好',
                                    `display_thinking` tinyint NULL DEFAULT 1 COMMENT '是否显示思考过程',
                                    `last_active_conversation_id` bigint DEFAULT NULL COMMENT '最后活动的会话ID',
                                    `update_time` datetime NOT NULL COMMENT '更新时间',
                                    PRIMARY KEY (`user_id`),
                                    INDEX `last_active_conversation_id`(`last_active_conversation_id`)
);


