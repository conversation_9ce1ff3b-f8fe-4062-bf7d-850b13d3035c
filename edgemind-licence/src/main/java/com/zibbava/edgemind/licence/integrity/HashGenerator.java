package com.zibbava.edgemind.licence.integrity;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.stream.Stream;

/**
 * 哈希生成和校验工具类
 * 保护cortex模块的所有.class文件
 */
public class HashGenerator {

    private static final String INTEGRITY_PROPS_FILE = "integrity.properties";

    /**
     * 构建时工具：生成所有cortex模块.class文件的哈希并保存到属性文件
     */
    public static void main(String[] args) throws IOException {
        if (args.length < 2) {
            System.err.println("错误：需要提供两个参数：<cortex-classes-dir> <licence-classes-dir>");
            System.exit(1);
        }

        Path cortexClassesPath = Paths.get(args[0]);
        Path licenceClassesPath = Paths.get(args[1]);

        if (!Files.exists(cortexClassesPath)) {
            System.err.println("错误：Cortex模块的classes目录未找到: " + cortexClassesPath);
            System.err.println("请确保 'cortex' 模块已成功编译。");
            System.exit(1);
        }

        // 确保licence的classes目录存在
        Files.createDirectories(licenceClassesPath);

        System.out.println("正在扫描 " + cortexClassesPath + " 下的所有.class文件...");
        Map<String, String> hashes = generateHashesForDirectory(cortexClassesPath);

        Path propsFilePath = licenceClassesPath.resolve(INTEGRITY_PROPS_FILE);
        saveHashesToProperties(hashes, propsFilePath);

        System.out.println("成功生成 " + hashes.size() + " 个文件的哈希值。");
        System.out.println("已保存到: " + propsFilePath);
    }

    /**
     * 递归生成目录下所有文件的哈希
     */
    private static Map<String, String> generateHashesForDirectory(Path directory) throws IOException {
        Map<String, String> hashes = new HashMap<>();
        try (Stream<Path> paths = Files.walk(directory)) {
            paths.filter(Files::isRegularFile)
                 .filter(path -> path.toString().endsWith(".class"))
                 .filter(path -> {
                     String relativePath = directory.relativize(path).toString().replace("\\", "/");
                     return relativePath.startsWith("com/zibbava/edgemind/cortex/");
                 })
                 .forEach(path -> {
                     try {
                         String relativePath = directory.relativize(path).toString().replace("\\", "/");
                         System.out.println("Hashing file: " + relativePath);
                         String hash = calculateFileHash(path);
                         hashes.put(relativePath, hash);
                     } catch (IOException | NoSuchAlgorithmException e) {
                         System.err.println("计算文件哈希失败: " + path + " - " + e.getMessage());
                     }
                 });
        }
        return hashes;
    }
    
    /**
     * 计算单个文件的SHA-256哈希值
     */
    public static String calculateFileHash(Path filePath) throws IOException, NoSuchAlgorithmException {
        byte[] fileBytes = Files.readAllBytes(filePath);
        return bytesToHex(calculateHash(fileBytes));
    }

    /**
     * 计算输入流的SHA-256哈希值
     */
    public static String calculateStreamHash(InputStream is) throws IOException, NoSuchAlgorithmException {
        byte[] streamBytes = is.readAllBytes();
        return bytesToHex(calculateHash(streamBytes));
    }

    private static byte[] calculateHash(byte[] bytes) throws NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        return digest.digest(bytes);
    }

    private static String bytesToHex(byte[] hashBytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : hashBytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    /**
     * 将哈希映射保存到.properties文件
     */
    private static void saveHashesToProperties(Map<String, String> hashes, Path outputFile) throws IOException {
        Properties props = new Properties();
        props.putAll(hashes);
        
        try (OutputStream os = Files.newOutputStream(outputFile)) {
            props.store(os, "Edgemind Cortex Integrity Hashes");
        }
    }
} 