package com.zibbava.edgemind.licence.initializer;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.sql.*;

/**
 * 极简数据库初始化器
 * 功能：检测数据库类型 -> MySQL: 设置密码 -> 创建数据库; H2: 直接初始化
 */
@Component
@Order(1)
public class DatabaseInitializer implements CommandLineRunner {

    @Value("${spring.datasource.url}")
    private String datasourceUrl;

    @Value("${spring.datasource.username}")
    private String username;

    @Value("${spring.datasource.password:}")
    private String password;

    @Override
    public void run(String... args) {
        try {
            // 检测数据库类型
            if (isH2Database()) {
                // H2数据库处理逻辑
                handleH2Database();
            } else {
                // MySQL数据库处理逻辑
                handleMySQLDatabase();
            }
        } catch (Exception e) {
            System.err.println("数据库初始化失败: " + e.getMessage());
        }
    }

    /**
     * 检测是否为H2数据库
     */
    private boolean isH2Database() {
        return datasourceUrl != null && datasourceUrl.startsWith("jdbc:h2:");
    }

    /**
     * 处理H2数据库初始化
     */
    private void handleH2Database() throws Exception {
        System.out.println("检测到数据库，开始初始化...");

        // H2数据库不需要创建数据库，直接检查表是否存在
        if (!isTablesExistH2()) {
            initializeTablesH2();
            System.out.println("数据库表初始化完成");
        } else {
            System.out.println("数据库表已存在，跳过初始化");
        }
    }

    /**
     * 处理MySQL数据库初始化
     */
    private void handleMySQLDatabase() throws Exception {
        System.out.println("检测到MySQL数据库，开始初始化...");

        // 检查并设置密码
        if (!isPasswordSet()) {
            trySetPassword();
        }

        createDatabase();

        // 检查并初始化表
        if (!isTablesExist()) {
            initializeTables();
        }
    }

    private boolean isPasswordSet() {
        try (Connection conn = DriverManager.getConnection(datasourceUrl, username, password)) {
            // 如果能用配置的密码连接成功，说明密码已设置
            return true;
        } catch (SQLException e) {
            // 连接失败，可能密码未设置或密码错误
            return false;
        }
    }

    private void trySetPassword() {
        try (Connection conn = DriverManager.getConnection(datasourceUrl, username, "wkg123456");
             Statement stmt = conn.createStatement()) {
            stmt.executeUpdate(String.format("ALTER USER '%s'@'%%' IDENTIFIED BY '%s'", username, password));
        } catch (SQLException e) {
        }
    }

    private void createDatabase() throws SQLException {
        String dbName = datasourceUrl.split("/")[3].split("\\?")[0];

        try (Connection conn = DriverManager.getConnection(datasourceUrl, username, password);
             Statement stmt = conn.createStatement()) {
            stmt.executeUpdate(String.format("CREATE DATABASE IF NOT EXISTS `%s` CHARACTER SET utf8mb4", dbName));
        }
    }

    private void initializeTables() {
        try {
            // 执行建表脚本
            executeSchemaScript();
        } catch (Exception e) {
        }
    }

    private boolean isTablesExist() throws SQLException {
        try (Connection conn = DriverManager.getConnection(datasourceUrl, username, password);
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SHOW TABLES LIKE 'sys_user'")) {
            return rs.next();
        }
    }

    /**
     * 检查H2数据库中表是否存在
     */
    private boolean isTablesExistH2() throws SQLException {
        try (Connection conn = DriverManager.getConnection(datasourceUrl, username, password);
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'SYS_USER'")) {
            return rs.next() && rs.getInt(1) > 0;
        }
    }

    /**
     * 初始化H2数据库表
     */
    private void initializeTablesH2() throws Exception {
        ClassPathResource resource = new ClassPathResource("sql/schema.sql");

        try (InputStream inputStream = resource.getInputStream();
             Connection conn = DriverManager.getConnection(datasourceUrl, username, password)) {

            // 读取整个SQL文件内容
            String sqlContent = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);

            // 执行SQL脚本 - 简化版本（已移除所有注释）
            try (Statement stmt = conn.createStatement()) {
                String[] statements = sqlContent.split(";");


                for (int i = 0; i < statements.length; i++) {
                    String statement = statements[i].trim();

                    if (!statement.isEmpty()) {
                        try {
                            stmt.execute(statement);
                        } catch (SQLException e) {
                            System.err.println("执行SQL语句失败 [" + (i+1) + "]: " + statement.substring(0, Math.min(200, statement.length())));
                            System.err.println("错误信息: " + e.getMessage());
                            // 继续执行其他语句，不中断整个过程
                        }
                    }
                }
            } catch (SQLException e) {
                System.err.println("H2数据库表创建失败: " + e.getMessage());
                throw e;
            }
        }
    }



    private void executeSchemaScript() throws Exception {
        ClassPathResource resource = new ClassPathResource("sql/schema.sql");

        // 构建支持多语句执行的数据库连接URL
        String multiStatementUrl = datasourceUrl;
        if (!multiStatementUrl.contains("allowMultiQueries")) {
            multiStatementUrl += (multiStatementUrl.contains("?") ? "&" : "?") + "allowMultiQueries=true";
        }

        try (InputStream inputStream = resource.getInputStream();
             Connection conn = DriverManager.getConnection(multiStatementUrl, username, password)) {

            // 读取整个SQL文件内容，保持原样不做任何修改
            String sqlContent = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);

            // 一次性执行整个SQL脚本文件
            try (Statement stmt = conn.createStatement()) {
                stmt.execute(sqlContent);
            } catch (SQLException e) {
                // 如果一次性执行失败，记录错误但不中断程序
            }
        }
    }
} 