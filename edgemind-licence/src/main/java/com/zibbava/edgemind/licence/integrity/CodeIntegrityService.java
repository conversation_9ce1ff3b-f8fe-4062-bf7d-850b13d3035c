package com.zibbava.edgemind.licence.integrity;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 代码完整性检测服务
 * 自动检测edgemind-cortex模块的所有.class文件是否被篡改
 */
@Component
@Order(2)
@Slf4j
public class CodeIntegrityService implements CommandLineRunner {

    private static final String INTEGRITY_PROPS_FILE = "/integrity.properties";
    private static final String CORTEX_CLASSES_PATH = "com/zibbava/edgemind/cortex/**/*.class";

    private static final boolean INTEGRITY_CHECK_ENABLED = true;
    private static final long CHECK_INTERVAL_SECONDS = 3600; // 生产环境建议延长检测间隔
    private static final boolean STRICT_MODE = true;

    private final Map<String, String> expectedHashes = new HashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private volatile boolean systemCompromised = false;

    @Override
    public void run(String... args) throws Exception {
        if (!INTEGRITY_CHECK_ENABLED) {
            log.info("代码完整性检测已禁用。");
            return;
        }

        // 在开发环境下，完全跳过完整性检查
        if (isDevelopmentEnvironment()) {
            log.info("\n===============================================================\n" +
                    "== DEV MODE: Integrity check is SKIPPED.                     ==\n" +
                    "===============================================================");
            return;
        }

        log.info("启动生产环境代码完整性自动检测服务...");
        try {
            boolean hashesAvailable = loadExpectedHashes();

            if (!hashesAvailable) {
                // 在生产环境下，文件不存在是致命错误
                throw new IOException("无法在类路径中找到 " + INTEGRITY_PROPS_FILE + "。系统可能已被篡改。");
            }

            performIntegrityCheck();
            startPeriodicCheck();
            log.info("代码完整性检测服务已成功启动。");
        } catch (Exception e) {
            System.err.println("代码完整性检测服务启动失败: " + e.getMessage());
            if (STRICT_MODE) {
                shutdownSystem("初始化失败", e);
            }
        }
    }

    /**
     * 通过检查代码运行来源来判断是否为开发环境。
     *
     * @return 如果代码从文件系统（而非JAR包）运行，则为true。
     */
    private boolean isDevelopmentEnvironment() {
        URL codeSourceUrl = getClass().getProtectionDomain().getCodeSource().getLocation();
        return "file".equals(codeSourceUrl.getProtocol());
    }

    private boolean loadExpectedHashes() throws IOException {
        Properties props = new Properties();
        try (InputStream is = CodeIntegrityService.class.getResourceAsStream(INTEGRITY_PROPS_FILE)) {
            if (is == null) {
                return false; // 表示文件未找到
            }
            props.load(is);
        }
        for (String key : props.stringPropertyNames()) {
            expectedHashes.put(key, props.getProperty(key));
        }
        if (!expectedHashes.isEmpty()) {
            log.info("已加载 " + expectedHashes.size() + " 个文件的完整性校验信息。");
        }
        return !expectedHashes.isEmpty();
    }

    public void performIntegrityCheck() {
        if (systemCompromised) {
            System.err.println("系统已被标记为受损，拒绝继续运行。");
            return;
        }

        log.info("开始执行代码完整性检测 (基于运行时扫描)...");
        AtomicInteger checkedFilesCount = new AtomicInteger(0);

        try {
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resolver.getResources("classpath*:" + CORTEX_CLASSES_PATH);

            if (resources.length == 0) {
                handleIntegrityViolation("在classpath中未找到任何cortex模块的class文件", null);
                return;
            }

            // 新增：将运行时扫描到的资源转换为路径集合，以便于比较
            Map<String, Resource> actualPathToResourceMap = new HashMap<>();
            for (Resource resource : resources) {
                try {
                    String resourcePath = getResourcePath(resource);
                    actualPathToResourceMap.put(resourcePath, resource);
                } catch (IOException e) {
                    log.warn("无法从 {} 获取资源路径, 将跳过此文件的校验: {}", resource.getURL(), e.getMessage());
                }
            }
            Set<String> actualPaths = actualPathToResourceMap.keySet();
            Set<String> expectedPaths = expectedHashes.keySet();

            log.info("完整性校验初步报告: 预期文件数={}, 实际扫描到文件数={}", expectedPaths.size(), actualPaths.size());
            if (log.isDebugEnabled()) {
                log.debug(">>> 预期文件列表 ({}):\n{}", expectedPaths.size(), String.join("\n", expectedPaths.stream().sorted().collect(Collectors.toList())));
                log.debug(">>> 实际扫描文件列表 ({}):\n{}", actualPaths.size(), String.join("\n", actualPaths.stream().sorted().collect(Collectors.toList())));
            }

            // 检查文件数量或内容是否一致，如果不一致，则提供详细的差异报告
            if (expectedPaths.size() != actualPaths.size() || !expectedPaths.containsAll(actualPaths)) {
                Set<String> missingFiles = new HashSet<>(expectedPaths);
                missingFiles.removeAll(actualPaths);

                Set<String> extraFiles = new HashSet<>(actualPaths);
                extraFiles.removeAll(expectedPaths);

                StringBuilder errorMessage = new StringBuilder();
                errorMessage.append(String.format("文件数量或内容不匹配！预期 %d 个, 实际 %d 个。", expectedPaths.size(), actualPaths.size()));

                if (!missingFiles.isEmpty()) {
                    errorMessage.append("\n错误详情: 以下文件在预期中，但运行时未找到 (可能被删除):\n - ");
                    errorMessage.append(String.join("\n - ", missingFiles));
                }

                if (!extraFiles.isEmpty()) {
                    errorMessage.append("\n错误详情: 以下文件在运行时被扫描到，但不在预期中 (可能是未授权的新增文件):\n - ");
                    errorMessage.append(String.join("\n - ", extraFiles));
                }

                handleIntegrityViolation(errorMessage.toString(), null);
                return;
            }


            log.info("文件数量和名称校验通过。扫描到 " + resources.length + " 个文件，与预期一致。");


            for (Resource resource : resources) {
                String resourcePath = getResourcePath(resource);

                if (!expectedHashes.containsKey(resourcePath)) {
                    handleIntegrityViolation("文件 '" + resourcePath + "' 未在哈希记录中找到，integrity.properties可能已被篡改。", null);
                    return;
                }

                String expectedHash = expectedHashes.get(resourcePath);

                try (InputStream is = resource.getInputStream()) {
                    if (is == null) {
                        handleIntegrityViolation("文件丢失: " + resourcePath, null);
                        return; // Should not happen with this approach, but for safety
                    }
                    String actualHash = HashGenerator.calculateStreamHash(is);
                    if (!expectedHash.equals(actualHash)) {
                        handleIntegrityViolation("文件被篡改: " + resourcePath, null);
                        return;
                    }
                    checkedFilesCount.incrementAndGet();
                }
            }

            log.info("代码完整性检测通过。成功校验了 " + checkedFilesCount.get() + " 个文件。");

        } catch (IOException e) {
            handleIntegrityViolation("完整性检测过程中发生IO错误", e);
        } catch (Exception e) {
            handleIntegrityViolation("完整性检测过程中发生未知错误", e);
        }
    }

    private String getResourcePath(Resource resource) throws IOException {
        String fullPath = resource.getURL().getPath();
        // We need the path relative to the classpath root.
        // Example: fullPath might be /path/to/app.jar!/BOOT-INF/classes!/com/zibbava/edgemind/cortex/manager/DockerManager.class
        // We need to extract "com/zibbava/edgemind/cortex/manager/DockerManager.class"
        String[] parts = fullPath.split("!/");
        for (int i = parts.length - 1; i >= 0; i--) {
            if (parts[i].startsWith("com/zibbava/edgemind/cortex")) {
                return parts[i];
            }
        }
        // Fallback for file-based resources during testing or unexpected structures
        if (fullPath.contains("/classes/")) {
            return fullPath.substring(fullPath.lastIndexOf("/classes/") + "/classes/".length());
        }
        throw new IOException("无法从资源URL中提取相对路径: " + fullPath);
    }

    private void handleIntegrityViolation(String message, Exception e) {
        systemCompromised = true;
        System.err.println("=== 安全警告: " + message + " ===");
        if (e != null) {
            System.err.println("错误详情: " + e.getMessage());
        }
        System.err.println("为了系统安全，应用将停止运行。请重新部署原始代码。");
        if (STRICT_MODE) {
            shutdownSystem(message, e);
        }
    }

    private void shutdownSystem(String reason, Throwable cause) {
        System.err.println("系统安全检测失败，正在关闭应用... 原因: " + reason);
        scheduler.shutdownNow();
        // 使用非0状态码退出，并抛出异常，以便外部脚本可以捕获
        new Thread(() -> System.exit(1)).start();
        throw new SecurityException("系统完整性受损: " + reason, cause);
    }

    private void startPeriodicCheck() {
        scheduler.scheduleAtFixedRate(this::performIntegrityCheck, CHECK_INTERVAL_SECONDS, CHECK_INTERVAL_SECONDS, TimeUnit.SECONDS);
        log.info("已启动定期完整性检测，间隔: " + CHECK_INTERVAL_SECONDS + " 秒。");
    }

}