package com.zibbava.edgemind.cortex.config;

import cn.hutool.system.SystemUtil;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.inmemory.InMemoryEmbeddingStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import com.zibbava.edgemind.cortex.store.LuceneEmbeddingStore;

/**
 * Apache Lucene 9.x 向量数据库配置类
 *
 * 🚀 核心特性：
 * - 基于Apache Lucene 9.x，完全嵌入式部署
 * - 支持向量检索和混合检索（向量+关键词）
 * - 优化的中文分词和搜索支持
 * - 高性能的HNSW向量索引
 * - 完善的条件过滤查询
 * - 数据持久化存储到本地文件系统
 * - 轻量级，资源消耗低
 * - 无需外部服务依赖
 *
 * 🎯 优势对比Chroma：
 * - 更成熟稳定的搜索引擎技术
 * - 更强大的文本搜索和分析能力
 * - 更好的中文支持和分词
 * - 更灵活的查询和过滤功能
 * - 更高的性能和可扩展性
 * - 完全开源，无供应商锁定
 */
@Configuration
@Slf4j
public class LuceneVectorStoreConfig {

    // --- Lucene 基础配置 ---
    @Value("${lucene.index.path:./data/lucene-index}")
    private String indexPath;

    @Value("${lucene.dimension:512}")
    private Integer dimension;

    @Value("${lucene.batch.size:100}")
    private Integer batchSize;

    // --- 混合搜索配置 ---
    @Value("${vector.hybrid.enabled:true}")
    private boolean hybridSearchEnabled;

    @Value("${lucene.hybrid.alpha:0.7}")
    private float hybridAlpha;

    // --- 向量存储类型配置 ---
    @Value("${lucene.store.type:lucene}")
    private String storeType;

    /**
     * 创建向量数据库Bean
     *
     * 🔧 支持多种向量存储类型：
     * - lucene: Apache Lucene向量存储（推荐）
     * - memory: 内存向量存储（测试用）
     *
     * 🔧 生命周期管理：
     * - 完全内嵌式，无需外部服务
     * - 支持混合搜索
     * - 数据自动持久化
     * - 与Chroma功能完全一致，性能更优
     */
    @Bean
    @Primary
    public EmbeddingStore<TextSegment> embeddingStore() {
        // 根据配置选择向量存储类型
        if ("memory".equalsIgnoreCase(storeType)) {
            log.info("🧠 使用内存向量存储（测试模式）");
            log.warn("⚠️ 内存存储重启后数据会丢失，仅适用于测试环境");
            return new InMemoryEmbeddingStore<>();
        }

        try {
            log.info("🚀 初始化Apache Lucene 9.x向量数据库");
            log.info("🖥️ 操作系统: {}", SystemUtil.getOsInfo().getName());
            log.info("📁 索引路径: {}", indexPath);
            log.info("📊 向量维度: {}", dimension);
            log.info("📦 批处理大小: {}", batchSize);
            log.info("🔍 混合搜索: {}", hybridSearchEnabled ? "启用" : "禁用");
            log.info("⚖️ 混合搜索权重: {}", hybridAlpha);

            // 创建Lucene向量存储
            LuceneEmbeddingStore store = new LuceneEmbeddingStore(
                indexPath,
                dimension,
                batchSize,
                hybridSearchEnabled,
                hybridAlpha
            );

            log.info("✅ Apache Lucene向量数据库初始化成功");
            return store;

        } catch (Exception e) {
            log.error("❌ 初始化Lucene向量数据库失败", e);
            log.warn("🔄 回退到内存向量存储");
            return new InMemoryEmbeddingStore<>();
        }
    }

    /**
     * 获取索引路径
     */
    public String getIndexPath() {
        return indexPath;
    }

    /**
     * 获取向量维度
     */
    public Integer getDimension() {
        return dimension;
    }

    /**
     * 获取批处理大小
     */
    public Integer getBatchSize() {
        return batchSize;
    }

    /**
     * 是否启用混合搜索
     */
    public boolean isHybridSearchEnabled() {
        return hybridSearchEnabled;
    }

    /**
     * 获取混合搜索权重
     */
    public float getHybridAlpha() {
        return hybridAlpha;
    }

    /**
     * 获取存储类型
     */
    public String getStoreType() {
        return storeType;
    }
}
