package com.zibbava.edgemind.cortex.enums;

import lombok.Getter;

/**
 * 模型信息枚举类
 * 定义了各种AI模型的基本信息和支持的功能
 */
@Getter
public enum ModelInfo {
    // Qwen系列 - 阿里云通义千问系列模型
    QWEN3_8B("qwen3:8b", "通义千问3 8B", "阿里云通义千问3的8B模型，支持40K上下文窗口，5.2GB大小。适合8GB显存以上显卡。", 5.2, "llm", true, false),
    QWEN3_4B("qwen3:4b", "通义千问3 4B", "阿里云通义千问3系列4B轻量版，平衡性能与资源需求。适合4GB显存以上显卡。", 2.6, "llm", true, false),
    QWEN3_1_7B("qwen3:1.7b", "通义千问3 1.7B", "通义千问3超轻量版，仅需1.4GB存储空间，适合低端设备使用。适合2GB显存显卡。", 1.4, "llm", true, false),
    QWEN3_14B("qwen3:14b", "通义千问3 14B", "通义千问3大型版本，提供更强的理解和生成能力。需要16GB以上显存。", 9.3, "llm", true, false),
    QWEN3_0_6B("qwen3:0.6b", "通义千问3 0.6B", "通义千问3的极轻量版本，仅523MB大小，适合边缘设备和移动应用。", 0.52, "llm", true, false),
    QWEN3_30B("qwen3:30b", "通义千问3 30B", "通义千问3的大型版本，19GB大小，提供接近商业模型的性能，需要高端显卡。", 19.0, "llm", true, false),

    // Llama3系列 - Meta最新大语言模型系列
    LLAMA3_2_3B("llama3.2:3b", "Llama 3.2 3B", "Meta的Llama 3.2系列3B小型模型，2.0GB大小，在资源受限环境中也能提供良好性能。适合4GB显存显卡。", 2.0, "llm", false, false),
    LLAMA3_2_1B("llama3.2:1b", "Llama 3.2 1B", "Meta的Llama 3.2系列超轻量模型，1.3GB大小，适合边缘设备和移动应用。适合2GB显存显卡。", 1.3, "llm", false, false),
    LLAMA3_1_8B("llama3.1:8b", "Llama 3.1 8B", "Meta的Llama 3.1系列8B模型，提供出色的多语言能力，4.9GB大小。适合8GB显存显卡。", 4.9, "llm", false, false),
    LLAMA3_1_70B("llama3.1:70b", "Llama 3.1 70B", "Meta的Llama 3.1旗舰版，43GB大小，性能强大但需要专业级GPU。需要至少32GB显存的专业显卡。", 43.0, "llm", false, false),

    // DeepSeek系列 - 擅长代码和推理的模型
    DEEPSEEK_R1_7B("deepseek-r1:7b", "DeepSeek R1 7B", "DeepSeek推出的高性能模型，擅长代码生成和推理，4.7GB大小。适合8GB显存显卡。", 4.7, "llm", true, false),
    DEEPSEEK_R1_1_5B("deepseek-r1:1.5b", "DeepSeek R1 1.5B", "DeepSeek R1系列轻量版模型，仅1.1GB大小，适合资源受限环境。适合2GB显存显卡。", 1.1, "llm", true, false),
    DEEPSEEK_R1_8B("deepseek-r1:8b", "DeepSeek R1 8B", "DeepSeek R1的增强版模型，4.9GB大小，提供更强的理解能力。适合8GB显存显卡。", 4.9, "llm", true, false),
    DEEPSEEK_R1_14B("deepseek-r1:14b", "DeepSeek R1 14B", "DeepSeek R1的大型版本，9.0GB大小，提供卓越的代码生成性能。需要12GB以上显存。", 9.0, "llm", true, false),
    DEEPSEEK_R1_32B("deepseek-r1:32b", "DeepSeek R1 32B", "DeepSeek R1的超大规模版本，20GB大小，性能强大但需要高端硬件支持。需要至少32GB显存。", 20.0, "llm", true, false),

    // Gemma系列 - Google开源系列模型
    GEMMA3_4B("gemma3:4b", "Gemma 3 4B", "Google的Gemma 3系列4B模型，支持多模态输入，3.3GB大小。适合6GB显存显卡。", 3.3, "llm", false, true),
    GEMMA3_1B("gemma3:1b", "Gemma 3 1B", "Google的Gemma 3系列超轻量版，仅815MB大小，适合边缘设备。适合2GB显存显卡。", 0.82, "llm", false, true),
    GEMMA3_12B("gemma3:12b", "Gemma 3 12B", "Google的Gemma 3中大型模型，8.1GB大小，在中等硬件上提供优秀性能。需要12GB以上显存。", 8.1, "llm", false, true),
    GEMMA3_27B("gemma3:27b", "Gemma 3 27B", "Google的Gemma 3大型模型，17GB大小，性能优异但需要高端显卡。需要24GB以上显存。", 17.0, "llm", false, true),

    // Phi系列 - Microsoft高效模型
    PHI4_14B("phi4:14b", "Phi-4 14B", "微软的Phi-4大型模型，9.1GB大小，在小型硬件上提供接近大模型的性能。需要16GB以上显存。", 9.1, "llm", false, false);

    // 嵌入模型已改为本地BGE中文模型，无需通过Ollama下载

    private final String id;
    private final String name;
    private final String description;
    private final double size;
    private final String type;
    private final boolean supportsThinking;  // 是否支持think功能
    private final boolean supportsMultimodal;  // 是否支持多模态

    ModelInfo(String id, String name, String description, double size, String type,
              boolean supportsThinking, boolean supportsMultimodal) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.size = size;
        this.type = type;
        this.supportsThinking = supportsThinking;
        this.supportsMultimodal = supportsMultimodal;
    }

    /**
     * 检查模型是否为必需模型
     * @return 是否为必需模型
     */
    public boolean isRequired() {
        return false; // 所有模型都不再是必需的
    }

    /**
     * 根据模型ID查找模型信息
     * @param modelId 模型ID
     * @return 模型信息，如果未找到返回null
     */
    public static ModelInfo findByModelId(String modelId) {
        if (modelId == null) {
            return null;
        }
        
        for (ModelInfo model : values()) {
            if (model.getId().equalsIgnoreCase(modelId)) {
                return model;
            }
        }
        return null;
    }

    /**
     * 检查指定模型是否支持think功能
     * @param modelName 模型名称
     * @return 是否支持think功能
     */
    public static boolean supportsThinking(String modelName) {
        if (modelName == null) {
            return false;
        }
        
        for (ModelInfo model : values()) {
            if (modelName.toLowerCase().contains(model.getId().toLowerCase()) || 
                modelName.toLowerCase().contains(model.getName().toLowerCase())) {
                return model.isSupportsThinking();
            }
        }
        return false;
    }

    /**
     * 检查指定模型是否支持多模态功能
     * @param modelName 模型名称
     * @return 是否支持多模态功能
     */
    public static boolean supportsMultimodal(String modelName) {
        if (modelName == null) {
            return false;
        }
        
        for (ModelInfo model : values()) {
            if (modelName.toLowerCase().contains(model.getId().toLowerCase()) || 
                modelName.toLowerCase().contains(model.getName().toLowerCase())) {
                return model.isSupportsMultimodal();
            }
        }
        return false;
    }
}