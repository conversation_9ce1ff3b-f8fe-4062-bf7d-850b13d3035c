package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 权限模板实体类
 * 用于动态生成权限
 * 
 * <AUTHOR>
 */
@Data
@TableName("sys_permission_template")
public class PermissionTemplate {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("template_name")
    private String templateName; // 模板名称

    @TableField("template_code")
    private String templateCode; // 模板编码，支持占位符如{space_id}

    @TableField("permission_type")
    private String permissionType; // 权限类型

    @TableField("resource_type")
    private String resourceType; // 资源类型

    @TableField("description")
    private String description; // 描述

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
