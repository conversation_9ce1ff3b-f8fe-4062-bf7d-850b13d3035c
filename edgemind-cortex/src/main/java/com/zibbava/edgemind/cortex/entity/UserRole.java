package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_user_role") // 对应数据库中间表名
public class UserRole {

    // 注意: 中间表通常不需要自增 ID，使用联合主键 (user_id, role_id)
    // Mybatis Plus 对联合主键的支持相对复杂，这里仅定义字段
    // 可以在数据库层面设置联合主键

    @TableField("user_id")
    private Long userId;

    @TableField("role_id")
    private Long roleId;

} 