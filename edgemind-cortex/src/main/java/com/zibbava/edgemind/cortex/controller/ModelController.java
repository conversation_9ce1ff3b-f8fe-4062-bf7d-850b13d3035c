package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.zibbava.edgemind.cortex.service.ModelService;
import com.zibbava.edgemind.cortex.service.RemoteModelIntegrationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 模型控制器，提供模型信息相关的API
 */
@RestController
@RequestMapping("/api")
@Slf4j
public class ModelController {
    
    @Autowired
    private ModelService modelService;

    @Autowired
    private RemoteModelIntegrationService remoteModelIntegrationService;
    
    /**
     * 获取所有可用的模型列表（包含本地和远程模型）
     * @return 模型列表，包含模型ID和名称
     */
    @SaIgnore
    @GetMapping("/models")
    public ResponseEntity<?> getModels() {
        List<Map<String, String>> allModels = new ArrayList<>();

        // 获取本地模型
        List<Map<String, String>> localModels = modelService.getModelsForFrontend();
        allModels.addAll(localModels);
        log.debug("🏠 本地推理模型数量: {}", localModels.size());

        // 获取远程模型
        List<Map<String, Object>> remoteModelInfos = remoteModelIntegrationService.getAvailableModelInfos();
        log.debug("🌐 云端API模型信息数量: {}", remoteModelInfos.size());

        for (Map<String, Object> remoteInfo : remoteModelInfos) {
            Map<String, String> modelMap = new HashMap<>();
            modelMap.put("value", (String) remoteInfo.get("id"));
            modelMap.put("name", remoteInfo.get("name") + " (云端)");
            allModels.add(modelMap);
            log.debug("🌐 添加云端API模型: id={}, name={}", remoteInfo.get("id"), remoteInfo.get("name"));
        }

        log.debug("📋 总模型数量: {} (本地推理: {}, 云端API: {})", allModels.size(), localModels.size(), remoteModelInfos.size());
        return ResponseEntity.ok(allModels);
    }

    /**
     * 获取本地推理模型列表
     * @return 本地模型列表
     */
    @SaIgnore
    @GetMapping("/models/local")
    public ResponseEntity<?> getLocalModels() {
        List<Map<String, String>> localModels = modelService.getModelsForFrontend();
        log.debug("🏠 获取本地推理模型: {} 个", localModels.size());
        return ResponseEntity.ok(localModels);
    }

    /**
     * 获取云端API模型列表
     * @return 远程模型列表
     */
    @SaIgnore
    @GetMapping("/models/remote")
    public ResponseEntity<?> getRemoteModels() {
        List<Map<String, Object>> remoteModelInfos = remoteModelIntegrationService.getAvailableModelInfos();

        List<Map<String, String>> remoteModels = new ArrayList<>();
        for (Map<String, Object> remoteInfo : remoteModelInfos) {
            Map<String, String> modelMap = new HashMap<>();
            modelMap.put("value", (String) remoteInfo.get("id"));
            modelMap.put("name", (String) remoteInfo.get("name"));
            remoteModels.add(modelMap);
        }

        log.debug("🌐 获取云端API模型: {} 个", remoteModels.size());
        return ResponseEntity.ok(remoteModels);
    }
} 