package com.zibbava.edgemind.cortex.model.ollama;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * Ollama模型详情
 */
@Data
@Builder
public class OllamaModelDetails {
    private String modelName;
    private String license;
    private String modelfile;
    private Map<String, Object> parameters;
    private String template;
    private ModelType modelType; // 模型类型枚举

    /**
     * 模型类型枚举
     */
    public enum ModelType {
        LLM,        // 大语言模型
        EMBEDDING,  // 嵌入向量模型
        UNKNOWN     // 未知类型
    }
} 