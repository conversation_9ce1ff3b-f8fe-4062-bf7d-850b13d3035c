package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 知识空间角色关联实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("kb_space_roles")
public class KnowledgeSpaceRole {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("space_id")
    private String spaceId;
    
    @TableField("role_id")
    private Long roleId;
    
    @TableField("permission_level")
    private PermissionLevel permissionLevel;
    
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    // 关联对象
    @TableField(exist = false)
    private Role role;
    
    @TableField(exist = false)
    private KnowledgeSpace space;
    
    /**
     * 权限级别枚举
     */
    public enum PermissionLevel {
        read, write, admin
    }
}
