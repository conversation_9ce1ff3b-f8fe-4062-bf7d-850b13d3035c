package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zibbava.edgemind.cortex.dto.ApiResponse;
import com.zibbava.edgemind.cortex.entity.Permission;
import com.zibbava.edgemind.cortex.service.PermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 权限管理控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/system/permission")
@RequiredArgsConstructor
@Validated
public class PermissionController {

    private final PermissionService permissionService;

    /**
     * 获取权限树
     */
    @GetMapping("/tree")
    @SaCheckPermission("permission:manage:list")
    public ResponseEntity<ApiResponse<List<Permission>>> getPermissionTree() {
        List<Permission> result = permissionService.getPermissionTree();
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 获取所有权限列表
     */
    @GetMapping("/list")
    @SaCheckPermission("permission:manage:list")
    public ResponseEntity<ApiResponse<List<Permission>>> getAllPermissions() {
        List<Permission> result = permissionService.list();
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 根据ID获取权限详情
     */
    @GetMapping("/{permissionId}")
    @SaCheckPermission("permission:manage:list")
    public ResponseEntity<ApiResponse<Permission>> getPermissionById(@PathVariable Long permissionId) {
        Permission result = permissionService.getById(permissionId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 创建权限
     */
    @PostMapping
    @SaCheckPermission("permission:manage:create")
    public ResponseEntity<ApiResponse<Void>> createPermission(@Valid @RequestBody Permission permission) {
        permissionService.save(permission);
        return ResponseEntity.ok(ApiResponse.success("权限创建成功"));
    }

    /**
     * 更新权限信息
     */
    @PutMapping("/{permissionId}")
    @SaCheckPermission("permission:manage:update")
    public ResponseEntity<ApiResponse<Void>> updatePermission(@PathVariable Long permissionId,
                                                             @Valid @RequestBody Permission permission) {
        permission.setId(permissionId);
        permissionService.updateById(permission);
        return ResponseEntity.ok(ApiResponse.success("权限信息更新成功"));
    }

    /**
     * 删除权限
     */
    @DeleteMapping("/{permissionId}")
    @SaCheckPermission("permission:manage:delete")
    public ResponseEntity<ApiResponse<Void>> deletePermission(@PathVariable Long permissionId) {
        permissionService.removeById(permissionId);
        return ResponseEntity.ok(ApiResponse.success("权限删除成功"));
    }

    /**
     * 根据角色ID列表获取权限
     */
    @PostMapping("/by-roles")
    @SaCheckPermission("permission:manage:list")
    public ResponseEntity<ApiResponse<List<Permission>>> getPermissionsByRoleIds(@RequestBody List<Long> roleIds) {
        List<Permission> result = permissionService.findPermissionsByRoleIds(roleIds);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }
}
