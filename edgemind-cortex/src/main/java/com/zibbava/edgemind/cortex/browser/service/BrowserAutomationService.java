package com.zibbava.edgemind.cortex.browser.service;

import com.zibbava.edgemind.cortex.browser.dto.BrowserToolRequest;
import com.zibbava.edgemind.cortex.browser.dto.BrowserToolResponse;
import com.zibbava.edgemind.cortex.browser.websocket.WebSocketManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 浏览器自动化服务
 * 提供浏览器自动化的核心业务逻辑和统计功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BrowserAutomationService {
    
    private final WebSocketManager webSocketManager;
    
    // 统计信息
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong successfulRequests = new AtomicLong(0);
    private final AtomicLong failedRequests = new AtomicLong(0);
    private final AtomicLong timeoutRequests = new AtomicLong(0);
    
    // 工具使用统计
    private final Map<String, AtomicLong> toolUsageStats = new ConcurrentHashMap<>();
    
    // 用户使用统计
    private final Map<Long, AtomicLong> userUsageStats = new ConcurrentHashMap<>();
    
    /**
     * 执行浏览器工具
     */
    public Mono<BrowserToolResponse> executeTool(String toolName, String arguments, Long userId, Long conversationId) {
        // 更新统计信息
        totalRequests.incrementAndGet();
        toolUsageStats.computeIfAbsent(toolName, k -> new AtomicLong(0)).incrementAndGet();
        if (userId != null) {
            userUsageStats.computeIfAbsent(userId, k -> new AtomicLong(0)).incrementAndGet();
        }
        
        log.info("🔧 执行浏览器工具: toolName={}, userId={}, conversationId={}", 
                toolName, userId, conversationId);
        
        // 创建工具请求
        BrowserToolRequest request = BrowserToolRequest.create(toolName, arguments, userId, conversationId);
        
        // 通过WebSocket管理器发送请求
        return webSocketManager.sendToolRequest(request)
                .timeout(Duration.ofSeconds(30))
                .doOnSuccess(response -> {
                    if (response.isSuccess()) {
                        successfulRequests.incrementAndGet();
                        log.info("✅ 工具执行成功: toolName={}, requestId={}, executionTime={}ms", 
                                toolName, request.getRequestId(), response.getExecutionTimeMs());
                    } else {
                        failedRequests.incrementAndGet();
                        log.warn("❌ 工具执行失败: toolName={}, requestId={}, error={}", 
                                toolName, request.getRequestId(), response.getError());
                    }
                })
                .doOnError(error -> {
                    if (error instanceof java.util.concurrent.TimeoutException) {
                        timeoutRequests.incrementAndGet();
                        log.error("⏰ 工具执行超时: toolName={}, requestId={}", 
                                toolName, request.getRequestId());
                    } else {
                        failedRequests.incrementAndGet();
                        log.error("❌ 工具执行异常: toolName={}, requestId={}, error={}", 
                                toolName, request.getRequestId(), error.getMessage(), error);
                    }
                })
                .onErrorReturn(BrowserToolResponse.error(request.getRequestId(), "工具执行失败"));
    }
    
    /**
     * 检查浏览器插件连接状态
     */
    public boolean isPluginConnected() {
        return webSocketManager.getActiveSessionCount() > 0;
    }
    
    /**
     * 获取连接的浏览器插件数量
     */
    public int getConnectedPluginCount() {
        return webSocketManager.getActiveSessionCount();
    }
    
    /**
     * 获取待处理请求数量
     */
    public int getPendingRequestCount() {
        return webSocketManager.getPendingRequestCount();
    }
    
    /**
     * 获取系统统计信息
     */
    public Map<String, Object> getSystemStats() {
        return Map.of(
            "totalRequests", totalRequests.get(),
            "successfulRequests", successfulRequests.get(),
            "failedRequests", failedRequests.get(),
            "timeoutRequests", timeoutRequests.get(),
            "successRate", calculateSuccessRate(),
            "connectedPlugins", getConnectedPluginCount(),
            "pendingRequests", getPendingRequestCount(),
            "toolUsageStats", getToolUsageStats(),
            "topUsers", getTopUsers(10)
        );
    }
    
    /**
     * 获取工具使用统计
     */
    public Map<String, Long> getToolUsageStats() {
        Map<String, Long> stats = new ConcurrentHashMap<>();
        toolUsageStats.forEach((tool, count) -> stats.put(tool, count.get()));
        return stats;
    }
    
    /**
     * 获取用户使用排行榜
     */
    public Map<Long, Long> getTopUsers(int limit) {
        return userUsageStats.entrySet().stream()
                .sorted(Map.Entry.<Long, AtomicLong>comparingByValue((a, b) -> Long.compare(b.get(), a.get())))
                .limit(limit)
                .collect(java.util.stream.Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> entry.getValue().get(),
                    (e1, e2) -> e1,
                    java.util.LinkedHashMap::new
                ));
    }
    
    /**
     * 计算成功率
     */
    private double calculateSuccessRate() {
        long total = totalRequests.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) successfulRequests.get() / total * 100.0;
    }
    
    /**
     * 重置统计信息
     */
    public void resetStats() {
        totalRequests.set(0);
        successfulRequests.set(0);
        failedRequests.set(0);
        timeoutRequests.set(0);
        toolUsageStats.clear();
        userUsageStats.clear();
        
        log.info("📊 浏览器自动化统计信息已重置");
    }
    
    /**
     * 获取最受欢迎的工具
     */
    public String getMostPopularTool() {
        return toolUsageStats.entrySet().stream()
                .max(Map.Entry.comparingByValue((a, b) -> Long.compare(a.get(), b.get())))
                .map(Map.Entry::getKey)
                .orElse("无");
    }
    
    /**
     * 获取系统健康状态
     */
    public Map<String, Object> getHealthStatus() {
        boolean isHealthy = isPluginConnected() && calculateSuccessRate() > 80.0;
        
        return Map.of(
            "healthy", isHealthy,
            "pluginConnected", isPluginConnected(),
            "connectedPlugins", getConnectedPluginCount(),
            "successRate", calculateSuccessRate(),
            "pendingRequests", getPendingRequestCount(),
            "status", isHealthy ? "正常" : "异常",
            "timestamp", System.currentTimeMillis()
        );
    }
    
    /**
     * 执行系统诊断
     */
    public Map<String, Object> runDiagnostics() {
        Map<String, Object> diagnostics = new ConcurrentHashMap<>();
        
        // 检查WebSocket连接
        boolean hasConnections = isPluginConnected();
        diagnostics.put("websocket_connections", Map.of(
            "status", hasConnections ? "正常" : "异常",
            "count", getConnectedPluginCount(),
            "message", hasConnections ? "浏览器插件已连接" : "没有浏览器插件连接"
        ));
        
        // 检查请求处理能力
        int pendingCount = getPendingRequestCount();
        boolean requestsHealthy = pendingCount < 10; // 假设超过10个待处理请求就认为异常
        diagnostics.put("request_processing", Map.of(
            "status", requestsHealthy ? "正常" : "异常",
            "pending_count", pendingCount,
            "message", requestsHealthy ? "请求处理正常" : "待处理请求过多"
        ));
        
        // 检查成功率
        double successRate = calculateSuccessRate();
        boolean successRateHealthy = successRate > 80.0 || totalRequests.get() < 10;
        diagnostics.put("success_rate", Map.of(
            "status", successRateHealthy ? "正常" : "异常",
            "rate", successRate,
            "total_requests", totalRequests.get(),
            "message", successRateHealthy ? "成功率正常" : "成功率偏低"
        ));
        
        // 整体健康状态
        boolean overallHealthy = hasConnections && requestsHealthy && successRateHealthy;
        diagnostics.put("overall", Map.of(
            "status", overallHealthy ? "正常" : "异常",
            "message", overallHealthy ? "系统运行正常" : "系统存在问题",
            "timestamp", System.currentTimeMillis()
        ));
        
        return diagnostics;
    }
}
