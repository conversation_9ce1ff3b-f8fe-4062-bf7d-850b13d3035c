package com.zibbava.edgemind.cortex.service.strategy.handler;

import com.zibbava.edgemind.cortex.browser.tools.BrowserToolProvider;
import com.zibbava.edgemind.cortex.dto.ChatContext;
import com.zibbava.edgemind.cortex.service.ChatService;
import com.zibbava.edgemind.cortex.service.streaming.StreamingResponseFormatter;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.model.chat.response.ChatResponse;
import dev.langchain4j.model.chat.response.StreamingChatResponseHandler;
import dev.langchain4j.service.AiServices;
import dev.langchain4j.service.TokenStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.SynchronousSink;
import reactor.util.context.Context;

import java.util.ArrayList;
import java.util.List;

/**
 * 远程模型聊天处理器
 * 负责处理远程模型的聊天请求，支持工具调用功能
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RemoteModelChatHandler {

    private final ChatService chatService;
    private final BrowserToolProvider browserToolProvider;
    private final StreamingResponseFormatter responseFormatter;

    /**
     * 远程聊天助手接口
     */
    public interface RemoteChatAssistant {
        TokenStream chat(String message);
    }

    /**
     * 执行远程模型聊天
     * @param context 聊天上下文
     * @param remoteStreamingModel 远程流式模型
     * @return 聊天结果流
     */
    public Flux<String> executeChat(ChatContext context, StreamingChatModel remoteStreamingModel) {
        try {
            // 设置工具调用状态到ThreadLocal
            BrowserToolProvider.setToolCallingEnabled(
                context.getEnableToolCalling() != null && context.getEnableToolCalling()
            );

            // 检查是否启用工具调用
            if (context.getEnableToolCalling() != null && context.getEnableToolCalling()) {
                return executeRemoteStreamingChatWithTools(context, remoteStreamingModel);
            } else {
                return executeRemoteStreamingChatSimple(context, remoteStreamingModel);
            }
        } catch (Exception e) {
            BrowserToolProvider.clearToolCallingState(); // 清理ThreadLocal状态
            log.error("❌ 执行远程模型对话出错: {}", e.getMessage(), e);
            return Flux.error(e);
        }
    }

    /**
     * 执行带工具调用的远程流式聊天
     */
    private Flux<String> executeRemoteStreamingChatWithTools(ChatContext context, StreamingChatModel remoteStreamingModel) {
        try {
            // 构建消息历史
            List<ChatMessage> messages = buildChatMessageHistory(context);

            // 使用AiServices构建支持工具调用的助手
            RemoteChatAssistant assistant = AiServices.builder(RemoteChatAssistant.class)
                    .streamingChatModel(remoteStreamingModel)
                    .toolProvider(browserToolProvider)
                    .build();

            // 构建完整的提示词
            String fullPrompt = buildFullPromptForRemoteModel(context);

            // 使用TokenStream进行流式调用
            return Flux.<String>create(sink -> {
                try {
                    TokenStream tokenStream = assistant.chat(fullPrompt);
                    
                    tokenStream
                        .onPartialResponse(partialResponse -> {
                            // 使用统一格式化器处理响应
                            String formattedToken = responseFormatter.formatMainContent(partialResponse);

                            // 创建SynchronousSink适配器
                            SynchronousSink<String> synchronousSink = createSynchronousSink(sink);

                            chatService.handelSSE(
                                context.getMainContentBuilder(),
                                context.getThinkingContentBuilder(),
                                formattedToken,
                                synchronousSink,
                                context.getIsInsideThinkingBlock()
                            );
                        })
                        .onCompleteResponse(completeResponse -> {

                            // 发送完成标识
                            SynchronousSink<String> completeSink = createSynchronousSink(sink);
                            String doneSignal = responseFormatter.formatDoneSignal();
                            chatService.handelSSE(
                                context.getMainContentBuilder(),
                                context.getThinkingContentBuilder(),
                                doneSignal,
                                completeSink,
                                context.getIsInsideThinkingBlock()
                            );

                            updateConversationWithQuality(context);
                            BrowserToolProvider.clearToolCallingState(); // 清理ThreadLocal状态
                            sink.complete();
                        })
                        .onError(error -> {
                            log.error("❌ 远程流式模型调用失败: {}", error.getMessage(), error);
                            BrowserToolProvider.clearToolCallingState(); // 清理ThreadLocal状态
                            sink.error(error);
                        })
                        .start();
                } catch (Exception e) {
                    log.error("❌ 远程流式模型调用失败: {}", e.getMessage(), e);
                    BrowserToolProvider.clearToolCallingState(); // 清理ThreadLocal状态
                    sink.error(e);
                }
            })
            .doOnError(e -> {
                BrowserToolProvider.clearToolCallingState(); // 清理ThreadLocal状态
                log.error("❌ 远程流式模型对话出错: {}", e.getMessage(), e);
            });

        } catch (Exception e) {
            BrowserToolProvider.clearToolCallingState(); // 清理ThreadLocal状态
            log.error("❌ 执行远程流式模型对话出错: {}", e.getMessage(), e);
            return Flux.error(e);
        }
    }

    /**
     * 执行简单的远程流式聊天（不带工具调用）
     */
    private Flux<String> executeRemoteStreamingChatSimple(ChatContext context, StreamingChatModel remoteStreamingModel) {
        try {
            // 构建完整的提示词
            String fullPrompt = buildFullPromptForRemoteModel(context);

            // 使用LangChain4j的真正流式调用
            return Flux.<String>create(sink -> {
                try {
                    remoteStreamingModel.chat(fullPrompt, new StreamingChatResponseHandler() {
                        @Override
                        public void onPartialResponse(String partialResponse) {
                            // 使用统一格式化器处理响应
                            String formattedToken = responseFormatter.formatMainContent(partialResponse);

                            // 创建SynchronousSink适配器
                            SynchronousSink<String> synchronousSink = createSynchronousSink(sink);

                            chatService.handelSSE(
                                context.getMainContentBuilder(),
                                context.getThinkingContentBuilder(),
                                formattedToken,
                                synchronousSink,
                                context.getIsInsideThinkingBlock()
                            );
                        }

                        @Override
                        public void onCompleteResponse(ChatResponse completeResponse) {

                            // 发送完成标识
                            SynchronousSink<String> completeSink = createSynchronousSink(sink);
                            String doneSignal = responseFormatter.formatDoneSignal();
                            chatService.handelSSE(
                                context.getMainContentBuilder(),
                                context.getThinkingContentBuilder(),
                                doneSignal,
                                completeSink,
                                context.getIsInsideThinkingBlock()
                            );

                            updateConversationWithQuality(context);
                            BrowserToolProvider.clearToolCallingState(); // 清理ThreadLocal状态
                            sink.complete();
                        }

                        @Override
                        public void onError(Throwable error) {
                            log.error("❌ 远程流式模型调用失败: {}", error.getMessage(), error);
                            BrowserToolProvider.clearToolCallingState(); // 清理ThreadLocal状态
                            sink.error(error);
                        }
                    });
                } catch (Exception e) {
                    log.error("❌ 远程流式模型调用失败: {}", e.getMessage(), e);
                    BrowserToolProvider.clearToolCallingState(); // 清理ThreadLocal状态
                    sink.error(e);
                }
            })
            .doOnError(e -> {
                BrowserToolProvider.clearToolCallingState(); // 清理ThreadLocal状态
                log.error("❌ 远程流式模型对话出错: {}", e.getMessage(), e);
            });

        } catch (Exception e) {
            BrowserToolProvider.clearToolCallingState(); // 清理ThreadLocal状态
            log.error("❌ 执行远程流式模型对话出错: {}", e.getMessage(), e);
            return Flux.error(e);
        }
    }

    /**
     * 构建LangChain4j的ChatMessage历史
     */
    private List<ChatMessage> buildChatMessageHistory(ChatContext context) {
        List<ChatMessage> messages = new ArrayList<>();
        
        // 添加系统消息（如果需要的话，可以从配置或其他地方获取）
        // 暂时不添加系统消息，直接使用历史消息和当前消息
        
        // 添加历史消息
        if (context.getConversationId() != null) {
            try {
                List<com.zibbava.edgemind.cortex.entity.ChatMessage> historyMessages =
                    chatService.getRecentConversationMessages(context.getConversationId(), 10);

                for (com.zibbava.edgemind.cortex.entity.ChatMessage msg : historyMessages) {
                    if ("user".equals(msg.getSender())) {
                        messages.add(UserMessage.from(msg.getContent()));
                    } else if ("assistant".equals(msg.getSender()) || "ai".equals(msg.getSender())) {
                        messages.add(AiMessage.from(msg.getContent()));
                    }
                }
            } catch (Exception e) {
                log.warn("加载历史消息失败: {}", e.getMessage());
            }
        }
        
        // 添加当前用户消息
        messages.add(UserMessage.from(context.getPrompt()));

        return messages;
    }

    /**
     * 为远程模型构建完整的提示词
     */
    private String buildFullPromptForRemoteModel(ChatContext context) {
        StringBuilder promptBuilder = new StringBuilder();
        
        // 添加系统提示词（如果需要的话，可以从配置或其他地方获取）
        // 暂时不添加系统提示词
        
        // 添加历史对话
        if (context.getConversationId() != null) {
            try {
                List<com.zibbava.edgemind.cortex.entity.ChatMessage> historyMessages =
                    chatService.getRecentConversationMessages(context.getConversationId(), 10);

                for (com.zibbava.edgemind.cortex.entity.ChatMessage msg : historyMessages) {
                    promptBuilder.append(msg.getSender().equals("user") ? "Human: " : "Assistant: ")
                               .append(msg.getContent())
                               .append("\n\n");
                }
            } catch (Exception e) {
                log.warn("加载历史消息失败: {}", e.getMessage());
            }
        }
        
        // 添加当前用户消息
        promptBuilder.append("Human: ").append(context.getPrompt()).append("\n\nAssistant: ");
        
        return promptBuilder.toString();
    }

    /**
     * 创建SynchronousSink适配器
     */
    private SynchronousSink<String> createSynchronousSink(reactor.core.publisher.FluxSink<String> sink) {
        return new SynchronousSink<String>() {
            @Override
            public void next(String value) {
                sink.next(value);
            }

            @Override
            public void error(Throwable error) {
                sink.error(error);
            }

            @Override
            public void complete() {
                // 不在这里完成，由外层控制
            }

            @Override
            public Context currentContext() {
                return Context.empty();
            }
        };
    }

    /**
     * 更新会话并进行质量评估
     */
    private void updateConversationWithQuality(ChatContext context) {
        try {
            // 基本的会话更新
            updateConversation(context);
        } catch (Exception e) {
            log.warn("⚠️ 会话质量评估失败: {}", e.getMessage());
        }
    }

    /**
     * 更新会话
     */
    private void updateConversation(ChatContext context) {
        // 如果需要，可以在这里添加更新会话的逻辑
        // 例如更新最后活动时间、会话质量评分等
    }
}
