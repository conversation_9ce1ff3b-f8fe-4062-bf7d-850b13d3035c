package com.zibbava.edgemind.cortex.util;

import java.io.BufferedReader;
import java.io.InputStreamReader;

/**
 * 系统架构检测工具类
 * 统一处理CPU架构检测逻辑，支持Windows、Linux、macOS
 */
public class SystemArchitectureDetector {

    /**
     * 系统架构枚举
     */
    public enum SystemArchitecture {
        X64("x64", "amd64", "x86_64"),
        ARM64("arm64", "aarch64", "arm64"),
        UNKNOWN("unknown", "unknown", "unknown");

        private final String displayName;
        private final String javaArch;
        private final String alternativeName;

        SystemArchitecture(String displayName, String javaArch, String alternativeName) {
            this.displayName = displayName;
            this.javaArch = javaArch;
            this.alternativeName = alternativeName;
        }

        public String getDisplayName() { return displayName; }
        public String getJavaArch() { return javaArch; }
        public String getAlternativeName() { return alternativeName; }
    }

    /**
     * 检测当前系统架构
     * 
     * @return SystemArchitecture 枚举值
     */
    public static SystemArchitecture detectArchitecture() {
        // 首先尝试从Java系统属性获取
        String osArch = System.getProperty("os.arch").toLowerCase();
        
        if (osArch.contains("amd64") || osArch.contains("x86_64")) {
            return SystemArchitecture.X64;
        } else if (osArch.contains("aarch64") || osArch.contains("arm64")) {
            return SystemArchitecture.ARM64;
        }

        // 如果Java属性不明确，尝试使用系统命令进一步检测
        String osName = System.getProperty("os.name").toLowerCase();
        
        if (osName.contains("windows")) {
            return detectWindowsArchitecture();
        } else if (osName.contains("linux")) {
            return detectLinuxArchitecture();
        } else if (osName.contains("mac")) {
            return detectMacArchitecture();
        }

        System.out.println("警告: 未知系统架构 " + osArch + "，默认使用x64");
        return SystemArchitecture.X64;
    }

    /**
     * 检测Windows系统架构
     */
    private static SystemArchitecture detectWindowsArchitecture() {
        try {
            ProcessBuilder pb = new ProcessBuilder(
                "powershell", "-Command",
                "$arch = (Get-WmiObject Win32_OperatingSystem).OSArchitecture; " +
                "if ($arch -eq '64-bit') { " +
                "  $proc = (Get-WmiObject Win32_Processor).Architecture; " +
                "  if ($proc -eq 12) { 'arm64' } else { 'x64' } " +
                "} elseif ($arch -eq 'ARM 64-bit') { 'arm64' } else { 'x64' }"
            );
            Process process = pb.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String result = reader.readLine();
            process.waitFor();
            
            if (result != null) {
                result = result.trim().toLowerCase();
                if (result.equals("arm64")) {
                    return SystemArchitecture.ARM64;
                } else if (result.equals("x64")) {
                    return SystemArchitecture.X64;
                }
            }
        } catch (Exception e) {
            System.err.println("检测Windows架构失败: " + e.getMessage());
        }
        
        return SystemArchitecture.X64; // 默认返回x64
    }

    /**
     * 检测Linux系统架构
     */
    private static SystemArchitecture detectLinuxArchitecture() {
        try {
            ProcessBuilder pb = new ProcessBuilder("uname", "-m");
            Process process = pb.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String result = reader.readLine();
            process.waitFor();
            
            if (result != null) {
                result = result.trim().toLowerCase();
                if (result.equals("aarch64") || result.equals("arm64")) {
                    return SystemArchitecture.ARM64;
                } else if (result.equals("x86_64") || result.equals("amd64")) {
                    return SystemArchitecture.X64;
                }
            }
        } catch (Exception e) {
            System.err.println("检测Linux架构失败: " + e.getMessage());
        }
        
        return SystemArchitecture.X64; // 默认返回x64
    }

    /**
     * 检测macOS系统架构
     */
    private static SystemArchitecture detectMacArchitecture() {
        try {
            ProcessBuilder pb = new ProcessBuilder("uname", "-m");
            Process process = pb.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String result = reader.readLine();
            process.waitFor();
            
            if (result != null) {
                result = result.trim().toLowerCase();
                if (result.equals("arm64")) {
                    return SystemArchitecture.ARM64;
                } else if (result.equals("x86_64")) {
                    return SystemArchitecture.X64;
                }
            }
        } catch (Exception e) {
            System.err.println("检测macOS架构失败: " + e.getMessage());
        }
        
        return SystemArchitecture.X64; // 默认返回x64
    }

    /**
     * 获取架构的显示名称
     */
    public static String getArchitectureDisplayName() {
        return detectArchitecture().getDisplayName();
    }

    /**
     * 检查是否为ARM64架构
     */
    public static boolean isARM64() {
        return detectArchitecture() == SystemArchitecture.ARM64;
    }

    /**
     * 检查是否为x64架构
     */
    public static boolean isX64() {
        return detectArchitecture() == SystemArchitecture.X64;
    }
}
