package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zibbava.edgemind.cortex.dto.ApiResponse;
import com.zibbava.edgemind.cortex.dto.UserManagementDTO.*;
import com.zibbava.edgemind.cortex.service.UserManagementService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 用户管理控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/system/user")
@RequiredArgsConstructor
@Validated
public class UserManagementController {

    private final UserManagementService userManagementService;

    /**
     * 分页查询用户列表 (GET方式 - 兼容旧版本)
     * 注意：查询操作不记录日志，避免日志膨胀
     */
    @GetMapping("/list")
    @SaCheckPermission("user:manage:list")
    public ResponseEntity<ApiResponse<IPage<UserResponse>>> getUserPage(@Valid UserQueryRequest request) {
        IPage<UserResponse> result = userManagementService.getUserPage(request);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 分页查询用户列表 (POST方式 - 推荐使用)
     * 注意：查询操作不记录日志，避免日志膨胀
     */
    @PostMapping("/page")
    @SaCheckPermission("user:manage:list")
    public ResponseEntity<ApiResponse<IPage<UserResponse>>> getUserPageByPost(@RequestBody UserQueryRequest request) {
        // 设置默认值
        if (request.getPageNum() == null) {
            request.setPageNum(1);
        }
        if (request.getPageSize() == null) {
            request.setPageSize(10);
        }

        IPage<UserResponse> result = userManagementService.getUserPage(request);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 获取用户详情
     * 注意：查询操作不记录日志，避免日志膨胀
     */
    @GetMapping("/{userId}")
    @SaCheckPermission("user:manage:list")
    public ResponseEntity<ApiResponse<UserResponse>> getUserById(@PathVariable Long userId) {
        UserResponse result = userManagementService.getUserById(userId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 创建用户
     */
    @PostMapping
    @SaCheckPermission("user:manage:create")
    public ResponseEntity<ApiResponse<Long>> createUser(@Valid @RequestBody CreateUserRequest request) {
        Long userId = userManagementService.createUser(request);
        return ResponseEntity.ok(ApiResponse.success("用户创建成功", userId));
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/{userId}")
    @SaCheckPermission("user:manage:update")
    public ResponseEntity<ApiResponse<Void>> updateUser(@PathVariable Long userId,
                                                       @Valid @RequestBody UpdateUserRequest request) {
        request.setId(userId);
        userManagementService.updateUser(request);
        return ResponseEntity.ok(ApiResponse.success("用户信息更新成功"));
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{userId}")
    @SaCheckPermission("user:manage:delete")
    public ResponseEntity<ApiResponse<Void>> deleteUser(@PathVariable Long userId) {
        userManagementService.deleteUser(userId);
        return ResponseEntity.ok(ApiResponse.success("用户删除成功"));
    }

    /**
     * 批量删除用户
     */
    @DeleteMapping("/batch")
    @SaCheckPermission("user:manage:delete")
    public ResponseEntity<ApiResponse<Void>> batchDeleteUsers(@RequestBody List<Long> userIds) {
        userManagementService.batchDeleteUsers(userIds);
        return ResponseEntity.ok(ApiResponse.success("用户批量删除成功"));
    }

    /**
     * 重置用户密码（管理员操作）
     */
    @PostMapping("/{userId}/reset-password")
    @SaCheckPermission("user:manage:reset-password")
    public ResponseEntity<ApiResponse<Void>> resetPassword(@PathVariable Long userId,
                                                          @Valid @RequestBody ResetPasswordRequest request) {
        request.setUserId(userId);
        userManagementService.resetPassword(request);
        return ResponseEntity.ok(ApiResponse.success("密码重置成功"));
    }

    /**
     * 为用户分配角色
     */
    @PostMapping("/{userId}/roles")
    @SaCheckPermission("user:manage:assign-role")
    public ResponseEntity<ApiResponse<Void>> assignRoles(@PathVariable Long userId,
                                                        @Valid @RequestBody AssignRoleRequest request) {
        request.setUserId(userId);
        userManagementService.assignRoles(request);
        return ResponseEntity.ok(ApiResponse.success("角色分配成功"));
    }

    /**
     * 切换用户状态
     */
    @PostMapping("/{userId}/toggle-status")
    @SaCheckPermission("user:manage:update")
    public ResponseEntity<ApiResponse<Void>> toggleUserStatus(@PathVariable Long userId,
                                                             @Valid @RequestBody ToggleStatusRequest request) {
        request.setUserId(userId);
        userManagementService.toggleUserStatus(request);
        return ResponseEntity.ok(ApiResponse.success("用户状态更新成功"));
    }



    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check-username")
    @SaCheckPermission("user:manage:list")
    public ResponseEntity<ApiResponse<Boolean>> checkUsername(@RequestParam String username) {
        boolean exists = userManagementService.isUsernameExists(username);
        return ResponseEntity.ok(ApiResponse.success("检查完成", exists));
    }

    /**
     * 获取用户的角色列表
     * 注意：查询操作不记录日志，避免日志膨胀
     */
    @GetMapping("/{userId}/roles")
    @SaCheckPermission("user:manage:list")
    public ResponseEntity<ApiResponse<List<RoleResponse>>> getUserRoles(@PathVariable Long userId) {
        List<RoleResponse> roles = userManagementService.getUserRoles(userId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", roles));
    }



    /**
     * 获取简化用户列表（用于下拉选择）
     * 注意：查询操作不记录日志，避免日志膨胀
     */
    @GetMapping("/simple")
    @SaCheckPermission("user:manage:list")
    public ResponseEntity<ApiResponse<List<SimpleUserResponse>>> getSimpleUsers(
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Long deptId) {
        List<SimpleUserResponse> users = userManagementService.getSimpleUsers(status, deptId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", users));
    }
}
