package com.zibbava.edgemind.cortex.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.zibbava.edgemind.cortex.common.enums.ResultCode;
import com.zibbava.edgemind.cortex.common.exception.BusinessException;
import com.zibbava.edgemind.cortex.dto.ChatContext;
import com.zibbava.edgemind.cortex.dto.UnifiedChatRequest;
import com.zibbava.edgemind.cortex.service.UnifiedChatService;
import com.zibbava.edgemind.cortex.service.strategy.ChatStrategyManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

/**
 * 统一聊天服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UnifiedChatServiceImpl implements UnifiedChatService {

    private final ChatStrategyManager strategyManager;

    @Override
    public Flux<String> chat(UnifiedChatRequest request) {
        try {
            // 获取用户ID
            Long userId;
            if (StpUtil.isLogin()) {
                userId = StpUtil.getLoginIdAsLong();
                log.info("用户 [{}] 发送聊天请求: prompt={}, model={}, knowledgeNodeId={}",
                        userId, request.getPrompt(), request.getModel(), request.getKnowledgeNodeId());
            } else {
                log.info("游客发送聊天请求: prompt={}, model={}", request.getPrompt(), request.getModel());
                throw new BusinessException(ResultCode.UNAUTHORIZED);
            }

            // 参数验证
            if (request.getPrompt() == null || request.getPrompt().trim().isEmpty()) {
                return Flux.error(new BusinessException(ResultCode.PARAM_MISSING, "提示词不能为空"));
            }

            if (request.getModel() == null || request.getModel().trim().isEmpty()) {
                return Flux.error(new BusinessException(ResultCode.PARAM_MISSING, "模型名称不能为空"));
            }

            // 创建聊天上下文
            final ChatContext context = ChatContext.fromRequest(request, userId);

            // 获取合适的策略
            var strategy = strategyManager.getStrategy(context);
            log.info("选择策略: {}", strategy.getClass().getSimpleName());

            // 执行通用聊天流程
            if (!strategy.checkPermission(context)) {
                return Flux.error(new BusinessException(ResultCode.FORBIDDEN, "无权访问此资源"));
            }

            // 准备上下文
            strategy.prepareContext(context);

            // 构建提示词
            String prompt = strategy.buildPrompt(context);
            context.setFinalPrompt(prompt);

            // 执行聊天并处理结果
            return strategy.executeChat(context)
                    .doOnComplete(() -> {
                        try {
                            // 保存对话历史
                            strategy.saveChatHistory(
                                    context,
                                    context.getPrompt(),
                                    context.getMainContentBuilder().toString(),
                                    context.getThinkingContentBuilder().toString()
                            );
                            log.info("聊天完成并保存: conversationId={}", context.getConversationId());
                        } catch (Exception e) {
                            log.error("保存聊天历史失败: {}", e.getMessage(), e);
                        }
                    })
                    .doOnError(e -> log.error("聊天过程中发生错误: {}", e.getMessage(), e));

        } catch (Exception e) {
            log.error("处理聊天请求失败: {}", e.getMessage(), e);
            return Flux.error(e);
        }
    }
}
