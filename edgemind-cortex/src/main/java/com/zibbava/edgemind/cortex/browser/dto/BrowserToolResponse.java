package com.zibbava.edgemind.cortex.browser.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 浏览器工具响应数据传输对象
 * 用于封装从浏览器插件返回到Java后端的工具执行结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrowserToolResponse {
    
    /**
     * 对应的请求ID，用于匹配请求和响应
     */
    private String requestId;
    
    /**
     * 执行是否成功
     */
    private boolean success;
    
    /**
     * 执行结果数据
     * 成功时包含工具执行的具体结果
     */
    private Object data;
    
    /**
     * 错误信息
     * 失败时包含详细的错误描述
     */
    private String error;
    
    /**
     * 错误代码
     * 用于程序化处理不同类型的错误
     */
    private String errorCode;
    
    /**
     * 响应时间戳
     */
    private long timestamp;
    
    /**
     * 执行耗时（毫秒）
     */
    private long executionTimeMs;
    
    /**
     * 工具名称（回显）
     */
    private String toolName;
    
    /**
     * 执行的标签页ID
     */
    private Integer tabId;
    
    /**
     * 执行的标签页URL
     */
    private String tabUrl;
    
    /**
     * 执行的标签页标题
     */
    private String tabTitle;
    
    /**
     * 额外的元数据信息
     */
    private Map<String, Object> metadata;
    
    /**
     * 创建成功响应的便捷方法
     */
    public static BrowserToolResponse success(String requestId, Object data) {
        return BrowserToolResponse.builder()
                .requestId(requestId)
                .success(true)
                .data(data)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建成功响应（带工具名称）
     */
    public static BrowserToolResponse success(String requestId, String toolName, Object data) {
        return BrowserToolResponse.builder()
                .requestId(requestId)
                .toolName(toolName)
                .success(true)
                .data(data)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建失败响应的便捷方法
     */
    public static BrowserToolResponse error(String requestId, String error) {
        return BrowserToolResponse.builder()
                .requestId(requestId)
                .success(false)
                .error(error)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建失败响应（带错误代码）
     */
    public static BrowserToolResponse error(String requestId, String error, String errorCode) {
        return BrowserToolResponse.builder()
                .requestId(requestId)
                .success(false)
                .error(error)
                .errorCode(errorCode)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建超时响应
     */
    public static BrowserToolResponse timeout(String requestId) {
        return BrowserToolResponse.builder()
                .requestId(requestId)
                .success(false)
                .error("工具执行超时")
                .errorCode("TIMEOUT")
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 检查是否为成功响应
     */
    public boolean isSuccess() {
        return success;
    }
    
    /**
     * 检查是否为错误响应
     */
    public boolean isError() {
        return !success;
    }
    
    /**
     * 获取数据的类型安全方法
     */
    @SuppressWarnings("unchecked")
    public <T> T getData(Class<T> type) {
        if (data == null) {
            return null;
        }
        if (type.isInstance(data)) {
            return (T) data;
        }
        throw new ClassCastException("Cannot cast data to " + type.getSimpleName());
    }
}
