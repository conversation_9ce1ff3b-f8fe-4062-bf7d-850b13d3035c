package com.zibbava.edgemind.cortex.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zibbava.edgemind.cortex.dto.UserManagementDTO.*;
import com.zibbava.edgemind.cortex.entity.User;

import java.util.List;

/**
 * 用户管理服务接口
 * 
 * <AUTHOR>
 */
public interface UserManagementService {

    /**
     * 分页查询用户列表
     * 
     * @param request 查询请求
     * @return 分页结果
     */
    IPage<UserResponse> getUserPage(UserQueryRequest request);

    /**
     * 根据ID获取用户详情
     * 
     * @param userId 用户ID
     * @return 用户详情
     */
    UserResponse getUserById(Long userId);

    /**
     * 创建用户
     * 
     * @param request 创建请求
     * @return 用户ID
     */
    Long createUser(CreateUserRequest request);

    /**
     * 更新用户信息
     * 
     * @param request 更新请求
     */
    void updateUser(UpdateUserRequest request);

    /**
     * 删除用户
     * 
     * @param userId 用户ID
     */
    void deleteUser(Long userId);

    /**
     * 批量删除用户
     * 
     * @param userIds 用户ID列表
     */
    void batchDeleteUsers(List<Long> userIds);

    /**
     * 重置用户密码（管理员操作）
     *
     * @param request 重置密码请求
     */
    void resetPassword(ResetPasswordRequest request);

    /**
     * 修改用户密码（用户自己操作）
     *
     * @param userId 用户ID
     * @param request 修改密码请求
     */
    void changePassword(Long userId, ChangePasswordRequest request);

    /**
     * 为用户分配角色
     * 
     * @param request 分配角色请求
     */
    void assignRoles(AssignRoleRequest request);

    /**
     * 切换用户状态（启用/禁用）
     * 
     * @param request 状态切换请求
     */
    void toggleUserStatus(ToggleStatusRequest request);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @return 是否存在
     */
    boolean isUsernameExists(String username);

    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @param excludeUserId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isEmailExists(String email, Long excludeUserId);

    /**
     * 检查手机号是否存在
     * 
     * @param phone 手机号
     * @param excludeUserId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isPhoneExists(String phone, Long excludeUserId);

    /**
     * 获取用户的角色列表
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    List<RoleResponse> getUserRoles(Long userId);



    /**
     * 获取简化用户列表（用于下拉选择）
     *
     * @param status 用户状态（可选）
     * @param deptId 部门ID（可选）
     * @return 简化用户列表
     */
    List<SimpleUserResponse> getSimpleUsers(Integer status, Long deptId);

}
