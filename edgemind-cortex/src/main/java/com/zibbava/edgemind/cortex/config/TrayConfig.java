package com.zibbava.edgemind.cortex.config;

import com.zibbava.edgemind.cortex.tray.SystemTrayManager;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;

import jakarta.annotation.PreDestroy;

/**
 * 系统托盘配置
 *
 * <AUTHOR>
 */
@Component
public class TrayConfig implements ApplicationListener<ApplicationReadyEvent> {
    
    private ConfigurableApplicationContext applicationContext;
    
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        this.applicationContext = event.getApplicationContext();
        
        new Thread(() -> {
            try {
                Thread.sleep(2000);
                SystemTrayManager.initialize(applicationContext);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }, "TrayInitializer").start();
    }
    
    @PreDestroy
    public void cleanup() {
        SystemTrayManager.cleanup();
    }
}
