package com.zibbava.edgemind.cortex.service;

import java.util.Map;

/**
 * 文档查看器服务接口
 * 提供基于开源前端工具的文档查看和编辑功能
 * 替代OnlyOffice，支持多种文档格式
 */
public interface DocumentViewerService {

    /**
     * 获取指定文件节点的查看器配置
     *
     * @param nodeId 文件节点ID
     * @param userId 当前用户ID
     * @param userName 当前用户名称
     * @return 文档查看器配置对象及附加信息
     */
    Map<String, Object> getDocumentViewerConfig(String nodeId, Long userId, String userName);
    
    /**
     * 获取支持的文件类型列表
     * 
     * @return 支持的文件扩展名列表
     */
    Map<String, String> getSupportedFileTypes();
    
    /**
     * 根据文件扩展名判断查看器类型
     * 
     * @param fileName 文件名
     * @return 查看器类型 (pdf, office, code, image, text, etc.)
     */
    String getViewerType(String fileName);
    
    /**
     * 检查文件是否支持在线编辑
     * 
     * @param fileName 文件名
     * @return 是否支持编辑
     */
    boolean isEditable(String fileName);
}
