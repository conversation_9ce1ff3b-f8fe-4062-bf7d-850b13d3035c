package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 知识空间实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("kb_knowledge_spaces")
public class KnowledgeSpace {

    /**
     * 知识空间ID (UUID字符串)
     */
    @TableId(value = "space_id", type = IdType.ASSIGN_UUID)
    private String spaceId;

    /**
     * 私人空间的所有者用户ID (关联 sys_user.id)，团队空间时为 NULL
     */
    @TableField("owner_user_id")
    private Long ownerUserId;

    /**
     * 空间名称
     */
    @TableField("name")
    private String name;

    /**
     * 空间描述
     */
    @TableField("description")
    private String description;

    /**
     * 是否为私人空间 (true/1=私人, false/0=团队)
     */


    /**
     * 访问类型: PUBLIC(公开), PRIVATE(私有), ROLE_BASED(基于角色)
     */
    @TableField("access_type")
    private String accessType;

    /**
     * 是否为系统空间（用于区分用户创建的空间）
     */


    /**
     * 创建者用户ID (关联 sys_user.id)
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 最后更新者用户ID (关联 sys_user.id)
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 创建时间 (由数据库或MyBatis-Plus自动填充)
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间 (由数据库或MyBatis-Plus自动填充)
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

} 