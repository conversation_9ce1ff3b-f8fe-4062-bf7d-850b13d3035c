package com.zibbava.edgemind.cortex.common.constants;

/**
 * 系统设置键名常量
 */
public class SettingKeys {

    /**
     * 系统相关设置
     */
    public static class System {
        /**
         * 系统版本号
         */
        public static final String VERSION = "system.version";

        /**
         * 系统唯一标识
         */
        public static final String IDENTIFIER = "system.identifier";
    }

    /**
     * 知识库相关设置
     */
    public static class KnowledgeBase {

        /**
         * 知识库存储路径
         */
        public static final String STORAGE_PATH = "kb.storage.path";

        /**
         * 个人库存储路径
         */
        public static final String PERSONAL_STORAGE_PATH = "kb.personal.storage.path";

        /**
         * 知识库同步状态
         */
        public static final String SYNC_STATUS = "kb.sync.status";

        /**
         * 知识库同步消息
         */
        public static final String SYNC_MESSAGE = "kb.sync.message";

        /**
         * 知识库最后同步时间
         */
        public static final String LAST_SYNC_TIME = "kb.last.sync.time";
    }

    /**
     * 许可证相关设置
     */
    public static class License {
        /**
         * 授权状态：0-未授权，1-已授权
         */
        public static final String STATUS = "license.status";

        /**
         * 授权类型：trial-试用版，standard-标准版，professional-专业版，enterprise-企业版
         */
        public static final String TYPE = "license.type";

        /**
         * 授权过期时间
         */
        public static final String EXPIRE_TIME = "license.expire_time";
    }

    /**
     * 同步状态常量
     */
    public static class SyncStatus {
        /**
         * 未同步
         */
        public static final int NOT_SYNCED = 0;

        /**
         * 同步中
         */
        public static final int SYNCING = 1;

        /**
         * 同步完成
         */
        public static final int COMPLETED = 2;

        /**
         * 同步失败
         */
        public static final int FAILED = 3;
    }
}
