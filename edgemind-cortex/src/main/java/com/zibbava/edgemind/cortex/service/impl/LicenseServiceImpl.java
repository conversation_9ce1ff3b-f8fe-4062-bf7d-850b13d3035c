package com.zibbava.edgemind.cortex.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zibbava.edgemind.cortex.common.constants.SettingKeys;
import com.zibbava.edgemind.cortex.dto.LicenseCheckResult;
import com.zibbava.edgemind.cortex.dto.LicenseStatusDTO;
import com.zibbava.edgemind.cortex.entity.License;
import com.zibbava.edgemind.cortex.mapper.LicenseMapper;
import com.zibbava.edgemind.cortex.service.LicenseService;
import com.zibbava.edgemind.cortex.service.SystemSettingsService;
import com.zibbava.edgemind.cortex.util.HardwareInfoUtils;
import com.zibbava.edgemind.cortex.util.LicenseUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 许可证服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LicenseServiceImpl implements LicenseService {

    private final LicenseMapper licenseMapper;
    private final SystemSettingsService systemSettingsService;

    // 创建定时缓存，默认5分钟过期
    private final TimedCache<String, LicenseCheckResult> licenseCache = CacheUtil.newTimedCache(5 * 60 * 1000);

    // 缓存键
    private static final String LICENSE_CACHE_KEY = "license_status";

    /**
     * 获取当前硬件指纹
     *
     * @return 硬件指纹字符串
     */
    @Override
    public String getHardwareFingerprint() {
        return HardwareInfoUtils.getHardwareFingerprint();
    }

    /**
     * 获取详细的硬件信息（用于调试）
     *
     * @return 硬件信息字符串
     */
    @Override
    public String getDetailedHardwareInfo() {
        List<String> details = HardwareInfoUtils.getDetailedHardwareInfo();
        return String.join("\n", details);
    }

    /**
     * 验证许可证
     *
     * @param licenseKey 许可证密钥
     * @return 验证结果，true表示验证通过
     */
    @Override
    public boolean verifyLicense(String licenseKey) {
        if (!StringUtils.hasText(licenseKey)) {
            return false;
        }

        String hardwareFingerprint = getHardwareFingerprint();
        String systemIdentifier = systemSettingsService.getSystemIdentifier();

        return LicenseUtils.verifyLicenseKey(licenseKey, hardwareFingerprint, systemIdentifier);
    }

    /**
     * 激活许可证
     *
     * @param licenseKey 许可证密钥
     * @return 激活结果，true表示激活成功
     */
    @Override
    @Transactional
    public boolean activateLicense(String licenseKey) {
        if (!StringUtils.hasText(licenseKey)) {
            return false;
        }

        // 验证许可证
        if (!verifyLicense(licenseKey)) {
            log.warn("许可证验证失败");
            return false;
        }

        try {
            String hardwareFingerprint = getHardwareFingerprint();
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expireTime = LicenseUtils.getExpireTimeFromLicense(licenseKey);

            // 保存许可证记录
            License license = new License();
            license.setLicenseKey(licenseKey);
            license.setHardwareFingerprint(hardwareFingerprint);
            license.setStatus(1); // 已激活
            license.setActivatedTime(now);
            license.setExpireTime(expireTime);
            licenseMapper.insert(license);

            // 更新系统设置
            systemSettingsService.updateSettingValue(SettingKeys.License.STATUS, "1", "授权状态");
            systemSettingsService.updateSettingValue(SettingKeys.License.TYPE, "standard", "授权类型");

            if (expireTime != null) {
                systemSettingsService.updateSettingValue(
                        SettingKeys.License.EXPIRE_TIME,
                        expireTime.toString(),
                        "授权过期时间"
                );
            } else {
                systemSettingsService.updateSettingValue(
                        SettingKeys.License.EXPIRE_TIME,
                        "NEVER",
                        "授权过期时间"
                );
            }

            // 激活成功后清除缓存，确保下次查询能获取最新状态
            clearLicenseCache();

            log.info("许可证激活成功");
            return true;
        } catch (Exception e) {
            log.error("激活许可证失败", e);
            return false;
        }
    }

    /**
     * 清除授权状态缓存
     */
    private void clearLicenseCache() {
        licenseCache.remove(LICENSE_CACHE_KEY);
        log.debug("清除授权状态缓存");
    }

    /**
     * 清除授权状态缓存（公开方法）
     */
    @Override
    public void clearCache() {
        clearLicenseCache();
    }

    /**
     * 获取许可证状态
     *
     * @return 许可证状态信息
     */
    @Override
    public LicenseStatusDTO getLicenseStatus() {
        String licenseStatus = systemSettingsService.getSettingValue(SettingKeys.License.STATUS, "0");
        String licenseType = systemSettingsService.getSettingValue(SettingKeys.License.TYPE, "trial");
        String expireTimeStr = systemSettingsService.getSettingValue(SettingKeys.License.EXPIRE_TIME);

        LocalDateTime expireTime = null;
        if (StringUtils.hasText(expireTimeStr) && !"NEVER".equals(expireTimeStr)) {
            try {
                expireTime = LocalDateTime.parse(expireTimeStr);
            } catch (Exception e) {
                log.warn("解析过期时间失败: {}", expireTimeStr);
            }
        }

        // 获取最新的许可证记录
        License license = null;
        if ("1".equals(licenseStatus)) {
            LambdaQueryWrapper<License> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(License::getStatus, 1)
                    .orderByDesc(License::getActivatedTime)
                    .last("LIMIT 1");
            license = licenseMapper.selectOne(queryWrapper);
        }

        LocalDateTime activatedTime = license != null ? license.getActivatedTime() : null;
        String hardwareFingerprint = getHardwareFingerprint();
        String systemIdentifier = systemSettingsService.getSystemIdentifier();

        // 计算是否过期和剩余天数
        boolean isExpired = false;
        long remainingDays = -1; // -1表示永不过期
        if (expireTime != null) {
            isExpired = LocalDateTime.now().isAfter(expireTime);
            if (!isExpired) {
                remainingDays = ChronoUnit.DAYS.between(LocalDateTime.now(), expireTime);
            } else {
                remainingDays = 0;
            }
        }

        return LicenseStatusDTO.builder()
                .status(Integer.parseInt(licenseStatus))
                .hardwareFingerprint(hardwareFingerprint)
                .systemIdentifier(systemIdentifier)
                .licenseType(licenseType)
                .activatedTime(activatedTime)
                .expireTime(expireTime)
                .isExpired(isExpired)
                .remainingDays(remainingDays)
                .build();
    }

    /**
     * 检查系统是否已授权
     * 包括检查授权状态、过期时间和硬件指纹是否匹配
     *
     * @return 授权状态，true表示已授权
     */
    @Override
    public boolean isLicensed() {
        LicenseCheckResult result = checkLicenseStatus();
        return result.isLicensed();
    }

    /**
     * 检查系统授权状态并返回详细信息
     * 使用Hutool缓存减少数据库查询
     *
     * @return 包含授权状态和详细信息的对象
     */
    @Override
    public synchronized LicenseCheckResult checkLicenseStatus() {
        // 从缓存中获取授权状态
        LicenseCheckResult cachedResult = licenseCache.get(LICENSE_CACHE_KEY);
        if (cachedResult != null) {
            log.debug("使用缓存的授权状态结果");
            return cachedResult;
        }

        log.debug("缓存失效或不存在，重新查询授权状态");

        try {
            // 获取授权状态
            LicenseStatusDTO status = getLicenseStatus();

            // 如果未激活或已过期，直接返回未授权
            if (status.getStatus() != 1) {
                LicenseCheckResult result = LicenseCheckResult.builder()
                        .licensed(false)
                        .statusInfo(status)
                        .failReason("not_activated")
                        .failMessage("系统未激活")
                        .build();
                // 将结果存入缓存
                licenseCache.put(LICENSE_CACHE_KEY, result);
                return result;
            }

            if (status.getIsExpired()) {
                LicenseCheckResult result = LicenseCheckResult.builder()
                        .licensed(false)
                        .statusInfo(status)
                        .failReason("expired")
                        .failMessage("许可证已过期")
                        .build();
                // 将结果存入缓存
                licenseCache.put(LICENSE_CACHE_KEY, result);
                return result;
            }

            // 获取当前硬件指纹
            String currentFingerprint = getHardwareFingerprint();

            // 获取最新的有效许可证记录
            License license = getActiveLicense();
            if (license == null) {
                log.warn("未找到有效的许可证记录");
                LicenseCheckResult result = LicenseCheckResult.builder()
                        .licensed(false)
                        .statusInfo(status)
                        .failReason("not_activated")
                        .failMessage("未找到有效的许可证记录")
                        .build();
                // 将结果存入缓存
                licenseCache.put(LICENSE_CACHE_KEY, result);
                return result;
            }

            // 验证硬件指纹是否匹配
            if (!currentFingerprint.equals(license.getHardwareFingerprint())) {
                log.warn("硬件指纹不匹配: 期望={}, 实际={}",
                        license.getHardwareFingerprint(), currentFingerprint);
                LicenseCheckResult result = LicenseCheckResult.builder()
                        .licensed(false)
                        .statusInfo(status)
                        .failReason("fingerprint")
                        .failMessage("硬件指纹不匹配")
                        .build();
                // 将结果存入缓存
                licenseCache.put(LICENSE_CACHE_KEY, result);
                return result;
            }

            // 所有检查通过，返回授权成功
            LicenseCheckResult result = LicenseCheckResult.builder()
                    .licensed(true)
                    .statusInfo(status)
                    .build();
            // 将结果存入缓存
            licenseCache.put(LICENSE_CACHE_KEY, result);
            return result;
        } catch (Exception e) {
            log.error("检查授权状态时出错", e);
            LicenseCheckResult result = LicenseCheckResult.builder()
                    .licensed(false)
                    .failReason("error")
                    .failMessage("系统错误: " + e.getMessage())
                    .build();
            // 出错时不缓存结果，下次还会重试
            return result;
        }
    }

    /**
     * 获取当前激活的许可证
     * @return 激活的许可证记录，如果没有则返回null
     */
    private License getActiveLicense() {
        LambdaQueryWrapper<License> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(License::getStatus, 1)
                .orderByDesc(License::getActivatedTime)
                .last("LIMIT 1");
        return licenseMapper.selectOne(queryWrapper);
    }

}
