package com.zibbava.edgemind.cortex.util;

import org.springframework.util.StringUtils;

import java.util.Arrays;

public class MediaUtils {



    /**
     * 根据文件扩展名获取 ONLYOFFICE 文档类型。
     *
     * @param fileExtension 文件扩展名 (小写，不带点)
     * @return ONLYOFFICE 文档类型 ("word", "cell", "slide", "text")，未知类型返回 "text"。
     */
    public static String getDocumentType(String fileExtension) {
        if (!StringUtils.hasText(fileExtension)) {
            return "text"; // 默认为文本
        }
        switch (fileExtension) {
            // Word processing documents
            case "doc":
            case "docx":
            case "odt":
            case "rtf":
            case "txt": // Treat .txt as text document handled by word editor
            case "html":
            case "htm":
            case "mht":
            case "pdf": // PDF is typically viewed/edited in word context
            case "djvu":
            case "xps":
                return "word";
            // Spreadsheets
            case "xls":
            case "xlsx":
            case "ods":
            case "csv":
                return "cell";
            // Presentations
            case "ppt":
            case "pptx":
            case "odp":
                return "slide";
            default:
                return "text"; // 或根据需要抛出异常/返回null
        }
    }


    /**
     * 根据MIME类型获取文件扩展名
     *
     * @param mimeType MIME类型
     * @return 文件扩展名（包含点号）
     */
    public static String getMimeTypeExtension(String mimeType) {
        if (mimeType == null) {
            return "";
        }

        switch (mimeType.toLowerCase()) {
            // 文本文档
            case "application/msword":
            case "application/vnd.ms-word":
                return ".doc";
            case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                return ".docx";
            case "application/vnd.oasis.opendocument.text":
                return ".odt";
            case "application/rtf":
                return ".rtf";
            case "text/plain":
                return ".txt";
            case "text/markdown":
            case "text/x-markdown":
                return ".md";
            case "text/html":
            case "application/xhtml+xml":
                return ".html";
            case "text/xml":
            case "application/xml":
                return ".xml";
            case "application/pdf":
                return ".pdf";
            case "application/x-mht":
            case "message/rfc822":
                return ".mht";
            case "application/vnd.ms-xpsdocument":
                return ".xps";
            case "image/vnd.djvu":
                return ".djvu";

            // 电子表格
            case "application/vnd.ms-excel":
            case "application/excel":
            case "application/x-excel":
            case "application/x-msexcel":
                return ".xls";
            case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
                return ".xlsx";
            case "application/vnd.oasis.opendocument.spreadsheet":
                return ".ods";
            case "text/csv":
            case "text/comma-separated-values":
                return ".csv";
            case "text/tab-separated-values":
                return ".tsv";

            // 演示文档
            case "application/vnd.ms-powerpoint":
            case "application/powerpoint":
            case "application/x-powerpoint":
                return ".ppt";
            case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
                return ".pptx";
            case "application/vnd.oasis.opendocument.presentation":
                return ".odp";

            // 图片
            case "image/jpeg":
            case "image/pjpeg":
                return ".jpg";
            case "image/png":
                return ".png";
            case "image/gif":
                return ".gif";
            case "image/svg+xml":
                return ".svg";
            case "image/bmp":
            case "image/x-windows-bmp":
                return ".bmp";
            case "image/tiff":
                return ".tiff";
            case "image/webp":
                return ".webp";

            // 压缩文件
            case "application/zip":
                return ".zip";
            case "application/x-rar-compressed":
            case "application/vnd.rar":
                return ".rar";
            case "application/x-7z-compressed":
                return ".7z";
            case "application/gzip":
                return ".gz";
            case "application/x-tar":
                return ".tar";

            // 音频文件
            case "audio/mpeg":
                return ".mp3";
            case "audio/wav":
            case "audio/x-wav":
                return ".wav";
            case "audio/ogg":
                return ".ogg";
            case "audio/aac":
                return ".aac";

            // 视频文件
            case "video/mp4":
                return ".mp4";
            case "video/mpeg":
                return ".mpeg";
            case "video/quicktime":
                return ".mov";
            case "video/x-msvideo":
                return ".avi";
            case "video/webm":
                return ".webm";

            default:
                // 尝试从 MIME 类型中提取扩展名
                String[] parts = mimeType.split("/");
                if (parts.length == 2 && !parts[1].contains(";")) {
                    return "." + parts[1];
                }
                return "";
        }
    }


    /**
     * 根据文件扩展名获取MIME类型
     * 与MediaUtils.getMimeTypeExtension方法保持一致
     *
     * @param extension 文件扩展名（含或不含点号）
     * @return MIME类型
     */
    public static String getMimeTypeFromExtension(String extension) {
        // 确保扩展名包含点号
        if (!extension.startsWith(".")) {
            extension = "." + extension;
        }

        // 根据扩展名映射MIME类型
        switch (extension.toLowerCase()) {
            // 文本文档
            case ".pdf":
                return "application/pdf";
            case ".doc":
                return "application/msword";
            case ".docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case ".odt":
                return "application/vnd.oasis.opendocument.text";
            case ".rtf":
                return "application/rtf";
            case ".txt":
                return "text/plain";
            case ".md":
                return "text/markdown";
            case ".html":
            case ".htm":
                return "text/html";
            case ".xml":
                return "application/xml";
            case ".mht":
                return "application/x-mht";
            case ".xps":
                return "application/vnd.ms-xpsdocument";
            case ".djvu":
                return "image/vnd.djvu";

            // 电子表格
            case ".xls":
                return "application/vnd.ms-excel";
            case ".xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case ".ods":
                return "application/vnd.oasis.opendocument.spreadsheet";
            case ".csv":
                return "text/csv";
            case ".tsv":
                return "text/tab-separated-values";

            // 演示文档
            case ".ppt":
                return "application/vnd.ms-powerpoint";
            case ".pptx":
                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            case ".odp":
                return "application/vnd.oasis.opendocument.presentation";

            // 图片
            case ".jpg":
            case ".jpeg":
                return "image/jpeg";
            case ".png":
                return "image/png";
            case ".gif":
                return "image/gif";
            case ".svg":
                return "image/svg+xml";
            case ".bmp":
                return "image/bmp";
            case ".tiff":
            case ".tif":
                return "image/tiff";
            case ".webp":
                return "image/webp";

            // 压缩文件
            case ".zip":
                return "application/zip";
            case ".rar":
                return "application/x-rar-compressed";
            case ".7z":
                return "application/x-7z-compressed";
            case ".gz":
                return "application/gzip";
            case ".tar":
                return "application/x-tar";

            // 音频文件
            case ".mp3":
                return "audio/mpeg";
            case ".wav":
                return "audio/wav";
            case ".ogg":
                return "audio/ogg";
            case ".aac":
                return "audio/aac";

            // 视频文件
            case ".mp4":
                return "video/mp4";
            case ".mpeg":
            case ".mpg":
                return "video/mpeg";
            case ".mov":
                return "video/quicktime";
            case ".avi":
                return "video/x-msvideo";
            case ".webm":
                return "video/webm";

            default:
                return "application/octet-stream";
        }
    }


    /**
     * 检查文件是否是支持的类型
     * 与前端页面上传支持的类型保持一致
     *
     * @param fileName 文件名
     * @return 如果是支持的类型则返回true
     */
    public static boolean isSupportedFileType(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        // 支持的文件类型列表
        return Arrays.asList(
                // 文档类型
                ".pdf", ".docx", ".doc", ".md", ".txt", ".html", ".htm", ".rtf", ".odt", ".xml", ".mht", ".xps", ".djvu",
                // 电子表格
                ".xlsx", ".xls", ".csv", ".ods", ".tsv",
                // 演示文档
                ".pptx", ".ppt", ".odp",
                // json
                ".json"
                // 注意：如果需要支持图片、压缩文件、音频或视频文件，请在此处添加
        ).contains(extension);
    }

    /**
     * 获取文件扩展名（包含点号）
     *
     * @param fileName 文件名
     * @return 文件扩展名
     */
    public static String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(lastDotIndex);
        }
        return "";
    }
}
