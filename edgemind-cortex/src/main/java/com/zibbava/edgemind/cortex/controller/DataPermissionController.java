package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zibbava.edgemind.cortex.dto.ApiResponse;
import com.zibbava.edgemind.cortex.entity.DataPermission;
import com.zibbava.edgemind.cortex.service.DataPermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 数据权限控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/system/data-permission")
@RequiredArgsConstructor
@Validated
public class DataPermissionController {

    private final DataPermissionService dataPermissionService;

    /**
     * 根据权限ID获取数据权限配置
     */
    @GetMapping("/permission/{permissionId}")
    @SaCheckPermission("permission:manage:list")
    public ResponseEntity<ApiResponse<List<DataPermission>>> getDataPermissionsByPermissionId(@PathVariable Long permissionId) {
        List<DataPermission> result = dataPermissionService.getDataPermissionsByPermissionId(permissionId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 根据角色ID获取数据权限配置
     */
    @GetMapping("/role/{roleId}")
    @SaCheckPermission("role:manage:list")
    public ResponseEntity<ApiResponse<List<DataPermission>>> getDataPermissionsByRoleId(@PathVariable Long roleId) {
        List<DataPermission> result = dataPermissionService.getDataPermissionsByRoleId(roleId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 根据用户ID获取数据权限配置
     */
    @GetMapping("/user/{userId}")
    @SaCheckPermission("user:manage:list")
    public ResponseEntity<ApiResponse<List<DataPermission>>> getDataPermissionsByUserId(@PathVariable Long userId) {
        List<DataPermission> result = dataPermissionService.getDataPermissionsByUserId(userId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 配置权限的数据权限
     */
    @PostMapping("/configure")
    @SaCheckPermission("permission:manage:update")
    public ResponseEntity<ApiResponse<DataPermission>> configureDataPermission(
            @RequestParam Long permissionId,
            @RequestParam DataPermission.DataScope dataScope,
            @RequestParam(required = false) List<Long> deptIds,
            @RequestParam(required = false) String filterSql) {
        
        DataPermission result = dataPermissionService.configureDataPermission(permissionId, dataScope, deptIds, filterSql);
        return ResponseEntity.ok(ApiResponse.success("数据权限配置成功", result));
    }

    /**
     * 删除权限的数据权限配置
     */
    @DeleteMapping("/permission/{permissionId}")
    @SaCheckPermission("permission:manage:update")
    public ResponseEntity<ApiResponse<Void>> removeDataPermissionByPermissionId(@PathVariable Long permissionId) {
        dataPermissionService.removeDataPermissionByPermissionId(permissionId);
        return ResponseEntity.ok(ApiResponse.success("数据权限配置删除成功"));
    }

    /**
     * 获取用户可访问的部门ID列表
     */
    @GetMapping("/accessible-depts/{userId}")
    @SaCheckPermission("user:manage:list")
    public ResponseEntity<ApiResponse<List<Long>>> getAccessibleDeptIds(@PathVariable Long userId) {
        List<Long> result = dataPermissionService.getAccessibleDeptIds(userId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 检查用户是否可以访问指定部门的数据
     */
    @GetMapping("/check-dept-access")
    @SaCheckPermission("user:manage:list")
    public ResponseEntity<ApiResponse<Boolean>> canAccessDeptData(@RequestParam Long userId, @RequestParam Long deptId) {
        boolean result = dataPermissionService.canAccessDeptData(userId, deptId);
        return ResponseEntity.ok(ApiResponse.success("检查完成", result));
    }

    /**
     * 构建用户的数据权限过滤SQL
     */
    @GetMapping("/filter-sql")
    @SaCheckPermission("user:manage:list")
    public ResponseEntity<ApiResponse<String>> buildDataPermissionSql(@RequestParam Long userId, @RequestParam String resourceType) {
        String result = dataPermissionService.buildDataPermissionSql(userId, resourceType);
        return ResponseEntity.ok(ApiResponse.success("构建成功", result));
    }
}
