package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.DocumentChunk;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 文档分片表的MyBatis Mapper接口
 */
@Mapper
public interface DocumentChunkMapper extends BaseMapper<DocumentChunk> {

    /**
     * 根据文档ID查询所有分片ID
     * 
     * @param documentId 文档ID
     * @return 分片ID列表
     */
    @Select("SELECT chunk_id FROM kb_document_chunks WHERE document_id = #{documentId}")
    List<String> findChunkIdsByDocumentId(@Param("documentId") String documentId);
    
    /**
     * 根据节点ID查询所有分片ID
     * 
     * @param nodeId 节点ID
     * @return 分片ID列表
     */
    @Select("SELECT chunk_id FROM kb_document_chunks WHERE node_id = #{nodeId}")
    List<String> findChunkIdsByNodeId(@Param("nodeId") String nodeId);
} 