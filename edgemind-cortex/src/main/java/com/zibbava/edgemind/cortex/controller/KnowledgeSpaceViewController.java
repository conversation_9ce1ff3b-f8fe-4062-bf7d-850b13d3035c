package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 知识库空间管理页面控制器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/knowledge")
public class KnowledgeSpaceViewController {
    
    /**
     * 知识库空间管理页面
     * 使用OR模式，只要有任一权限即可访问
     */
    @GetMapping("/space-management")
    @SaCheckPermission(value = {"knowledge:space:create", "knowledge:space:edit", "knowledge:space:delete"}, mode = SaMode.OR)
    public String spaceManagement() {
        return "knowledge/space-management";
    }
}
