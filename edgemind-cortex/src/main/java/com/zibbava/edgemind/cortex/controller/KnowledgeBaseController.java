package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import com.zibbava.edgemind.cortex.common.Result;
import com.zibbava.edgemind.cortex.common.enums.NodeType;
import com.zibbava.edgemind.cortex.common.enums.ResultCode;
import com.zibbava.edgemind.cortex.common.enums.VectorStatus;
import com.zibbava.edgemind.cortex.common.exception.BusinessException;
import com.zibbava.edgemind.cortex.dto.knowledgebase.KnowledgeNodeDto;
import com.zibbava.edgemind.cortex.dto.knowledgebase.NodeCreateRequest;
import com.zibbava.edgemind.cortex.dto.knowledgebase.NodeUpdateRequest;
import com.zibbava.edgemind.cortex.entity.KnowledgeDocument;
import com.zibbava.edgemind.cortex.entity.KnowledgeNode;
import com.zibbava.edgemind.cortex.entity.KnowledgeSpace;
import com.zibbava.edgemind.cortex.service.KnowledgeBaseService;
import com.zibbava.edgemind.cortex.service.DocumentViewerService;
import com.zibbava.edgemind.cortex.util.FileUtils;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriUtils;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

/**
 * 知识库管理 API 控制器。
 * 提供对知识空间、节点（文件夹/文件）以及文档上传和开源文档查看器集成的 RESTful 接口。
 */
@RestController
@RequestMapping("/api/knowledgebase")
@RequiredArgsConstructor
@Slf4j
public class KnowledgeBaseController {

    private final KnowledgeBaseService knowledgeBaseService;
    private final FileUtils fileUtils;
    private final DocumentViewerService documentViewerService;

    @Value("${run.env:dev}")
    private String runEnv;

    /**
     * 获取当前用户可访问的知识空间列表（基于角色权限）。
     * GET /api/knowledgebase/spaces
     *
     * @return 知识空间列表。
     */
    @GetMapping("/spaces")
    public Result<List<KnowledgeSpace>> getSpaces() {
        Long userId = StpUtil.getLoginIdAsLong();
        List<KnowledgeSpace> spaces = knowledgeBaseService.getAccessibleSpaces(userId);

        // 记录用户访问的空间数量，用于调试
        log.debug("用户 {} 可访问 {} 个知识空间", userId, spaces.size());

        return Result.success(spaces);
    }

    /**
     * 获取或创建知识空间。
     * 根据类型参数检查是否存在相应知识空间，如不存在则创建后返回。
     * GET /api/knowledgebase/spaces/ensure?type=team|private
     *
     * @param type 知识空间类型，"team"表示团队空间，"private"表示私人空间
     * @return 获取或创建的知识空间
     */
    @GetMapping("/spaces/ensure")
    public Result<KnowledgeSpace> ensureKnowledgeSpace(@RequestParam("type") String type) {
        if (!"team".equalsIgnoreCase(type) && !"private".equalsIgnoreCase(type)) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "知识空间类型参数无效，必须为'team'或'private'");
        }

        Long userId = StpUtil.getLoginIdAsLong();
        boolean isPrivate = "private".equalsIgnoreCase(type);
        KnowledgeSpace space = knowledgeBaseService.getOrCreateKnowledgeSpace(userId, isPrivate);
        return Result.success(space);
    }

    /**
     * 获取用户可访问的所有知识空间
     * GET /api/knowledgebase/spaces/accessible
     *
     * @return 用户可访问的知识空间列表
     */
    @GetMapping("/spaces/accessible")
    public Result<List<KnowledgeSpace>> getAccessibleSpaces() {
        Long userId = StpUtil.getLoginIdAsLong();
        List<KnowledgeSpace> spaces = knowledgeBaseService.getAccessibleSpaces(userId);
        return Result.success(spaces);
    }

    /**
     * 获取指定知识空间的节点树。
     * GET /api/knowledgebase/spaces/{spaceId}/tree
     *
     * @param spaceId 知识空间 ID。
     * @return 包含层级结构的节点 DTO 列表。
     */
    @GetMapping("/spaces/{spaceId}/tree")
    public Result<List<KnowledgeNodeDto>> getSpaceTree(@PathVariable String spaceId) {
        // 权限检查在 Service 层进行
        List<KnowledgeNodeDto> tree = knowledgeBaseService.getSpaceTree(spaceId);
        return Result.success(tree);
    }

    /**
     * 创建知识库节点 (文件夹或文件)。
     * POST /api/knowledgebase/nodes
     *
     * @param request 包含节点信息的请求体。
     * @return 创建成功的节点 DTO。
     */
    @PostMapping("/nodes")
    public Result<KnowledgeNodeDto> createNode(@Valid @RequestBody NodeCreateRequest request) {
        if ("demo".equals(runEnv)) {
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "演示环境不支持创建节点");
        }
        // 权限检查在 Service 层进行
        KnowledgeNodeDto createdNode = knowledgeBaseService.createNode(request);
        return Result.success("节点创建成功", createdNode);
    }

    /**
     * 更新知识库节点（支持重命名和移动节点）。
     * PUT /api/knowledgebase/nodes/{nodeId}
     *
     * @param nodeId  要更新的节点 ID。
     * @param request 包含新名称和可选的新父节点ID的请求体。
     * @return 更新后的节点 DTO。
     */
    @PutMapping("/nodes/{nodeId}")
    public Result<KnowledgeNodeDto> updateNode(@PathVariable String nodeId,
                                               @Valid @RequestBody NodeUpdateRequest request) {
        if ("demo".equals(runEnv)) {
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "演示环境不支持修改节点");
        }
        // 权限检查在 Service 层进行
        KnowledgeNodeDto updatedNode = knowledgeBaseService.updateNode(nodeId, request);
        return Result.success("节点更新成功", updatedNode);
    }

    /**
     * 删除知识库节点（文件夹或文件）。
     * DELETE /api/knowledgebase/nodes/{nodeId}
     * 会触发关联向量的异步删除。
     *
     * @param nodeId 要删除的节点 ID。
     * @return 成功结果。
     */
    @DeleteMapping("/nodes/{nodeId}")
    public Result<Void> deleteNode(@PathVariable String nodeId) {
        if ("demo".equals(runEnv)) {
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "演示环境不支持删除节点");
        }
        // 权限检查在 Service 层进行
        knowledgeBaseService.deleteNode(nodeId);
        return Result.success("节点删除成功", null);
    }

    /**
     * 上传文档内容到指定的文件节点。
     * POST /api/knowledgebase/nodes/{nodeId}/upload
     * 会触发异步索引任务，支持混合检索（Dense + BM25）。
     *
     * @param nodeId 要上传到的文件节点 ID。
     * @param file   上传的文件 (multipart/form-data)。
     * @return 包含操作结果信息的响应体。
     */
    @PostMapping("/nodes/{nodeId}/upload")
    public Result<Map<String, Object>> uploadDocument(@PathVariable String nodeId,
                                                      @RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            throw new BusinessException(ResultCode.FILE_FORMAT_ERROR, "上传文件不能为空");
        }
        if ("demo".equals(runEnv)) {
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "演示环境不支持文件上传");
        }

        try {
            log.info("📁 开始上传文档到节点: {}, 文件名: {}, 大小: {} bytes", 
                    nodeId, file.getOriginalFilename(), file.getSize());
            
            // 权限检查在 Service 层进行
            KnowledgeDocument document = knowledgeBaseService.uploadDocument(nodeId, file, false);

            // 返回成功信息及文档基本状态
            Map<String, Object> data = Map.of(
                    "documentId", document.getDocumentId(),
                    "status", document.getVectorStatus(), // 返回当前状态 (可能是 PENDING)
                    "message", "文件上传成功，正在进行智能索引处理（支持混合检索：语义搜索 + 关键词匹配）"
            );

            log.info("✅ 文档上传成功: documentId={}, 状态={}", 
                    document.getDocumentId(), document.getVectorStatus());

            return Result.success("文件上传成功并已触发混合检索索引", data);
        } catch (IOException e) {
            log.error("文件上传IO失败: nodeId={}, error={}", nodeId, e.getMessage(), e);
            throw new BusinessException(ResultCode.FILE_UPLOAD_ERROR, "文件保存失败: " + e.getMessage(), e);
        }
        // 其他异常由全局异常处理器处理
    }

    /**
     * 获取指定文件节点的文档查看器配置。
     * GET /api/knowledgebase/nodes/{nodeId}/viewer-config
     *
     * @param nodeId 文件节点 ID。
     * @return 包含文档查看器配置的对象。
     */
    @GetMapping("/nodes/{nodeId}/viewer-config")
    public Result<Map<String, Object>> getDocumentViewerConfig(@PathVariable String nodeId) {
        log.info("请求获取节点 {} 的文档查看器配置", nodeId);
        Long userId = StpUtil.getLoginIdAsLong();
        String userName = StpUtil.getLoginIdAsString();

        try {
            // 委托给服务层处理业务逻辑
            Map<String, Object> configMap = documentViewerService.getDocumentViewerConfig(nodeId, userId, userName);
            return Result.success(configMap);
        } catch (BusinessException e) {
            log.warn("获取节点 {} 的文档查看器配置失败: {}", nodeId, e.getMessage());
            return Result.error(e.getResultCode(), e.getMessage());
        } catch (Exception e) {
            log.error("获取节点 {} 的文档查看器配置时发生意外错误: {}", nodeId, e.getMessage(), e);
            return Result.error(ResultCode.SYSTEM_ERROR, "获取文档配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的文件类型列表。
     * GET /api/knowledgebase/supported-file-types
     *
     * @return 支持的文件类型映射。
     */
    @GetMapping("/supported-file-types")
    public Result<Map<String, String>> getSupportedFileTypes() {
        log.debug("请求获取支持的文件类型列表");
        try {
            Map<String, String> supportedTypes = documentViewerService.getSupportedFileTypes();
            return Result.success(supportedTypes);
        } catch (Exception e) {
            log.error("获取支持的文件类型列表时发生错误: {}", e.getMessage(), e);
            return Result.error(ResultCode.SYSTEM_ERROR, "获取文件类型列表失败: " + e.getMessage());
        }
    }

    /**
     * 下载指定节点的文件内容。
     * GET /api/knowledgebase/nodes/{nodeId}/download
     * 此接口主要供文档查看器调用以下载文档。
     *
     * @param nodeId 文件节点 ID。
     * @return 包含文件内容的 ResponseEntity<Resource>。
     */
    @SaIgnore // 忽略安全检查，允许文档查看器直接访问
    @GetMapping("/nodes/{nodeId}/download")
    public ResponseEntity<Resource> downloadFile(@PathVariable String nodeId) {
        log.debug("请求下载节点 {} 的文件", nodeId);

        try {
            // 1. 获取节点和文档信息
            KnowledgeNode node = knowledgeBaseService.findNodeById(nodeId);
            if (node == null) {
                log.warn("下载请求失败：节点 {} 不存在", nodeId);
                return ResponseEntity.notFound().build();
            }
            if (node.getType() != NodeType.FILE) {
                log.warn("下载请求失败：节点 {} 不是文件类型", nodeId);
                return ResponseEntity.badRequest().body(null); // Bad request, not a file
            }

            // 2. 文档查看器访问时不检查权限
            // knowledgeBaseService.checkAccess(node.getSpaceId(), userId);

            KnowledgeDocument document = knowledgeBaseService.findDocumentByNodeId(nodeId);
            if (document == null) {
                log.error("下载请求失败：节点 {} 关联的文档记录丢失", nodeId);
                return ResponseEntity.notFound().build(); // Document metadata missing
            }

            // 3. 定位文件并创建资源
            try {
                // 构建节点路径
                List<KnowledgeNode> nodePath = fileUtils.getNodePath(nodeId);
                if (nodePath.isEmpty()) {
                    log.error("无法获取节点路径: {}", nodeId);
                    return ResponseEntity.notFound().build();
                }

                // 获取存储路径
                FileUtils.StoragePath storagePath = fileUtils.getStoragePathForNode(nodeId);
                String baseDir = storagePath.getFullPath();

                // 构建父目录路径
                Path basePath = Paths.get(baseDir);
                Path parentPath = basePath;

                // 构建目录路径，但不包含当前节点
                for (int i = 0; i < nodePath.size() - 1; i++) {
                    String safeName = fileUtils.sanitizeFileName(nodePath.get(i).getName());
                    parentPath = parentPath.resolve(safeName);
                }

                // 获取当前节点名称
                String nodeName = fileUtils.sanitizeFileName(node.getName());

                // 构建完整文件路径
                Path filePath = parentPath.resolve(nodeName);

                // 检查文件是否存在
                if (!Files.exists(filePath) || !Files.isRegularFile(filePath) || !Files.isReadable(filePath)) {
                    log.error("文件不存在或不可读: {}", filePath);
                    throw new FileNotFoundException("文件未找到或不可读: " + filePath);
                }

                // 创建资源
                Resource resource = new UrlResource(filePath.toUri());

                log.debug("成功定位文件: {}", filePath);

                // 4. 确定 Content-Type
                String contentType = null;
                try {
                    contentType = Files.probeContentType(filePath);
                } catch (IOException e) {
                    log.warn("无法探测文件 {} 的 Content-Type: {}", filePath, e.getMessage());
                }
                if (contentType == null) {
                    contentType = StringUtils.hasText(document.getMimeType())
                            ? document.getMimeType()
                            : MediaType.APPLICATION_OCTET_STREAM_VALUE; // Default binary type
                }

                // 5. 构建响应
                HttpHeaders headers = new HttpHeaders();
                // Set Content-Disposition to suggest filename, handle potential special characters
                String encodedFilename = UriUtils.encode(node.getName(), StandardCharsets.UTF_8);
                headers.add(HttpHeaders.CONTENT_DISPOSITION, "inline; filename*=\"UTF-8''" + encodedFilename + "\""); // Use inline for preview

                log.info("准备提供节点 {} 的文件下载: {}, Content-Type: {}", nodeId, filePath, contentType);

                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .headers(headers)
                        // .contentLength(resource.contentLength()) // Let Spring handle content length
                        .body(resource);
            } catch (IOException e) {
                log.error("定位或读取文件时出错: {}", e.getMessage(), e);
                return ResponseEntity.status(500).build();

            }
        } catch (Exception e) {
            log.error("下载节点 {} 文件时发生未知错误: {}", nodeId, e.getMessage(), e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 获取指定文件节点的文档向量化状态。
     * GET /api/knowledgebase/nodes/{nodeId}/document-status
     *
     * @param nodeId 文件节点 ID。
     * @return 包含文档向量状态的响应体。
     */
    @GetMapping("/nodes/{nodeId}/document-status")
    public Result<Map<String, String>> getDocumentStatus(@PathVariable String nodeId) {
        try {
            VectorStatus status = knowledgeBaseService.getDocumentVectorStatus(nodeId);
            return Result.success(Map.of("nodeId", nodeId, "status", status.name()));
        } catch (BusinessException e) {
            log.warn("获取节点 {} 文档状态失败: {}", nodeId, e.getMessage());
            return Result.error(e.getResultCode(), e.getMessage());
        } catch (Exception e) {
            log.error("获取节点 {} 文档状态时发生意外错误: {}", nodeId, e.getMessage(), e);
            return Result.error(ResultCode.SYSTEM_ERROR, "获取文档状态失败: " + e.getMessage());
        }
    }

    /**
     * 重新触发指定文件节点的向量化处理。
     * POST /api/knowledgebase/nodes/{nodeId}/reparse
     *
     * @param nodeId 文件节点 ID。
     * @return 包含操作结果及文档新状态的响应体。
     */
    @PostMapping("/nodes/{nodeId}/reparse")
    public Result<Map<String, Object>> reparseDocument(@PathVariable String nodeId) {
        if ("demo".equals(runEnv)) {
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "演示环境不支持重新解析文档");
        }
        log.info("请求重新解析节点 {} 的文档", nodeId);
        try {
            KnowledgeDocument document = knowledgeBaseService.reparseDocument(nodeId);
            Map<String, Object> data = Map.of(
                    "documentId", document.getDocumentId(),
                    "status", document.getVectorStatus().name() // 返回 PENDING 状态
            );
            return Result.success("重新解析任务已触发", data);
        } catch (BusinessException e) {
            log.warn("重新解析节点 {} 失败: {}", nodeId, e.getMessage());
            return Result.error(e.getResultCode(), e.getMessage());
        } catch (Exception e) {
            log.error("重新解析节点 {} 时发生意外错误: {}", nodeId, e.getMessage(), e);
            return Result.error(ResultCode.SYSTEM_ERROR, "重新解析失败: " + e.getMessage());
        }
    }
}