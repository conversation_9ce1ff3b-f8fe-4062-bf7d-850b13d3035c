package com.zibbava.edgemind.cortex.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 同步状态响应数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncStatusResponseDTO {
    
    /**
     * 同步状态
     * 0: 未同步
     * 1: 同步中
     * 2: 同步完成
     * 3: 同步失败
     */
    private Integer syncStatus;
    
    /**
     * 同步消息
     */
    private String syncMessage;
    
    /**
     * 最后同步时间
     */
    private LocalDateTime lastSyncTime;
}
