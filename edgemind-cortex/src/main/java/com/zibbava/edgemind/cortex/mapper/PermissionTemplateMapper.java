package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.PermissionTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 权限模板Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PermissionTemplateMapper extends BaseMapper<PermissionTemplate> {
    
    /**
     * 根据资源类型查询权限模板
     */
    @Select("SELECT * FROM sys_permission_template WHERE resource_type = #{resourceType}")
    List<PermissionTemplate> findByResourceType(@Param("resourceType") String resourceType);
}
