package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.KnowledgeNode;
import com.zibbava.edgemind.cortex.entity.KnowledgeNodeClosure;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 知识库节点闭包表的MyBatis Mapper接口
 * 提供了高效的树形结构查询功能
 */
@Mapper
public interface KnowledgeNodeClosureMapper extends BaseMapper<KnowledgeNodeClosure> {

    /**
     * 查询指定节点的所有后代节点
     *
     * @param ancestorId 祖先节点ID
     * @return 所有后代节点列表
     */
    @Select("SELECT n.* FROM kb_knowledge_nodes n " +
            "JOIN kb_knowledge_node_closure c ON n.node_id = c.descendant_id " +
            "WHERE c.ancestor_id = #{ancestorId} AND c.depth > 0 " +
            "ORDER BY c.depth, n.name")
    List<KnowledgeNode> findDescendants(@Param("ancestorId") String ancestorId);

    /**
     * 查询指定节点的所有祖先节点
     *
     * @param descendantId 后代节点ID
     * @return 所有祖先节点列表
     */
    @Select("SELECT n.* FROM kb_knowledge_nodes n " +
            "JOIN kb_knowledge_node_closure c ON n.node_id = c.ancestor_id " +
            "WHERE c.descendant_id = #{descendantId} AND c.depth > 0 " +
            "ORDER BY c.depth DESC")
    List<KnowledgeNode> findAncestors(@Param("descendantId") String descendantId);

    /**
     * 查询指定节点的直接子节点
     *
     * @param parentId 父节点ID
     * @return 直接子节点列表
     */
    @Select("SELECT n.* FROM kb_knowledge_nodes n " +
            "JOIN kb_knowledge_node_closure c ON n.node_id = c.descendant_id " +
            "WHERE c.ancestor_id = #{parentId} AND c.depth = 1 " +
            "ORDER BY n.name")
    List<KnowledgeNode> findDirectChildren(@Param("parentId") String parentId);

    /**
     * 查询指定空间的根节点列表
     *
     * @param spaceId 空间ID
     * @return 根节点列表
     */
    @Select("SELECT n.* FROM kb_knowledge_nodes n " +
            "WHERE n.space_id = #{spaceId} AND n.parent_node_id IS NULL " +
            "ORDER BY n.name")
    List<KnowledgeNode> findRootNodes(@Param("spaceId") String spaceId);

    /**
     * 查询指定空间的所有节点（分页）
     *
     * @param spaceId 空间ID
     * @param ancestorId 祖先节点ID（可选，如果提供则只查询其后代）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 节点列表
     */
    @Select("<script>" +
            "SELECT n.* FROM kb_knowledge_nodes n " +
            "<if test='ancestorId != null'>" +
            "JOIN kb_knowledge_node_closure c ON n.node_id = c.descendant_id " +
            "WHERE n.space_id = #{spaceId} AND c.ancestor_id = #{ancestorId} " +
            "</if>" +
            "<if test='ancestorId == null'>" +
            "WHERE n.space_id = #{spaceId} " +
            "</if>" +
            "ORDER BY n.name " +
            "LIMIT #{limit} OFFSET #{offset}" +
            "</script>")
    List<KnowledgeNode> findNodesPaged(
            @Param("spaceId") String spaceId,
            @Param("ancestorId") String ancestorId,
            @Param("offset") int offset,
            @Param("limit") int limit);

    /**
     * 查询指定空间的节点总数
     *
     * @param spaceId 空间ID
     * @param ancestorId 祖先节点ID（可选，如果提供则只计算其后代）
     * @return 节点总数
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM kb_knowledge_nodes n " +
            "<if test='ancestorId != null'>" +
            "JOIN kb_knowledge_node_closure c ON n.node_id = c.descendant_id " +
            "WHERE n.space_id = #{spaceId} AND c.ancestor_id = #{ancestorId} " +
            "</if>" +
            "<if test='ancestorId == null'>" +
            "WHERE n.space_id = #{spaceId} " +
            "</if>" +
            "</script>")
    int countNodes(
            @Param("spaceId") String spaceId,
            @Param("ancestorId") String ancestorId);
}
