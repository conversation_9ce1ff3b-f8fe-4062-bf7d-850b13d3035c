package com.zibbava.edgemind.cortex.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 个人库存储路径数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PersonalStoragePathDTO {

    /**
     * 个人库存储路径
     */
    @NotBlank(message = "个人库存储路径不能为空")
    private String personalStoragePath;
}
