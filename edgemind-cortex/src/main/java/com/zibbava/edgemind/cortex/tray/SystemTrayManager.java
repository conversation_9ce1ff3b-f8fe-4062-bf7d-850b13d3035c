package com.zibbava.edgemind.cortex.tray;

import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.net.URL;

/**
 * Windows系统托盘管理器
 *
 * <AUTHOR>
 */
public class SystemTrayManager {

    private static SystemTray systemTray;
    private static TrayIcon trayIcon;
    private static Object applicationContext;
    
    /**
     * 初始化系统托盘
     */
    public static void initialize(Object context) {
        applicationContext = context;

        if (!System.getProperty("os.name", "").toLowerCase().contains("windows")) {
            return;
        }

        if (!SystemTray.isSupported()) {
            return;
        }

        try {
            System.setProperty("file.encoding", "UTF-8");
            createSystemTray();
        } catch (Exception e) {
            System.err.println("系统托盘初始化失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建系统托盘
     */
    private static void createSystemTray() throws AWTException {
        systemTray = SystemTray.getSystemTray();

        Image iconImage = loadIcon();
        trayIcon = new TrayIcon(iconImage, "端智AI助手");
        trayIcon.setImageAutoSize(true);
        trayIcon.setToolTip("端智AI助手");

        trayIcon.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseReleased(java.awt.event.MouseEvent e) {
                if (e.isPopupTrigger() || e.getButton() == java.awt.event.MouseEvent.BUTTON3) {
                    showJPopupMenu();
                }
            }
        });

        systemTray.add(trayIcon);
    }

    /**
     * 显示右键菜单
     */
    private static void showJPopupMenu() {
        Point mouseLocation = MouseInfo.getPointerInfo().getLocation();

        JDialog popupDialog = new JDialog();
        popupDialog.setUndecorated(true);
        popupDialog.setSize(1, 1);
        popupDialog.setLocation(mouseLocation.x, mouseLocation.y);

        JPopupMenu popup = new JPopupMenu() {
            @Override
            public void firePopupMenuWillBecomeInvisible() {
                super.firePopupMenuWillBecomeInvisible();
                popupDialog.setVisible(false);
                popupDialog.dispose();
            }
        };

        JMenuItem exitItem = new JMenuItem("退出");
        exitItem.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        exitItem.setBackground(Color.WHITE);
        exitItem.setBorder(BorderFactory.createEmptyBorder(8, 16, 8, 16));

        exitItem.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent e) {
                exitItem.setBackground(new Color(240, 240, 240));
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent e) {
                exitItem.setBackground(Color.WHITE);
            }
        });

        exitItem.addActionListener(e -> {
            popup.setVisible(false);
            exitApplication();
        });

        popup.add(exitItem);
        popup.setBackground(Color.WHITE);
        popup.setBorder(BorderFactory.createLineBorder(new Color(200, 200, 200), 1));

        popupDialog.setVisible(true);
        popup.show(popupDialog, 0, 0);
    }
    

    
    /**
     * 加载图标
     */
    private static Image loadIcon() {
        try {
            URL iconUrl = SystemTrayManager.class.getResource("/static/images/ai-assistant-logo.png");
            if (iconUrl != null) {
                return new ImageIcon(iconUrl).getImage();
            }
        } catch (Exception ignored) {
        }
        return createDefaultIcon();
    }
    
    /**
     * 创建默认图标
     */
    private static Image createDefaultIcon() {
        BufferedImage image = new BufferedImage(16, 16, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setColor(new Color(0, 120, 215)); // Windows蓝色
        g2d.fillOval(0, 0, 16, 16);
        g2d.setColor(Color.WHITE);
        g2d.setFont(new Font("Microsoft YaHei", Font.BOLD, 10));
        
        FontMetrics fm = g2d.getFontMetrics();
        String text = "端";
        int textWidth = fm.stringWidth(text);
        int textHeight = fm.getHeight();
        g2d.drawString(text, (16 - textWidth) / 2, (16 - textHeight) / 2 + fm.getAscent());
        
        g2d.dispose();
        return image;
    }
    
    /**
     * 退出应用程序
     */
    private static void exitApplication() {
        SwingUtilities.invokeLater(() -> {
            cleanup();
            if (applicationContext != null) {
                try {
                    applicationContext.getClass().getMethod("close").invoke(applicationContext);
                } catch (Exception ignored) {
                }
            }
            System.exit(0);
        });
    }
    
    /**
     * 清理资源
     */
    public static void cleanup() {
        try {
            if (systemTray != null && trayIcon != null) {
                systemTray.remove(trayIcon);
            }
        } catch (Exception ignored) {
        }
    }
}
