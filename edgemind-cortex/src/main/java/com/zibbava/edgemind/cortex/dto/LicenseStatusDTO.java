package com.zibbava.edgemind.cortex.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 许可证状态数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LicenseStatusDTO {

    /**
     * 授权状态：0-未授权，1-已授权
     */
    private Integer status;

    /**
     * 硬件指纹
     */
    private String hardwareFingerprint;

    /**
     * 系统唯一标识
     */
    private String systemIdentifier;

    /**
     * 授权类型：trial-试用版，standard-标准版，professional-专业版，enterprise-企业版
     */
    private String licenseType;

    /**
     * 激活时间
     */
    private LocalDateTime activatedTime;

    /**
     * 过期时间，null表示永不过期
     */
    private LocalDateTime expireTime;

    /**
     * 是否过期
     */
    private Boolean isExpired;

    /**
     * 剩余天数，-1表示永不过期
     */
    private Long remainingDays;
}
