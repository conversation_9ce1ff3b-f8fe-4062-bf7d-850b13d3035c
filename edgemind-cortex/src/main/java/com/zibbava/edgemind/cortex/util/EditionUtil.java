package com.zibbava.edgemind.cortex.util;

import com.zibbava.edgemind.cortex.config.EditionConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 版本工具类
 * 提供给Thymeleaf模板使用的版本判断方法
 */
@Component("editionUtil")
public class EditionUtil {

    @Autowired
    private EditionConfig editionConfig;

    /**
     * 判断是否为企业版
     */
    public boolean isEnterprise() {
        return editionConfig.isEnterprise();
    }

    /**
     * 判断是否为个人版
     */
    public boolean isPersonal() {
        return editionConfig.isPersonal();
    }

    /**
     * 获取版本类型
     */
    public String getType() {
        return editionConfig.getType();
    }
}
