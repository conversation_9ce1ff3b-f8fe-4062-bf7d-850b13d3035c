package com.zibbava.edgemind.cortex.config;

import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.embedding.onnx.bgesmallzhv15.BgeSmallZhV15EmbeddingModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * BGE中文嵌入模型配置
 * 使用本地ONNX实现，无需外部服务依赖
 */
@Configuration
@Slf4j
public class EmbeddingModelConfig {

    @Value("${embedding.model.type:bge-small-zh-v15}")
    private String modelType;

    @Value("${embedding.model.dimension:512}")
    private Integer modelDimension;

    /**
     * 创建BGE中文嵌入模型Bean
     * 使用本地ONNX实现，启动即可用
     */
    @Bean
    public EmbeddingModel embeddingModel() {
        log.info("🚀 初始化BGE中文嵌入模型: {}", modelType);
        log.info("📊 模型向量维度: {}", modelDimension);

        try {
            // 创建BGE Small Chinese v1.5 嵌入模型
            EmbeddingModel embeddingModel = new BgeSmallZhV15EmbeddingModel();

            log.info("✅ BGE中文嵌入模型初始化成功");
            log.info("🎯 模型特性: 专为中文优化，本地ONNX推理，无需外部服务");

            return embeddingModel;

        } catch (Exception e) {
            log.error("❌ BGE中文嵌入模型初始化失败: {}", e.getMessage(), e);
            throw new RuntimeException("嵌入模型初始化失败", e);
        }
    }

}