package com.zibbava.edgemind.cortex.common.enums;

import lombok.Getter;

/**
 * 统一业务错误码
 * 规则：
 * - 成功: 200
 * - 参数错误: 400-499
 * - 业务错误: 500-599
 * - 系统错误: 600-699
 */
@Getter
public enum ResultCode {

    // 成功
    SUCCESS(200, "操作成功"),

    // 参数错误 400-499
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权或会话已过期"),
    FORBIDDEN(403, "无权访问该资源"),
    RESOURCE_NOT_FOUND(404, "请求的资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    PARAM_MISSING(410, "缺少必要的请求参数"),
    PARAM_INVALID(411, "请求参数无效"),
    REQUEST_TOO_FREQUENT(429, "请求过于频繁，请稍后再试"),
    
    // 业务错误 500-599
    OPERATION_FAILED(500, "操作失败"),
    DATA_ALREADY_EXISTS(501, "数据已存在"),
    DATA_NOT_EXISTS(502, "数据不存在"),
    FILE_UPLOAD_ERROR(510, "文件上传失败"),
    FILE_FORMAT_ERROR(511, "文件格式不支持"),
    FILE_SIZE_EXCEED(512, "文件大小超过限制"),
    FOLDER_NOT_EMPTY(520, "文件夹不为空，请先删除内容"),
    NODE_DUPLICATE_NAME(521, "同一层级下存在同名节点"),
    SPACE_PERMISSION_DENIED(530, "无权访问该知识空间"),
    NODE_PERMISSION_DENIED(531, "无权操作该节点"),
    NODE_TYPE_MISMATCH(532, "节点类型不匹配操作"),
    
    // 系统错误 600-699
    SYSTEM_ERROR(600, "系统内部错误"),
    SERVICE_UNAVAILABLE(601, "服务不可用"),
    DEPENDENCY_SERVICE_ERROR(610, "依赖服务调用失败"),
    DATABASE_ERROR(620, "数据库操作异常"),
    JOB_EXECUTION_ERROR(630, "任务执行错误"),
    UNKNOWN_ERROR(999, "未知错误");

    private final int code;
    private final String message;

    ResultCode(int code, String message) {
        this.code = code;
        this.message = message;
    }
} 