package com.zibbava.edgemind.cortex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zibbava.edgemind.cortex.entity.Permission;
import com.zibbava.edgemind.cortex.mapper.PermissionMapper;
import com.zibbava.edgemind.cortex.mapper.RolePermissionMapper;
import com.zibbava.edgemind.cortex.service.DynamicPermissionService;
import com.zibbava.edgemind.cortex.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 权限服务实现类
 * 
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PermissionServiceImpl extends ServiceImpl<PermissionMapper, Permission> implements PermissionService {

    private final PermissionMapper permissionMapper;
    private final RolePermissionMapper rolePermissionMapper;
    private final DynamicPermissionService dynamicPermissionService;

    @Override
    public List<Permission> findPermissionsByRoleIds(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return new ArrayList<>();
        }
        
        // 获取角色对应的权限ID列表
        List<Long> permissionIds = rolePermissionMapper.selectPermissionIdsByRoleIds(roleIds);
        
        if (CollectionUtils.isEmpty(permissionIds)) {
            return new ArrayList<>();
        }
        
        // 根据权限ID列表查询权限详情
        return permissionMapper.selectBatchIds(permissionIds);
    }

    @Override
    public List<Permission> getPermissionTree() {
        // 确保动态权限已生成
        dynamicPermissionService.ensureDynamicPermissionsGenerated();

        // 获取所有启用且未删除的权限
        LambdaQueryWrapper<Permission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Permission::getStatus, 1)
               .eq(Permission::getDeleted, false)
               .orderByAsc(Permission::getSortOrder, Permission::getId);

        List<Permission> allPermissions = this.list(wrapper);

        // 构建树形结构
        return buildPermissionTree(allPermissions, 0L);
    }

    /**
     * 构建权限树
     */
    private List<Permission> buildPermissionTree(List<Permission> allPermissions, Long parentId) {
        List<Permission> result = new ArrayList<>();
        
        // 按父ID分组
        Map<Long, List<Permission>> permissionMap = allPermissions.stream()
                .collect(Collectors.groupingBy(perm -> perm.getParentId() == null ? 0L : perm.getParentId()));
        
        List<Permission> children = permissionMap.get(parentId);
        if (children != null) {
            for (Permission permission : children) {
                permission.setChildren(buildPermissionTree(allPermissions, permission.getId()));
                result.add(permission);
            }
        }
        
        return result;
    }
}
