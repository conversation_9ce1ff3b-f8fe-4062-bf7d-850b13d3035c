package com.zibbava.edgemind.cortex.dto;

import lombok.Builder;
import lombok.Data;

/**
 * 系统信息响应DTO
 */
@Data
@Builder
public class SystemInfoResponse {
    /**
     * CPU信息
     */
    private CpuInfo cpu;
    
    /**
     * 内存信息
     */
    private MemoryInfo memory;
    
    /**
     * GPU信息
     */
    private GpuInfo gpu;
    
    /**
     * 存储信息
     */
    private StorageInfo storage;
    
    /**
     * 推荐模型大小
     */
    private String recommendedModelSize;
    
    /**
     * CPU信息
     */
    @Data
    @Builder
    public static class CpuInfo {
        private String model;
        private int cores;
        private int threads;
    }
    
    /**
     * 内存信息
     */
    @Data
    @Builder
    public static class MemoryInfo {
        private long totalBytes;
        private long availableBytes;
        private String total;
        private String available;
    }
    
    /**
     * GPU信息
     */
    @Data
    @Builder
    public static class GpuInfo {
        private String model;
        private long memoryBytes;
        private String memory;
        private boolean available;
    }
    
    /**
     * 存储信息
     */
    @Data
    @Builder
    public static class StorageInfo {
        private long totalBytes;
        private long availableBytes;
        private String total;
        private String available;
    }
}
