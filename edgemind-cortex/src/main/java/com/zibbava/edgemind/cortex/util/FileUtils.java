package com.zibbava.edgemind.cortex.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zibbava.edgemind.cortex.common.enums.NodeType;
import com.zibbava.edgemind.cortex.entity.KnowledgeDocument;
import com.zibbava.edgemind.cortex.entity.KnowledgeNode;
import com.zibbava.edgemind.cortex.entity.KnowledgeSpace;
import com.zibbava.edgemind.cortex.mapper.KnowledgeDocumentMapper;
import com.zibbava.edgemind.cortex.mapper.KnowledgeNodeMapper;
import com.zibbava.edgemind.cortex.service.KnowledgeNodeClosureService;
import com.zibbava.edgemind.cortex.service.SystemSettingsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件操作工具类。
 * 提供文件保存、读取、计算哈希值和删除等常用功能。
 */
@Component
@Slf4j
public class FileUtils {

    private KnowledgeNodeMapper nodeMapper;
    private KnowledgeNodeClosureService closureService;
    private KnowledgeDocumentMapper documentMapper;
    private SystemSettingsService systemSettingsService;

    @Autowired
    public void setNodeMapper(KnowledgeNodeMapper nodeMapper) {
        this.nodeMapper = nodeMapper;
    }

    @Autowired
    public void setClosureService(KnowledgeNodeClosureService closureService) {
        this.closureService = closureService;
    }

    @Autowired
    public void setDocumentMapper(KnowledgeDocumentMapper documentMapper) {
        this.documentMapper = documentMapper;
    }

    @Autowired
    public void setSystemSettingsService(@Lazy SystemSettingsService systemSettingsService) {
        this.systemSettingsService = systemSettingsService;
    }


    /**
     * 根据知识空间类型获取存储路径
     * 集中处理知识库和个人库的路径选择逻辑
     *
     * @param space 知识空间实体
     * @return 存储路径对象，包含基础路径和完整路径
     */
    public StoragePath getStoragePathForSpace(KnowledgeSpace space) {
        if (space == null) {
            throw new IllegalArgumentException("知识空间不能为空");
        }

        String baseDir;
        Path basePath;
        Path fullPath;

        if ("PRIVATE".equals(space.getAccessType()) && space.getOwnerUserId() != null) {
            // 个人空间使用个人库存储路径
            baseDir = systemSettingsService.getPersonalKnowledgeStoragePath();
            basePath = Paths.get(baseDir);

            // 添加用户ID子目录
            fullPath = basePath.resolve(space.getOwnerUserId().toString());

            log.debug("使用个人库存储路径: {}", fullPath);
        } else {
            // 其他空间使用知识库存储路径
            baseDir = systemSettingsService.getKnowledgeStoragePath();
            basePath = Paths.get(baseDir);
            fullPath = basePath;

            log.debug("使用知识库存储路径: {}", fullPath);
        }

        // 确保路径存在
        try {
            if (!Files.exists(fullPath)) {
                Files.createDirectories(fullPath);
                log.info("创建存储路径: {}", fullPath);
            }
        } catch (IOException e) {
            log.error("创建存储路径失败: {}", e.getMessage());
            throw new RuntimeException("创建存储路径失败: " + e.getMessage(), e);
        }

        return new StoragePath(baseDir, fullPath.toString());
    }

    /**
     * 根据节点ID获取存储路径
     * 先获取节点所属的空间，然后根据空间类型选择存储路径
     *
     * @param nodeId 节点ID
     * @return 存储路径对象，包含基础路径和完整路径
     */
    public StoragePath getStoragePathForNode(String nodeId) {
        if (!StringUtils.hasText(nodeId)) {
            throw new IllegalArgumentException("节点ID不能为空");
        }

        // 获取节点信息
        KnowledgeNode node = nodeMapper.selectById(nodeId);
        if (node == null) {
            throw new IllegalArgumentException("找不到节点: " + nodeId);
        }

        // 获取知识空间信息
        KnowledgeSpace space = nodeMapper.selectSpaceById(node.getSpaceId());
        if (space == null) {
            throw new IllegalArgumentException("找不到知识空间: " + node.getSpaceId());
        }

        return getStoragePathForSpace(space);
    }

    /**
     * 存储路径对象，包含基础路径和完整路径
     */
    public static class StoragePath {
        private final String baseDir;  // 原始基础目录（不包含用户ID子目录）
        private final String fullPath; // 完整路径（包含用户ID子目录）

        public StoragePath(String baseDir, String fullPath) {
            this.baseDir = baseDir;
            this.fullPath = fullPath;
        }

        public String getBaseDir() {
            return baseDir;
        }

        public String getFullPath() {
            return fullPath;
        }
    }

    /**
     * 获取节点的完整路径（从根节点到当前节点）
     * 使用闭包表查询，提高查询效率
     *
     * @param nodeId 节点ID
     * @return 节点路径列表，从根节点到当前节点
     */
    public List<KnowledgeNode> getNodePath(String nodeId) {
        // 首先获取当前节点
        KnowledgeNode currentNode = nodeMapper.selectById(nodeId);
        if (currentNode == null) {
            log.warn("找不到节点: {}", nodeId);
            return new ArrayList<>();
        }

        // 使用闭包表查询所有祖先节点
        List<KnowledgeNode> ancestors = closureService.getAncestors(nodeId);

        // 创建完整路径（从根到当前节点）
        List<KnowledgeNode> path = new ArrayList<>();

        path.addAll(ancestors);

        // 打印日志以便调试
        if (!ancestors.isEmpty()) {
            log.debug("节点 {} 的祖先节点顺序: {}", nodeId,
                    ancestors.stream().map(KnowledgeNode::getName).collect(java.util.stream.Collectors.joining(", ")));
        }

        // 添加当前节点
        path.add(currentNode);

        // 打印完整路径
        log.debug("节点 {} 的完整路径: {}", nodeId,
                path.stream().map(KnowledgeNode::getName).collect(java.util.stream.Collectors.joining("/")));

        return path;
    }

    /**
     * 根据节点ID构建对应的文件系统路径
     * 直接使用节点名称和层级结构构建路径，不使用节点ID
     *
     * @param baseDir 基础目录
     * @param nodeId  节点ID
     * @return 对应的文件系统路径
     */
    public Path buildNodePath(String baseDir, String nodeId) {
        Path basePath = Paths.get(baseDir);
        List<KnowledgeNode> nodePath = getNodePath(nodeId);

        // 从基础目录开始构建路径，不使用空间ID
        Path currentPath = basePath;

        // 添加每个节点的名称到路径，按照层级结构
        for (KnowledgeNode node : nodePath) {
            // 对节点名称进行安全处理
            String safeName = sanitizeFileName(node.getName());
            currentPath = currentPath.resolve(safeName);
        }

        return currentPath;
    }

    /**
     * 创建文件夹节点对应的物理目录
     *
     * @param nodeId 节点ID
     * @return 创建的目录路径
     * @throws IOException 如果目录创建失败
     */
    public Path createNodeDirectory(String nodeId) throws IOException {
        // 获取存储路径
        StoragePath storagePath = getStoragePathForNode(nodeId);
        String baseDir = storagePath.getFullPath();

        Path nodePath = buildNodePath(baseDir, nodeId);

        if (!Files.exists(nodePath)) {
            Files.createDirectories(nodePath);
            log.info("为节点 {} 创建目录: {}", nodeId, nodePath);
        } else {
            log.info("节点 {} 的目录已存在: {}", nodeId, nodePath);
        }

        return nodePath;
    }

    /**
     * 将上传的文件保存到按文档树结构组织的目录中
     * 使用节点路径结构确定文件位置，不再返回相对路径
     *
     * @param spaceId      知识空间 ID。
     * @param nodeId       知识库节点 ID。
     * @param file         Spring MVC 的 MultipartFile 对象。
     * @param skipIfExists 如果文件已存在，是否跳过而不覆盖。在同步知识库时设置为true可以提高效率。
     * @return 保存后文件的路径对象，如果跳过则返回现有文件路径
     * @throws IOException 如果目录创建或文件写入失败。
     */
    public Path saveFile(String spaceId, String nodeId, MultipartFile file, boolean skipIfExists) throws IOException {
        // 获取当前节点信息
        KnowledgeNode node = nodeMapper.selectById(nodeId);
        if (node == null) {
            throw new IOException("找不到节点: " + nodeId);
        }

        // 获取知识空间信息
        KnowledgeSpace space = nodeMapper.selectSpaceById(spaceId);
        if (space == null) {
            throw new IOException("找不到知识空间: " + spaceId);
        }

        // 获取存储路径
        StoragePath storagePath = getStoragePathForSpace(space);
        String baseDir = storagePath.getFullPath();

        // 直接使用buildNodePath方法构建节点路径
        Path nodePath = buildNodePath(baseDir, nodeId);

        // 确保父目录存在
        if (!Files.exists(nodePath.getParent())) {
            Files.createDirectories(nodePath.getParent());
            log.info("创建节点目录结构: {}", nodePath.getParent());
        }

        // 直接使用节点名称作为文件名（已包含扩展名）
        String fileName = node.getName();
        String safeFileName = fileName;

        // 创建完整的文件路径
        Path filePath = nodePath.getParent().resolve(safeFileName);

        // 处理文件名冲突
        if (Files.exists(filePath)) {
            if (skipIfExists) {
                // 如果设置了跳过已存在文件（同步知识库时）
                log.info("文件已存在，跳过不覆盖: {}", filePath);
                return filePath; // 直接返回现有文件路径，不进行复制
            } else {
                // 正常上传时，覆盖现有文件
                log.warn("文件已存在，将覆盖现有文件: {}", filePath);
            }
        }

        // 复制文件内容
        try (InputStream inputStream = file.getInputStream()) {
            long bytesCopied = Files.copy(inputStream, filePath, StandardCopyOption.REPLACE_EXISTING);
            log.info("文件成功保存至: {}, 大小: {} bytes", filePath.toString(), bytesCopied);
        }

        // 返回文件路径对象
        return filePath;
    }

    /**
     * 清理文件名，移除不安全的字符
     *
     * @param fileName 原始文件名（不含扩展名）
     * @return 安全的文件名
     */
    public String sanitizeFileName(String fileName) {
        // 替换不安全字符为下划线
        String sanitized = fileName;

        // 如果替换后为空，使用默认名称
        if (sanitized.trim().isEmpty()) {
            sanitized = "file_" + System.currentTimeMillis();
        }

        // 截断过长的文件名
        if (sanitized.length() > 100) {
            sanitized = sanitized.substring(0, 100);
        }

        return sanitized;
    }

    /**
     * 计算输入流内容的 SHA-256 哈希值。
     * 方法会负责关闭传入的输入流。
     *
     * @param inputStream 文件内容的输入流。
     * @return SHA-256 哈希值的十六进制字符串。
     * @throws IOException 如果读取输入流失败。
     */
    public String calculateHash(InputStream inputStream) throws IOException {
        try (InputStream is = inputStream) {
            String hash = DigestUtils.sha256Hex(is);
            log.debug("计算文件内容的 SHA-256 哈希值完成。");
            return hash;
        } catch (IOException e) {
            log.error("计算文件哈希值时发生 IO 异常", e);
            throw e;
        }
    }

    /**
     * 根据节点ID读取文件内容。
     * 直接使用节点名称和文档类型构建完整路径。
     *
     * @param nodeId 知识库节点ID。
     * @return 文件内容的字节数组。
     * @throws IOException 如果文件未找到或读取失败。
     */
    public byte[] readFile(String nodeId) throws IOException {
        // 获取节点信息
        KnowledgeNode node = nodeMapper.selectById(nodeId);
        if (node == null) {
            throw new IOException("找不到节点: " + nodeId);
        }

        // 如果不是文件节点，直接报错
        if (node.getType() != NodeType.FILE) {
            throw new IOException("节点不是文件类型: " + nodeId);
        }

        // 获取存储路径
        StoragePath storagePath = getStoragePathForNode(nodeId);
        String baseDir = storagePath.getFullPath();

        // 使用闭包表获取节点路径
        List<KnowledgeNode> nodePath = getNodePath(nodeId);
        if (nodePath.isEmpty()) {
            throw new IOException("无法获取节点路径: " + nodeId);
        }

        // 构建父目录路径
        Path basePath = Paths.get(baseDir);
        Path parentPath = basePath;

        // 构建目录路径，但不包含当前节点
        for (int i = 0; i < nodePath.size() - 1; i++) {
            String safeName = sanitizeFileName(nodePath.get(i).getName());
            parentPath = parentPath.resolve(safeName);
        }

        // 如果父目录不存在
        if (!Files.exists(parentPath)) {
            throw new IOException("节点父目录不存在: " + parentPath);
        }

        // 获取当前节点名称
        String nodeName = sanitizeFileName(node.getName());

        try {
            // 直接使用节点名称读取文件
            Path filePath = parentPath.resolve(nodeName);
            if (Files.exists(filePath) && Files.isRegularFile(filePath) && Files.isReadable(filePath)) {
                log.debug("直接使用节点名称读取文件: {}", filePath);
                byte[] content = Files.readAllBytes(filePath);
                log.debug("文件读取完成: {}, 大小: {} bytes", filePath, content.length);
                return content;
            }

        } catch (IOException e) {
            log.error("在读取文件时出错: {}", e.getMessage());
            throw new IOException("读取文件时出错: " + e.getMessage(), e);
        }

        // 如果所有方法都找不到文件
        log.error("无法找到节点对应的文件: nodeId={}, nodeName={}, path={}",
                nodeId, nodeName, parentPath.resolve(nodeName));
        throw new IOException("无法找到节点对应的文件: " + nodeId);
    }


    /**
     * 根据节点ID删除文件。
     * 如果文件不存在，则静默返回 true。
     * 使用节点名称和路径结构查找文件，而不是节点ID。
     *
     * @param nodeId 知识库节点ID。
     * @return 如果删除成功或文件不存在则返回 true，否则返回 false。
     */
    public boolean deleteFileByNodeId(String nodeId) {
        if (!StringUtils.hasText(nodeId)) {
            log.warn("尝试删除文件，但提供的节点ID为空。");
            return true; // ID为空，认为无需删除
        }

        try {
            // 获取节点信息
            KnowledgeNode node = nodeMapper.selectById(nodeId);
            if (node == null) {
                log.warn("找不到节点: {}", nodeId);
                return true; // 节点不存在，认为无需删除
            }

            // 如果不是文件节点，返回成功
            if (node.getType() != NodeType.FILE) {
                log.warn("节点不是文件类型，无需删除文件: {}", nodeId);
                return true;
            }

            // 获取存储路径
            StoragePath storagePath = getStoragePathForNode(nodeId);
            String baseDir = storagePath.getFullPath();

            // 使用闭包表获取节点路径
            List<KnowledgeNode> nodePath = getNodePath(nodeId);
            if (nodePath.isEmpty()) {
                log.warn("无法获取节点路径: {}", nodeId);
                return true; // 路径不存在，认为无需删除
            }

            // 构建父目录路径
            Path basePath = Paths.get(baseDir);
            Path parentPath = basePath;

            // 构建目录路径，但不包含当前节点
            for (int i = 0; i < nodePath.size() - 1; i++) {
                String safeName = sanitizeFileName(nodePath.get(i).getName());
                parentPath = parentPath.resolve(safeName);
            }

            if (!Files.exists(parentPath)) {
                log.warn("节点父目录不存在: {}", parentPath);
                return true; // 父目录不存在，认为无需删除
            }

            // 获取当前节点名称
            String nodeName = sanitizeFileName(node.getName());

            // 直接使用节点名称找到文件
            Path filePath = null;
            Path filePathDirect = parentPath.resolve(nodeName);
            if (Files.exists(filePathDirect) && Files.isRegularFile(filePathDirect)) {
                filePath = filePathDirect;
                log.debug("直接使用节点名称找到文件: {}", filePath);
            }


            // 如果找到了文件，删除它
            if (filePath != null) {
                try {
                    Files.delete(filePath);
                    log.info("成功删除文件: {}", filePath);
                    // 不再删除空父目录，只删除文件本身
                    return true;
                } catch (IOException e) {
                    log.error("删除文件时出错: {}", e.getMessage());
                    return false;
                }
            }

            log.warn("无法找到节点对应的文件: nodeId={}, nodeName={}, path={}",
                    nodeId, nodeName, parentPath.resolve(nodeName));
            return true; // 文件不存在，认为删除成功
        } catch (Exception e) {
            log.error("删除文件时出错: nodeId={}, 错误: {}", nodeId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 删除节点对应的目录
     *
     * @param nodeId 节点ID
     * @return 如果删除成功或目录不存在则返回 true，否则返回 false
     */
    public boolean deleteNodeDirectory(String nodeId) {
        try {
            // 获取存储路径
            StoragePath storagePath = getStoragePathForNode(nodeId);
            String baseDir = storagePath.getFullPath();

            Path nodePath = buildNodePath(baseDir, nodeId);
            if (Files.exists(nodePath) && Files.isDirectory(nodePath)) {
                // 检查目录是否为空
                if (Files.list(nodePath).findAny().isEmpty()) {
                    Files.delete(nodePath);
                    log.info("成功删除节点目录: {}", nodePath);

                    // 不再删除空父目录，只删除当前目录
                    return true;
                } else {
                    log.warn("节点目录不为空，无法删除: {}", nodePath);
                    return false;
                }
            } else {
                log.warn("尝试删除的节点目录不存在: {}", nodePath);
                return true; // 目录不存在，视为删除成功
            }
        } catch (IOException e) {
            log.error("删除节点目录时出错: nodeId={}, 错误: {}", nodeId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 重命名节点对应的文件或目录
     *
     * @param nodeId  节点ID
     * @param oldName 旧名称
     * @param newName 新名称
     * @return 如果重命名成功则返回 true，否则返回 false
     */
    public boolean renameNodeFileOrDirectory(String nodeId, String oldName, String newName) {
        try {
            // 获取节点信息
            KnowledgeNode node = nodeMapper.selectById(nodeId);
            if (node == null) {
                log.error("找不到节点: {}", nodeId);
                return false;
            }

            // 获取存储路径
            StoragePath storagePath = getStoragePathForNode(nodeId);
            String baseDir = storagePath.getFullPath();

            // 获取节点的父节点路径
            List<KnowledgeNode> nodePath = getNodePath(nodeId);
            if (nodePath.isEmpty()) {
                log.error("无法获取节点路径: {}", nodeId);
                return false;
            }

            // 构建父目录路径
            Path basePath = Paths.get(baseDir);
            Path parentPath = basePath;

            // 构建目录路径，但不包含当前节点
            for (int i = 0; i < nodePath.size() - 1; i++) {
                String safeName = sanitizeFileName(nodePath.get(i).getName());
                parentPath = parentPath.resolve(safeName);
            }

            // 使用旧名称构建当前路径
            String safeOldName = sanitizeFileName(oldName);
            Path currentPath = parentPath.resolve(safeOldName);

            // 如果是文件，需要考虑扩展名
            boolean isFile = node.getType() == NodeType.FILE;

            if (isFile) {
                // 如果是文件节点，尝试从文档记录中获取MIME类型
                KnowledgeDocument document = documentMapper.selectOne(
                        new LambdaQueryWrapper<KnowledgeDocument>().eq(KnowledgeDocument::getNodeId, nodeId)
                );

                if (document != null && StringUtils.hasText(document.getMimeType())) {
                    // 从 MIME 类型获取文件扩展名
                    Path filePathWithExt = parentPath.resolve(safeOldName);

                    if (Files.exists(filePathWithExt) && Files.isRegularFile(filePathWithExt)) {
                        currentPath = filePathWithExt;
                        log.debug("使用MIME类型找到文件: {}", currentPath);
                    }
                }

                // 如果仍然找不到文件，返回失败
                if (!Files.exists(currentPath) || !Files.isRegularFile(currentPath)) {
                    log.warn("要重命名的文件不存在: {}", currentPath);
                    return false;
                }

            } else {
                // 如果是目录，直接检查路径是否存在
                if (!Files.exists(currentPath) || !Files.isDirectory(currentPath)) {
                    log.warn("要重命名的目录不存在: {}", currentPath);
                    return false;
                }
            }

            // 构建新路径（使用新名称）
            String safeNewName = sanitizeFileName(newName);
            Path newPath = parentPath.resolve(safeNewName);

            // 检查新路径是否已存在
            if (Files.exists(newPath)) {
                log.error("目标路径已存在，无法重命名: {}", newPath);
                return false;
            }

            // 执行重命名
            Files.move(currentPath, newPath, StandardCopyOption.ATOMIC_MOVE);
            log.info("成功重命名 {} 为 {}", currentPath, newPath);
            return true;
        } catch (IOException e) {
            log.error("重命名节点文件或目录时出错: nodeId={}, oldName={}, newName={}, 错误: {}",
                    nodeId, oldName, newName, e.getMessage(), e);
            return false;
        }
    }

}