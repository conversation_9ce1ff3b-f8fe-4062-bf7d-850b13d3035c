package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.KnowledgeNode;
import com.zibbava.edgemind.cortex.entity.KnowledgeSpace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 知识库节点表（kb_knowledge_nodes）的MyBatis Mapper接口。
 * 继承自 BaseMapper，提供了基础的 CRUD 功能。
 * 如有复杂的树状查询或递归操作，可在此处添加自定义 SQL 方法或使用 XML 映射文件。
 */
@Mapper
public interface KnowledgeNodeMapper extends BaseMapper<KnowledgeNode> {
    // MP BaseMapper 提供了基本的 CRUD 方法
    // 如果需要复杂的树查询或递归删除，可以考虑在这里添加自定义方法或使用 XML

    /**
     * 根据知识空间ID查询知识空间信息
     *
     * @param spaceId 知识空间ID
     * @return 知识空间实体
     */
    @Select("SELECT * FROM kb_knowledge_spaces WHERE space_id = #{spaceId}")
    KnowledgeSpace selectSpaceById(@Param("spaceId") String spaceId);
}