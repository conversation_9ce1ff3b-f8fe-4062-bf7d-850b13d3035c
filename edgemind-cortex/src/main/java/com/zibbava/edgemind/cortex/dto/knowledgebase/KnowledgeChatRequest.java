package com.zibbava.edgemind.cortex.dto.knowledgebase;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 知识库 RAG (检索增强生成) 聊天请求的数据传输对象 (DTO)。
 */
@Data
public class KnowledgeChatRequest {

    /**
     * 用户的查询语句或问题 (必填)。
     */
    @NotBlank(message = "查询内容不能为空")
    private String query;

    /**
     * 当前聊天上下文关联的知识库节点 ID (必填)。
     * 这可以是用户在界面上选中的文件或文件夹的 ID。
     */
    @NotBlank(message = "上下文节点 ID 不能为空")
    private String contextNodeId;

    // 未来可扩展字段:
    // private String modelIdentifier; // 指定使用的 AI 模型
    // private List<ChatMessageDto> history; // 传递聊天历史记录以支持多轮对话
} 