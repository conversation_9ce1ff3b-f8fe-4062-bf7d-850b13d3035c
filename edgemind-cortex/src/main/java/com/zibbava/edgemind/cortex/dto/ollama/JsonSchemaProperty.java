package com.zibbava.edgemind.cortex.dto.ollama;

import lombok.Builder;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * JSON Schema属性定义类
 */
@Data
@Builder
public class JsonSchemaProperty {
    private String type;
    private String description;
    private List<String> enumValues;
    private JsonSchema items; // 用于数组类型
    private Map<String, JsonSchemaProperty> properties; // 用于对象类型

    public static JsonSchemaProperty string(String description) {
        return JsonSchemaProperty.builder()
                .type("string")
                .description(description)
                .build();
    }

    public static JsonSchemaProperty number(String description) {
        return JsonSchemaProperty.builder()
                .type("number")
                .description(description)
                .build();
    }

    public static JsonSchemaProperty integer(String description) {
        return JsonSchemaProperty.builder()
                .type("integer")
                .description(description)
                .build();
    }

    public static JsonSchemaProperty bool(String description) {
        return JsonSchemaProperty.builder()
                .type("boolean")
                .description(description)
                .build();
    }

    public static JsonSchemaProperty enumString(String description, String... values) {
        return JsonSchemaProperty.builder()
                .type("string")
                .description(description)
                .enumValues(Arrays.asList(values))
                .build();
    }
} 