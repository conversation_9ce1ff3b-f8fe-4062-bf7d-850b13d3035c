package com.zibbava.edgemind.cortex.controller.api;

import cn.dev33.satoken.annotation.SaIgnore;
import com.zibbava.edgemind.cortex.common.Result;
import com.zibbava.edgemind.cortex.dto.ModelDownloadRequest;
import com.zibbava.edgemind.cortex.dto.SystemInfoResponse;
import com.zibbava.edgemind.cortex.entity.RemoteModelConfig;
import com.zibbava.edgemind.cortex.service.ModelService;
import com.zibbava.edgemind.cortex.service.RemoteModelService;
import com.zibbava.edgemind.cortex.service.RemoteModelIntegrationService;
import com.zibbava.edgemind.cortex.service.SystemInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 模型API控制器
 * 提供模型相关REST API接口
 */
@RestController
@RequestMapping("/api/models")
@RequiredArgsConstructor
@Slf4j
public class ModelApiController {

    private final ModelService modelService;
    private final RemoteModelService remoteModelService;
    private final RemoteModelIntegrationService remoteModelIntegrationService;
    private final SystemInfoService systemInfoService;

    /**
     * 获取系统硬件信息
     *
     * @return 系统硬件信息
     */
    @GetMapping("/system-info")
    public Result<SystemInfoResponse> getSystemInfo() {
        log.info("获取系统硬件信息");
        return Result.success(systemInfoService.getSystemInfo());
    }

    /**
     * 获取可用模型列表
     *
     * @return 模型列表
     */
    @GetMapping("/available")
    public Result<List<Map<String, Object>>> getAvailableModels() {
        log.info("获取可用模型列表");
        return Result.success(modelService.getAvailableModels());
    }

    /**
     * 检查Ollama服务状态
     *
     * @return Ollama服务状态
     */
    @GetMapping("/ollama/status")
    public Result<Map<String, Object>> checkOllamaStatus() {
        log.info("检查Ollama服务状态");
        return Result.success(modelService.checkOllamaStatus());
    }

    /**
     * 获取已安装的模型列表
     *
     * @return 模型列表
     */
    @GetMapping("/installed")
    public Result<List<Map<String, Object>>> getInstalledModels() {
        log.info("获取已安装模型列表");
        return Result.success(modelService.getInstalledModels());
    }

    /**
     * 下载模型
     *
     * @param request 下载请求
     * @return 操作结果
     */
    @PostMapping("/download")
    public Result<Map<String, Object>> downloadModel(@RequestBody ModelDownloadRequest request) {
        log.info("下载模型: {}", request.getModelName());
        return Result.success(modelService.startModelDownload(request));
    }

    /**
     * 获取模型下载进度
     *
     * @param taskId 下载任务ID
     * @return 下载进度信息
     */
    @GetMapping("/download/progress/{taskId}")
    public Result<Map<String, Object>> getDownloadProgress(@PathVariable String taskId) {
        return Result.success(modelService.getDownloadProgress(taskId));
    }

    /**
     * 获取模型下载实时进度（使用SSE）
     *
     * @param taskId 下载任务ID
     * @return 事件流
     */
    @SaIgnore
    @GetMapping(value = "/download/stream/{taskId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public org.springframework.web.servlet.mvc.method.annotation.SseEmitter streamDownloadProgress(@PathVariable String taskId) {
        return modelService.streamDownloadProgress(taskId);
    }
    
    /**
     * 删除模型
     *
     * @return 操作结果
     */
    @PostMapping("/delete")
    public Result<Boolean> deleteModel(@RequestBody Map<String, String> request) {
        String modelName = request.get("modelName");
        log.info("删除模型: {}", modelName);
        return Result.success(modelService.deleteModel(modelName));
    }

    // BGE-Large模型状态检查API已删除，现在使用本地BGE中文嵌入模型

    // ==================== 远程模型相关API ====================

    /**
     * 获取所有支持的远程模型
     *
     * @return 远程模型列表
     */
    @GetMapping("/remote/available")
    public Result<List<Map<String, Object>>> getAvailableRemoteModels() {
        log.info("获取所有支持的远程模型");
        return Result.success(remoteModelService.getAllRemoteModels());
    }

    /**
     * 根据提供商获取远程模型
     *
     * @param provider 提供商名称
     * @return 远程模型列表
     */
    @GetMapping("/remote/available/{provider}")
    public Result<List<Map<String, Object>>> getRemoteModelsByProvider(@PathVariable String provider) {
        log.info("获取提供商{}的远程模型", provider);
        return Result.success(remoteModelService.getRemoteModelsByProvider(provider));
    }

    /**
     * 获取所有支持的提供商
     *
     * @return 提供商列表
     */
    @GetMapping("/remote/providers")
    public Result<List<String>> getAllProviders() {
        log.info("获取所有支持的提供商");
        return Result.success(remoteModelService.getAllProviders());
    }

    /**
     * 获取远程模型配置
     *
     * @return 远程模型配置列表
     */
    @GetMapping("/remote/configs")
    public Result<List<RemoteModelConfig>> getRemoteModelConfigs() {
        log.info("获取远程模型配置");
        return Result.success(remoteModelService.getAllRemoteModelConfigs());
    }

    /**
     * 获取启用的远程模型配置
     *
     * @return 启用的远程模型配置列表
     */
    @GetMapping("/remote/configs/enabled")
    public Result<List<RemoteModelConfig>> getEnabledRemoteModelConfigs() {
        log.info("获取启用的远程模型配置");
        return Result.success(remoteModelService.getEnabledRemoteModelConfigs());
    }

    /**
     * 保存或更新远程模型配置
     *
     * @param config 远程模型配置
     * @return 保存后的配置
     */
    @PostMapping("/remote/configs")
    public Result<RemoteModelConfig> saveRemoteModelConfig(@RequestBody RemoteModelConfig config) {
        log.info("保存远程模型配置: 模型={}", config.getModelId());

        RemoteModelConfig savedConfig = remoteModelService.saveOrUpdateRemoteModelConfig(config);

        // 刷新远程模型集成
        remoteModelIntegrationService.refreshRemoteModels();

        return Result.success(savedConfig);
    }

    /**
     * 删除远程模型配置
     *
     * @param configId 配置ID
     * @return 删除结果
     */
    @DeleteMapping("/remote/configs/{configId}")
    public Result<Boolean> deleteRemoteModelConfig(@PathVariable Long configId) {
        log.info("删除远程模型配置: 配置ID={}", configId);

        boolean success = remoteModelService.deleteRemoteModelConfig(configId);
        return Result.success(success);
    }

    /**
     * 启用/禁用远程模型配置
     *
     * @param configId 配置ID
     * @param request 请求体，包含enabled字段
     * @return 操作结果
     */
    @PutMapping("/remote/configs/{configId}/toggle")
    public Result<Boolean> toggleRemoteModelConfig(@PathVariable Long configId,
                                                  @RequestBody Map<String, Object> request) {
        Boolean enabled = (Boolean) request.get("enabled");

        log.info("{}远程模型配置: 配置ID={}", enabled ? "启用" : "禁用", configId);

        boolean success = remoteModelService.toggleRemoteModelConfig(configId, enabled);

        // 刷新远程模型集成
        if (success) {
            remoteModelIntegrationService.refreshRemoteModels();
        }

        return Result.success(success);
    }

    /**
     * 测试远程模型连接
     *
     * @param request 测试请求，包含modelId、apiKey
     * @return 测试结果
     */
    @PostMapping("/remote/test-connection")
    public Result<Map<String, Object>> testRemoteModelConnection(@RequestBody Map<String, String> request) {
        String modelId = request.get("modelId");
        String apiKey = request.get("apiKey");

        log.info("测试远程模型连接: 模型={}", modelId);

        Map<String, Object> result = remoteModelService.testRemoteModelConnection(modelId, apiKey);
        return Result.success(result);
    }

    /**
     * 获取指定的远程模型配置
     *
     * @param modelId 模型ID
     * @return 远程模型配置
     */
    @GetMapping("/remote/configs/{modelId}")
    public Result<RemoteModelConfig> getRemoteModelConfig(@PathVariable String modelId) {
        log.info("获取远程模型配置: 模型={}", modelId);

        RemoteModelConfig config = remoteModelService.getRemoteModelConfig(modelId);
        return Result.success(config);
    }

    /**
     * 测试远程模型功能（LangChain4j集成测试）
     *
     * @param request 测试请求，包含modelId、testMessage
     * @return 测试结果
     */
    @PostMapping("/remote/test-model")
    public Result<Map<String, Object>> testRemoteModelIntegration(@RequestBody Map<String, String> request) {
        String modelId = request.get("modelId");
        String testMessage = request.getOrDefault("testMessage", "Hello, this is a test message.");

        log.info("测试远程模型集成: 模型={}, 消息={}", modelId, testMessage);

        Map<String, Object> result = remoteModelIntegrationService.testRemoteModel(modelId, testMessage);
        return Result.success(result);
    }

    /**
     * 获取所有可用的远程模型信息
     *
     * @return 可用的远程模型信息列表
     */
    @GetMapping("/remote/available-models")
    public Result<List<Map<String, Object>>> getAvailableRemoteModelInfos() {
        log.info("获取所有可用的远程模型信息");

        List<Map<String, Object>> availableModels = remoteModelIntegrationService.getAvailableModelInfos();
        return Result.success(availableModels);
    }

    /**
     * 刷新远程模型集成
     *
     * @return 刷新结果
     */
    @PostMapping("/remote/refresh")
    public Result<Map<String, Object>> refreshRemoteModels() {
        log.info("手动刷新远程模型集成");

        try {
            remoteModelIntegrationService.refreshRemoteModels();

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "远程模型刷新成功");
            result.put("availableModels", remoteModelIntegrationService.getAvailableModelIds());

            return Result.success(result);
        } catch (Exception e) {
            log.error("刷新远程模型失败", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "刷新失败: " + e.getMessage());

            return Result.success(result);
        }
    }
}
