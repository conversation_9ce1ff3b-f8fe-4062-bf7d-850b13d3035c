package com.zibbava.edgemind.cortex.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zibbava.edgemind.cortex.dto.UserManagementDTO.*;
import com.zibbava.edgemind.cortex.entity.*;
import com.zibbava.edgemind.cortex.exception.BadRequestException;
import com.zibbava.edgemind.cortex.exception.ResourceNotFoundException;
import com.zibbava.edgemind.cortex.mapper.UserMapper;
import com.zibbava.edgemind.cortex.mapper.UserRoleMapper;
import com.zibbava.edgemind.cortex.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户管理服务实现类
 * 
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserManagementServiceImpl implements UserManagementService {

    private final UserMapper userMapper;
    private final UserRoleMapper userRoleMapper;
    private final RoleService roleService;
    private final DepartmentService departmentService;

    @Override
    public IPage<UserResponse> getUserPage(UserQueryRequest request) {
        Page<Map<String, Object>> page = new Page<>(request.getPageNum(), request.getPageSize());

        // 构建查询参数
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.hasText(request.getUsername())) {
            params.put("username", request.getUsername());
        }
        if (StringUtils.hasText(request.getNickname())) {
            params.put("nickname", request.getNickname());
        }
        if (StringUtils.hasText(request.getEmail())) {
            params.put("email", request.getEmail());
        }
        if (StringUtils.hasText(request.getPhone())) {
            params.put("phone", request.getPhone());
        }
        if (request.getDeptId() != null) {
            params.put("deptId", request.getDeptId());
        }
        if (request.getStatus() != null) {
            params.put("status", request.getStatus());
        }

        IPage<Map<String, Object>> userPage = userMapper.selectUserPageWithDept(page, params);

        // 转换为响应DTO并填充角色信息
        IPage<UserResponse> responsePage = userPage.convert(map -> {
            UserResponse userResponse = convertMapToUserResponse(map);
            // 获取用户角色
            Long userId = userResponse.getId();
            if (userId != null) {
                List<Role> roles = roleService.findRolesByUserId(userId);
                userResponse.setRoles(roles.stream()
                        .map(this::convertToRoleResponse)
                        .collect(Collectors.toList()));
            }
            return userResponse;
        });

        return responsePage;
    }

    @Override
    public UserResponse getUserById(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在: " + userId);
        }
        
        UserResponse response = convertToUserResponse(user);
        
        // 获取用户角色
        List<Role> roles = roleService.findRolesByUserId(userId);
        response.setRoles(roles.stream()
                .map(this::convertToRoleResponse)
                .collect(Collectors.toList()));
        
        return response;
    }

    @Override
    @Transactional
    public Long createUser(CreateUserRequest request) {
        // 验证用户名唯一性
        if (isUsernameExists(request.getUsername())) {
            throw new BadRequestException("用户名已存在: " + request.getUsername());
        }
        
        // 验证邮箱唯一性
        if (StringUtils.hasText(request.getEmail()) && isEmailExists(request.getEmail(), null)) {
            throw new BadRequestException("邮箱已存在: " + request.getEmail());
        }
        
        // 验证手机号唯一性
        if (StringUtils.hasText(request.getPhone()) && isPhoneExists(request.getPhone(), null)) {
            throw new BadRequestException("手机号已存在: " + request.getPhone());
        }
        
        // 验证部门存在性
        if (request.getDeptId() != null && departmentService.getById(request.getDeptId()) == null) {
            throw new BadRequestException("部门不存在: " + request.getDeptId());
        }
        
        // 创建用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(SaSecureUtil.md5(request.getPassword()));
        user.setNickname(request.getNickname());
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        user.setDeptId(request.getDeptId());
        user.setRemark(request.getRemark());
        user.setStatus(request.getStatus());
        
        user.setPasswordUpdateTime(LocalDateTime.now());
        
        userMapper.insert(user);
        
        // 分配角色
        if (!CollectionUtils.isEmpty(request.getRoleIds())) {
            assignRolesToUser(user.getId(), request.getRoleIds());
        }
        
        return user.getId();
    }

    @Override
    @Transactional
    public void updateUser(UpdateUserRequest request) {
        User existingUser = userMapper.selectById(request.getId());
        if (existingUser == null) {
            throw new ResourceNotFoundException("用户不存在: " + request.getId());
        }
        
        // 验证邮箱唯一性
        if (StringUtils.hasText(request.getEmail()) && isEmailExists(request.getEmail(), request.getId())) {
            throw new BadRequestException("邮箱已存在: " + request.getEmail());
        }
        
        // 验证手机号唯一性
        if (StringUtils.hasText(request.getPhone()) && isPhoneExists(request.getPhone(), request.getId())) {
            throw new BadRequestException("手机号已存在: " + request.getPhone());
        }
        
        // 验证部门存在性
        if (request.getDeptId() != null && departmentService.getById(request.getDeptId()) == null) {
            throw new BadRequestException("部门不存在: " + request.getDeptId());
        }
        
        // 更新用户信息
        User user = new User();
        user.setId(request.getId());
        user.setNickname(request.getNickname());
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        user.setDeptId(request.getDeptId());
        user.setRemark(request.getRemark());
        user.setStatus(request.getStatus());

        userMapper.updateById(user);

        // 更新角色分配
        if (request.getRoleIds() != null) {
            assignRolesToUser(request.getId(), request.getRoleIds());
        }
    }

    @Override
    @Transactional
    public void deleteUser(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在: " + userId);
        }
        
        // 删除用户角色关联
        LambdaQueryWrapper<UserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserRole::getUserId, userId);
        userRoleMapper.delete(wrapper);
        
        // 删除用户
        userMapper.deleteById(userId);
    }

    @Override
    @Transactional
    public void batchDeleteUsers(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        
        // 删除用户角色关联
        LambdaQueryWrapper<UserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(UserRole::getUserId, userIds);
        userRoleMapper.delete(wrapper);
        
        // 批量删除用户
        userMapper.deleteBatchIds(userIds);
    }

    @Override
    @Transactional
    public void resetPassword(ResetPasswordRequest request) {
        User user = userMapper.selectById(request.getUserId());
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在: " + request.getUserId());
        }
        
        User updateUser = new User();
        updateUser.setId(request.getUserId());
        updateUser.setPassword(SaSecureUtil.md5(request.getNewPassword()));
        updateUser.setPasswordUpdateTime(LocalDateTime.now());
        
        userMapper.updateById(updateUser);
    }

    @Override
    @Transactional
    public void changePassword(Long userId, ChangePasswordRequest request) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在: " + userId);
        }

        // 验证新密码和确认密码是否一致
        if (!request.getNewPassword().equals(request.getConfirmPassword())) {
            throw new IllegalArgumentException("新密码和确认密码不一致");
        }

        // 验证原密码是否正确
        String encryptedOldPassword = SaSecureUtil.md5(request.getOldPassword());
        if (!encryptedOldPassword.equals(user.getPassword())) {
            throw new IllegalArgumentException("原密码错误");
        }

        // 检查新密码是否与原密码相同
        String encryptedNewPassword = SaSecureUtil.md5(request.getNewPassword());
        if (encryptedNewPassword.equals(user.getPassword())) {
            throw new IllegalArgumentException("新密码不能与原密码相同");
        }

        User updateUser = new User();
        updateUser.setId(userId);
        updateUser.setPassword(encryptedNewPassword);
        updateUser.setPasswordUpdateTime(LocalDateTime.now());

        userMapper.updateById(updateUser);
    }

    @Override
    @Transactional
    public void assignRoles(AssignRoleRequest request) {
        User user = userMapper.selectById(request.getUserId());
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在: " + request.getUserId());
        }
        
        assignRolesToUser(request.getUserId(), request.getRoleIds());
    }

    @Override
    @Transactional
    public void toggleUserStatus(ToggleStatusRequest request) {
        User user = userMapper.selectById(request.getUserId());
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在: " + request.getUserId());
        }
        
        User updateUser = new User();
        updateUser.setId(request.getUserId());
        updateUser.setStatus(request.getStatus());
        
        userMapper.updateById(updateUser);
    }



    @Override
    public boolean isUsernameExists(String username) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getUsername, username);
        return userMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean isEmailExists(String email, Long excludeUserId) {
        if (!StringUtils.hasText(email)) {
            return false;
        }
        
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getEmail, email);
        if (excludeUserId != null) {
            wrapper.ne(User::getId, excludeUserId);
        }
        return userMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean isPhoneExists(String phone, Long excludeUserId) {
        if (!StringUtils.hasText(phone)) {
            return false;
        }
        
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getPhone, phone);
        if (excludeUserId != null) {
            wrapper.ne(User::getId, excludeUserId);
        }
        return userMapper.selectCount(wrapper) > 0;
    }

    @Override
    public List<RoleResponse> getUserRoles(Long userId) {
        List<Role> roles = roleService.findRolesByUserId(userId);
        return roles.stream()
                .map(this::convertToRoleResponse)
                .collect(Collectors.toList());
    }



    @Override
    public List<SimpleUserResponse> getSimpleUsers(Integer status, Long deptId) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(status != null, User::getStatus, status)
                   .eq(deptId != null, User::getDeptId, deptId)
                   .orderBy(true, true, User::getUsername);

        List<User> users = userMapper.selectList(queryWrapper);

        return users.stream()
                .map(this::convertToSimpleUserResponse)
                .collect(Collectors.toList());
    }


    /**
     * 为用户分配角色
     */
    private void assignRolesToUser(Long userId, List<Long> roleIds) {
        // 删除现有角色关联
        LambdaQueryWrapper<UserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserRole::getUserId, userId);
        userRoleMapper.delete(wrapper);
        
        // 添加新的角色关联
        if (!CollectionUtils.isEmpty(roleIds)) {
            List<UserRole> userRoles = roleIds.stream()
                    .map(roleId -> new UserRole(userId, roleId))
                    .collect(Collectors.toList());
            
            for (UserRole userRole : userRoles) {
                userRoleMapper.insert(userRole);
            }
        }
    }

    /**
     * 转换为简化用户响应DTO
     */
    private SimpleUserResponse convertToSimpleUserResponse(User user) {
        return SimpleUserResponse.builder()
                .id(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .email(user.getEmail())
                .deptId(user.getDeptId())
                .status(user.getStatus())
                .build();
    }

    /**
     * 转换Map为用户响应DTO（用于JOIN查询结果）
     */
    private UserResponse convertMapToUserResponse(Map<String, Object> map) {
        return UserResponse.builder()
                .id(getLongValue(map, "id"))
                .username(getStringValue(map, "username"))
                .nickname(getStringValue(map, "nickname"))
                .email(getStringValue(map, "email"))
                .phone(getStringValue(map, "phone"))
                .status(getIntegerValue(map, "status"))
                .deptId(getLongValue(map, "dept_id"))
                .deptName(getStringValue(map, "dept_name"))
                .remark(getStringValue(map, "remark"))
                .avatar(getStringValue(map, "avatar"))
                .lastLoginTime(getLocalDateTimeValue(map, "last_login_time"))
                .lastLoginIp(getStringValue(map, "last_login_ip"))
                .createTime(getLocalDateTimeValue(map, "create_time"))
                .updateTime(getLocalDateTimeValue(map, "update_time"))
                .build();
    }

    /**
     * 转换为用户响应DTO
     */
    private UserResponse convertToUserResponse(User user) {
        return UserResponse.builder()
                .id(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .email(user.getEmail())
                .phone(user.getPhone())
                .status(user.getStatus())
                .deptId(user.getDeptId())
                .deptName("") // 暂时设为空，避免N+1查询问题
                .remark(user.getRemark())
                .avatar(user.getAvatar())
                .lastLoginTime(user.getLastLoginTime())
                .lastLoginIp(user.getLastLoginIp())
                .createTime(user.getCreateTime())
                .updateTime(user.getUpdateTime())
                .build();
    }

    /**
     * 转换为角色响应DTO
     */
    private RoleResponse convertToRoleResponse(Role role) {
        return RoleResponse.builder()
                .id(role.getId())
                .roleName(role.getRoleName())
                .roleCode(role.getRoleCode())
                .description(role.getDescription())
                .status(role.getStatus())
                .build();
    }

    // 辅助方法：安全地从Map中获取值
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Long) return (Long) value;
        if (value instanceof Number) return ((Number) value).longValue();
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Integer) return (Integer) value;
        if (value instanceof Number) return ((Number) value).intValue();
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Boolean getBooleanValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Boolean) return (Boolean) value;
        if (value instanceof Number) return ((Number) value).intValue() != 0;
        return Boolean.parseBoolean(value.toString());
    }

    private LocalDateTime getLocalDateTimeValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof LocalDateTime) return (LocalDateTime) value;
        // 如果需要处理其他时间格式，可以在这里添加转换逻辑
        return null;
    }
}
