package com.zibbava.edgemind.cortex.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色管理相关DTO
 * 
 * <AUTHOR>
 */
public class RoleManagementDTO {

    /**
     * 角色创建请求DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateRoleRequest {

        @NotBlank(message = "角色名称不能为空")
        @Size(min = 2, max = 50, message = "角色名称长度必须在2-50个字符之间")
        private String roleName;

        @NotBlank(message = "角色编码不能为空")
        @Size(min = 2, max = 50, message = "角色编码长度必须在2-50个字符之间")
        @Pattern(regexp = "^[A-Z_]+$", message = "角色编码只能包含大写字母和下划线")
        private String roleCode;

        @Size(max = 255, message = "角色描述长度不能超过255个字符")
        private String description;

        @NotNull(message = "状态不能为空")
        @Min(value = 0, message = "状态值不正确")
        @Max(value = 1, message = "状态值不正确")
        private Integer status;

        private List<Long> permissionIds;
    }

    /**
     * 角色更新请求DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateRoleRequest {
        
        @NotNull(message = "角色ID不能为空")
        private Long id;

        @NotBlank(message = "角色名称不能为空")
        @Size(min = 2, max = 50, message = "角色名称长度必须在2-50个字符之间")
        private String roleName;

        @Size(max = 255, message = "角色描述长度不能超过255个字符")
        private String description;

        @NotNull(message = "状态不能为空")
        @Min(value = 0, message = "状态值不正确")
        @Max(value = 1, message = "状态值不正确")
        private Integer status;
    }

    /**
     * 角色查询请求DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RoleQueryRequest {
        
        private String roleName;
        private String roleCode;
        private Integer status;
        
        @Min(value = 1, message = "页码必须大于0")
        @Builder.Default
        private Integer pageNum = 1;

        @Min(value = 1, message = "页面大小必须大于0")
        @Max(value = 100, message = "页面大小不能超过100")
        @Builder.Default
        private Integer pageSize = 10;
    }

    /**
     * 角色响应DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RoleResponse {
        
        private Long id;
        private String roleName;
        private String roleCode;
        private String description;
        private Integer status;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime createTime;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime updateTime;
        
        private List<PermissionResponse> permissions;
        private Integer userCount; // 拥有此角色的用户数量
    }

    /**
     * 权限响应DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PermissionResponse {
        
        private Long id;
        private String permissionName;
        private String permissionCode;
        private String type;
        private Long parentId;
        private String description;
        private String icon;
        private Integer sortOrder;
        private List<PermissionResponse> children;
    }

    /**
     * 分配权限请求DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AssignPermissionRequest {
        
        @NotNull(message = "角色ID不能为空")
        private Long roleId;

        @NotEmpty(message = "权限ID列表不能为空")
        private List<Long> permissionIds;
    }

    /**
     * 角色状态切换请求DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ToggleStatusRequest {
        
        @NotNull(message = "角色ID不能为空")
        private Long roleId;

        @NotNull(message = "状态不能为空")
        @Min(value = 0, message = "状态值不正确")
        @Max(value = 1, message = "状态值不正确")
        private Integer status;
    }

    /**
     * 权限树节点DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PermissionTreeNode {
        
        private Long id;
        private String label; // 显示名称
        private String value; // 权限编码
        private String type;
        private Long parentId;
        private String icon;
        private Integer sortOrder;
        @Builder.Default
        private Boolean checked = false; // 是否选中
        @Builder.Default
        private Boolean expanded = false; // 是否展开
        @Builder.Default
        private Boolean isDynamic = false; // 是否为动态权限
        private String relatedResourceId; // 关联资源ID
        private String relatedResourceType; // 关联资源类型
        private List<PermissionTreeNode> children;
    }
}
