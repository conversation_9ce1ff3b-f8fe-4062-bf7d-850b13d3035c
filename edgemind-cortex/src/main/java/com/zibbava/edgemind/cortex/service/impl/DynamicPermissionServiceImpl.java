package com.zibbava.edgemind.cortex.service.impl;

import com.zibbava.edgemind.cortex.entity.KnowledgeSpace;
import com.zibbava.edgemind.cortex.entity.Permission;
import com.zibbava.edgemind.cortex.entity.PermissionTemplate;
import com.zibbava.edgemind.cortex.mapper.KnowledgeSpaceMapper;
import com.zibbava.edgemind.cortex.mapper.PermissionMapper;
import com.zibbava.edgemind.cortex.mapper.PermissionTemplateMapper;
import com.zibbava.edgemind.cortex.mapper.RolePermissionMapper;
import com.zibbava.edgemind.cortex.service.DynamicPermissionService;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 动态权限服务实现类
 * 
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DynamicPermissionServiceImpl implements DynamicPermissionService {
    
    private final PermissionMapper permissionMapper;
    private final PermissionTemplateMapper templateMapper;
    private final RolePermissionMapper rolePermissionMapper;
    private final KnowledgeSpaceMapper knowledgeSpaceMapper;
    
    @Override
    @Transactional
    public List<Permission> generateKnowledgeSpacePermissions(String spaceId, String spaceName) {
        List<Permission> generatedPermissions = new ArrayList<>();
        
        // 获取知识库空间权限模板
        List<PermissionTemplate> templates = templateMapper.findByResourceType("KNOWLEDGE_SPACE");
        
        // 查找或创建知识库管理父权限
        Permission parentPermission = getOrCreateKnowledgeParentPermission();
        
        for (PermissionTemplate template : templates) {
            // 检查是否已存在该空间的权限
            String permissionCode = template.getTemplateCode().replace("{space_id}", spaceId);
            Permission existingPermission = permissionMapper.findByPermissionCode(permissionCode);
            
            if (existingPermission == null) {
                Permission permission = new Permission();
                permission.setPermissionName(spaceName + " - " + template.getTemplateName());
                permission.setPermissionCode(permissionCode);
                permission.setType(Permission.PermissionType.valueOf(template.getPermissionType()));
                permission.setParentId(parentPermission.getId());
                permission.setDescription(template.getDescription().replace("{space_name}", spaceName));
                permission.setStatus(1);
                permission.setIsDynamic(true);
                permission.setRelatedResourceId(spaceId);
                permission.setRelatedResourceType("KNOWLEDGE_SPACE");
                permission.setSortOrder(999); // 动态权限排在最后
                permission.setDeleted(false); // 设置为未删除

                // 设置创建人和更新人
                try {
                    Long currentUserId = StpUtil.getLoginIdAsLong();
                    permission.setCreateBy(currentUserId);
                    permission.setUpdateBy(currentUserId);
                } catch (Exception e) {
                    // 如果获取当前用户失败，设置为系统用户
                    permission.setCreateBy(1L);
                    permission.setUpdateBy(1L);
                }

                permissionMapper.insert(permission);
                generatedPermissions.add(permission);

                log.info("为知识空间 {} 生成权限: {}", spaceName, permissionCode);
            } else {
                generatedPermissions.add(existingPermission);
            }
        }
        
        return generatedPermissions;
    }
    
    @Override
    @Transactional
    public void removeKnowledgeSpacePermissions(String spaceId) {
        // 查找该空间的所有权限
        List<Permission> spacePermissions = permissionMapper.findByRelatedResourceId(spaceId);
        
        for (Permission permission : spacePermissions) {
            // 删除角色权限关联
            rolePermissionMapper.deleteByPermissionId(permission.getId());
            // 删除权限记录
            permissionMapper.deleteById(permission.getId());
            
            log.info("删除知识空间权限: {}", permission.getPermissionCode());
        }
    }
    
    @Override
    public Permission getOrCreateKnowledgeParentPermission() {
        Permission parent = permissionMapper.findByPermissionCode("knowledge:manage");
        if (parent == null) {
            parent = new Permission();
            parent.setPermissionName("知识库管理");
            parent.setPermissionCode("knowledge:manage");
            parent.setType(Permission.PermissionType.MENU);
            parent.setParentId(0L);
            parent.setDescription("知识库相关权限的父级菜单");
            parent.setStatus(1);
            parent.setIcon("bi-book");
            parent.setSortOrder(100);
            parent.setIsDynamic(false);
            
            permissionMapper.insert(parent);
            log.info("创建知识库管理父权限");
        }
        return parent;
    }
    
    @Override
    public List<PermissionTemplate> getPermissionTemplates(String resourceType) {
        return templateMapper.findByResourceType(resourceType);
    }
    
    @Override
    public void ensureDynamicPermissionsGenerated() {
        List<KnowledgeSpace> allSpaces = knowledgeSpaceMapper.selectList(null);
        
        for (KnowledgeSpace space : allSpaces) {
            // 检查是否已有权限，没有则生成
            String accessPermissionCode = "kb:space:" + space.getSpaceId() + ":access";
            Permission existingPermission = permissionMapper.findByPermissionCode(accessPermissionCode);
            
            if (existingPermission == null) {
                generateKnowledgeSpacePermissions(space.getSpaceId(), space.getName());
            }
        }
    }
}
