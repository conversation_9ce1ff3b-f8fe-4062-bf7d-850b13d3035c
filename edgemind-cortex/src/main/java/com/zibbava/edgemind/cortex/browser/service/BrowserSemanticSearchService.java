package com.zibbava.edgemind.cortex.browser.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zibbava.edgemind.cortex.store.LuceneEmbeddingStore;
import dev.langchain4j.data.document.Document;
import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.document.Metadata;
import dev.langchain4j.data.document.splitter.DocumentSplitters;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import dev.langchain4j.store.embedding.EmbeddingStore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 浏览器语义搜索服务
 * 负责标签页内容的向量化存储和语义搜索
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BrowserSemanticSearchService {

    private final EmbeddingStore<TextSegment> embeddingStore;
    private final EmbeddingModel embeddingModel;
    private final ObjectMapper objectMapper;

    // 标签页内容缓存，用于避免重复索引
    private final Map<String, Long> tabContentTimestamps = new HashMap<>();
    
    /**
     * 索引标签页内容
     */
    public void indexTabContent(int tabId, String url, String title, String content) {
        try {
            String tabKey = tabId + ":" + url;
            long currentTime = System.currentTimeMillis();
            
            // 检查是否需要重新索引（内容变化或超过5分钟）
            Long lastIndexTime = tabContentTimestamps.get(tabKey);
            if (lastIndexTime != null && (currentTime - lastIndexTime) < 300000) { // 5分钟
                log.debug("跳过标签页 {} 的重复索引", tabId);
                return;
            }
            
            log.info("🔍 开始索引标签页内容: tabId={}, url={}, title={}", tabId, url, title);
            
            // 清理旧的索引数据
            removeTabContent(tabId, url);
            
            // 分块处理内容
            List<TextChunk> chunks = chunkContent(content, title, url);
            
            // 批量向量化和存储
            List<TextSegment> segments = new ArrayList<>();
            for (TextChunk chunk : chunks) {
                Metadata metadata = Metadata.from(Map.of(
                    "tabId", tabId,
                    "url", url,
                    "title", title,
                    "chunkIndex", chunk.getIndex(),
                    "chunkType", chunk.getType(),
                    "timestamp", currentTime
                ));

                TextSegment segment = TextSegment.from(chunk.getText(), metadata);
                segments.add(segment);
            }
            
            // 批量生成嵌入向量
            List<Embedding> embeddings = embeddingModel.embedAll(segments).content();
            
            // 存储到向量数据库
            embeddingStore.addAll(embeddings, segments);
            
            // 更新时间戳
            tabContentTimestamps.put(tabKey, currentTime);
            
            log.info("✅ 标签页内容索引完成: tabId={}, 分块数={}", tabId, chunks.size());
            
        } catch (Exception e) {
            log.error("❌ 索引标签页内容失败: tabId={}, error={}", tabId, e.getMessage(), e);
        }
    }
    
    /**
     * 语义搜索标签页内容
     */
    public List<SearchResult> searchTabsContent(String query, int maxResults, double minScore, boolean includeContent) {
        try {
            log.info("🔍 执行语义搜索: query={}, maxResults={}, minScore={}", query, maxResults, minScore);
            
            // 生成查询向量
            Embedding queryEmbedding = embeddingModel.embed(query).content();
            
            // 执行搜索
            List<EmbeddingMatch<TextSegment>> matches;
            if (embeddingStore instanceof LuceneEmbeddingStore) {
                // 使用混合搜索
                LuceneEmbeddingStore luceneStore = (LuceneEmbeddingStore) embeddingStore;
                EmbeddingSearchResult<TextSegment> result = luceneStore.hybridSearch(
                    query, queryEmbedding, maxResults, minScore, null);
                matches = result.matches();
            } else {
                // 使用普通向量搜索
                EmbeddingSearchRequest request = EmbeddingSearchRequest.builder()
                    .queryEmbedding(queryEmbedding)
                    .maxResults(maxResults)
                    .minScore(minScore)
                    .build();
                matches = embeddingStore.search(request).matches();
            }
            
            // 转换为搜索结果
            List<SearchResult> results = matches.stream()
                .map(match -> convertToSearchResult(match, includeContent))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            
            log.info("✅ 语义搜索完成: 返回 {} 个结果", results.size());
            return results;
            
        } catch (Exception e) {
            log.error("❌ 语义搜索失败: query={}, error={}", query, e.getMessage(), e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 移除标签页内容
     */
    public void removeTabContent(int tabId, String url) {
        try {
            String tabKey = tabId + ":" + url;
            tabContentTimestamps.remove(tabKey);

            // 从向量数据库中删除对应的内容
            if (embeddingStore instanceof LuceneEmbeddingStore) {
                LuceneEmbeddingStore luceneStore = (LuceneEmbeddingStore) embeddingStore;

                // 根据tabId删除文档
                luceneStore.removeByMetadata("tabId", String.valueOf(tabId));

                log.info("🗑️ 移除标签页内容: tabId={}, url={}", tabId, url);
            } else {
                log.warn("⚠️ 当前向量存储不支持按元数据删除，仅清理缓存");
            }

        } catch (Exception e) {
            log.error("❌ 移除标签页内容失败: tabId={}, error={}", tabId, e.getMessage(), e);
        }
    }
    
    /**
     * 内容分块 - 使用LangChain4j的专业分块工具
     */
    private List<TextChunk> chunkContent(String content, String title, String url) {
        List<TextChunk> chunks = new ArrayList<>();

        // 添加标题块
        if (title != null && !title.trim().isEmpty()) {
            chunks.add(new TextChunk(0, "title", title.trim()));
        }

        // 添加URL块（用于域名搜索）
        if (url != null && !url.trim().isEmpty()) {
            chunks.add(new TextChunk(1, "url", url));
        }

        // 使用LangChain4j的递归文档分块器处理内容
        if (content != null && !content.trim().isEmpty()) {
            try {
                // 创建文档对象
                Document document = Document.from(content.trim());

                // 配置分块器：400字符块大小，50字符重叠
                DocumentSplitter splitter = DocumentSplitters.recursive(400, 50);

                // 执行分块
                List<TextSegment> segments = splitter.split(document);

                // 转换为TextChunk
                int index = 2;
                for (TextSegment segment : segments) {
                    String segmentText = segment.text();
                    if (segmentText.length() > 20) { // 过滤太短的块
                        chunks.add(new TextChunk(index++, "content", segmentText));
                    }
                }

                log.debug("使用LangChain4j分块器处理内容: 原始长度={}, 分块数={}",
                    content.length(), segments.size());

            } catch (Exception e) {
                log.warn("LangChain4j分块失败，使用简单分块: {}", e.getMessage());

                // 降级到简单分块
                String cleanContent = content.trim().replaceAll("\\s+", " ");
                int chunkSize = 400;
                int overlap = 50;
                int index = 2;

                for (int i = 0; i < cleanContent.length(); i += chunkSize - overlap) {
                    int end = Math.min(i + chunkSize, cleanContent.length());
                    String chunkText = cleanContent.substring(i, end);

                    if (chunkText.length() > 20) {
                        chunks.add(new TextChunk(index++, "content", chunkText));
                    }

                    if (end >= cleanContent.length()) break;
                }
            }
        }

        return chunks;
    }
    
    /**
     * 转换为搜索结果
     */
    private SearchResult convertToSearchResult(EmbeddingMatch<TextSegment> match, boolean includeContent) {
        try {
            TextSegment segment = match.embedded();
            Map<String, Object> metadata = segment.metadata().toMap();
            
            SearchResult result = new SearchResult();
            result.setTabId((Integer) metadata.get("tabId"));
            result.setUrl((String) metadata.get("url"));
            result.setTitle((String) metadata.get("title"));
            result.setScore(match.score());
            result.setChunkType((String) metadata.get("chunkType"));
            result.setTimestamp((Long) metadata.get("timestamp"));
            
            if (includeContent) {
                result.setContent(segment.text());
            }
            
            return result;
            
        } catch (Exception e) {
            log.warn("转换搜索结果失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 文本块
     */
    private static class TextChunk {
        private final int index;
        private final String type;
        private final String text;
        
        public TextChunk(int index, String type, String text) {
            this.index = index;
            this.type = type;
            this.text = text;
        }
        
        public int getIndex() { return index; }
        public String getType() { return type; }
        public String getText() { return text; }
    }
    
    /**
     * 搜索结果
     */
    public static class SearchResult {
        private int tabId;
        private String url;
        private String title;
        private double score;
        private String content;
        private String chunkType;
        private long timestamp;
        
        // Getters and Setters
        public int getTabId() { return tabId; }
        public void setTabId(int tabId) { this.tabId = tabId; }
        
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        
        public double getScore() { return score; }
        public void setScore(double score) { this.score = score; }
        
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        
        public String getChunkType() { return chunkType; }
        public void setChunkType(String chunkType) { this.chunkType = chunkType; }
        
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    }
}
