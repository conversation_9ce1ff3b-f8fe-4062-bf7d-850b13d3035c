package com.zibbava.edgemind.cortex.service.impl;

import com.zibbava.edgemind.cortex.common.enums.NodeType;
import com.zibbava.edgemind.cortex.common.enums.ResultCode;
import com.zibbava.edgemind.cortex.common.exception.BusinessException;
import com.zibbava.edgemind.cortex.entity.KnowledgeDocument;
import com.zibbava.edgemind.cortex.entity.KnowledgeNode;
import com.zibbava.edgemind.cortex.service.DocumentViewerService;
import com.zibbava.edgemind.cortex.service.KnowledgeBaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 文档查看器服务实现类
 * 使用开源前端工具替代OnlyOffice
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentViewerServiceImpl implements DocumentViewerService {

    private final KnowledgeBaseService knowledgeBaseService;

    // 支持的文件类型映射
    private static final Map<String, String> SUPPORTED_FILE_TYPES = new LinkedHashMap<>();
    
    static {
        // PDF文档
        SUPPORTED_FILE_TYPES.put("pdf", "pdf");
        
        // Office文档
        SUPPORTED_FILE_TYPES.put("doc", "office");
        SUPPORTED_FILE_TYPES.put("docx", "office");
        SUPPORTED_FILE_TYPES.put("xls", "excel");
        SUPPORTED_FILE_TYPES.put("xlsx", "excel");
        SUPPORTED_FILE_TYPES.put("ppt", "office");
        SUPPORTED_FILE_TYPES.put("pptx", "office");
        
        // 文本文档
        SUPPORTED_FILE_TYPES.put("txt", "text");
        SUPPORTED_FILE_TYPES.put("md", "markdown");
        SUPPORTED_FILE_TYPES.put("rtf", "text");
        SUPPORTED_FILE_TYPES.put("csv", "text");
        
        // 代码文件作为文本处理
        SUPPORTED_FILE_TYPES.put("js", "text");
        SUPPORTED_FILE_TYPES.put("ts", "text");
        SUPPORTED_FILE_TYPES.put("py", "text");
        SUPPORTED_FILE_TYPES.put("java", "text");
        SUPPORTED_FILE_TYPES.put("cpp", "text");
        SUPPORTED_FILE_TYPES.put("c", "text");
        SUPPORTED_FILE_TYPES.put("h", "text");
        SUPPORTED_FILE_TYPES.put("html", "text");
        SUPPORTED_FILE_TYPES.put("css", "text");
        SUPPORTED_FILE_TYPES.put("scss", "text");
        SUPPORTED_FILE_TYPES.put("less", "text");
        SUPPORTED_FILE_TYPES.put("json", "text");
        SUPPORTED_FILE_TYPES.put("xml", "text");
        SUPPORTED_FILE_TYPES.put("yaml", "text");
        SUPPORTED_FILE_TYPES.put("yml", "text");
        SUPPORTED_FILE_TYPES.put("sql", "text");
        SUPPORTED_FILE_TYPES.put("sh", "text");
        SUPPORTED_FILE_TYPES.put("bat", "text");
        SUPPORTED_FILE_TYPES.put("ps1", "text");
        
        // 图片文件
        SUPPORTED_FILE_TYPES.put("jpg", "image");
        SUPPORTED_FILE_TYPES.put("jpeg", "image");
        SUPPORTED_FILE_TYPES.put("png", "image");
        SUPPORTED_FILE_TYPES.put("gif", "image");
        SUPPORTED_FILE_TYPES.put("bmp", "image");
        SUPPORTED_FILE_TYPES.put("svg", "image");
        SUPPORTED_FILE_TYPES.put("webp", "image");
        
        // 其他格式
        SUPPORTED_FILE_TYPES.put("zip", "archive");
        SUPPORTED_FILE_TYPES.put("rar", "archive");
        SUPPORTED_FILE_TYPES.put("7z", "archive");
        SUPPORTED_FILE_TYPES.put("tar", "archive");
        SUPPORTED_FILE_TYPES.put("gz", "archive");
    }

    @Override
    public Map<String, Object> getDocumentViewerConfig(String nodeId, Long userId, String userName) {
        log.info("生成节点 {} 的文档查看器配置", nodeId);

        // 1. 获取节点信息并检查权限和类型
        KnowledgeNode node = knowledgeBaseService.findNodeById(nodeId);
        if (node == null) {
            throw new BusinessException(ResultCode.RESOURCE_NOT_FOUND, "节点不存在: " + nodeId);
        }
        if (node.getType() != NodeType.FILE) {
            throw new BusinessException(ResultCode.NODE_TYPE_MISMATCH, "节点类型错误，无法为文件夹生成查看器配置");
        }

        // 2. 获取文档信息
        KnowledgeDocument document = knowledgeBaseService.findDocumentByNodeId(nodeId);
        if (document == null) {
            throw new BusinessException(ResultCode.RESOURCE_NOT_FOUND, "文档不存在: " + nodeId);
        }

        // 3. 确定查看器类型
        String fileName = node.getName();
        String viewerType = getViewerType(fileName);
        boolean isEditable = isEditable(fileName);

        // 4. 构建查看器配置
        Map<String, Object> viewerConfig = new HashMap<>();
        viewerConfig.put("nodeId", nodeId);
        viewerConfig.put("fileName", fileName);
        viewerConfig.put("viewerType", viewerType);
        viewerConfig.put("isEditable", isEditable);
        viewerConfig.put("fileUrl", "/wkg/api/knowledgebase/nodes/" + nodeId + "/download");
        viewerConfig.put("userId", userId);
        viewerConfig.put("userName", userName);

        // 5. 根据文件类型添加特定配置
        addTypeSpecificConfig(viewerConfig, viewerType, fileName);

        Map<String, Object> result = new HashMap<>();
        result.put("viewerConfig", viewerConfig);
        result.put("vectorStatus", document.getVectorStatus());

        log.info("为节点 {} 生成文档查看器配置成功，类型: {}", nodeId, viewerType);
        return result;
    }

    @Override
    public Map<String, String> getSupportedFileTypes() {
        return new HashMap<>(SUPPORTED_FILE_TYPES);
    }

    @Override
    public String getViewerType(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "text";
        }
        
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        return SUPPORTED_FILE_TYPES.getOrDefault(extension, "text");
    }

    @Override
    public boolean isEditable(String fileName) {
        String viewerType = getViewerType(fileName);
        // 目前支持编辑的类型：文本、Markdown、代码
        return "text".equals(viewerType) || "markdown".equals(viewerType) || "code".equals(viewerType);
    }

    /**
     * 根据查看器类型添加特定配置
     */
    private void addTypeSpecificConfig(Map<String, Object> config, String viewerType, String fileName) {
        switch (viewerType) {
            case "code":
                // 为代码文件添加语言配置
                String language = getCodeLanguage(fileName);
                config.put("language", language);
                config.put("theme", "vs-dark");
                break;
            case "markdown":
                config.put("enablePreview", true);
                config.put("enableEdit", true);
                break;
            case "pdf":
                config.put("enableDownload", true);
                config.put("enablePrint", true);
                break;
            case "office":
                config.put("readonly", true); // Office文档暂时只读
                break;
            case "image":
                config.put("enableZoom", true);
                config.put("enableRotate", true);
                break;
        }
    }

    /**
     * 根据文件扩展名获取代码语言
     */
    private String getCodeLanguage(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "plaintext";
        }
        
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        
        Map<String, String> languageMap = new HashMap<>();
        languageMap.put("js", "javascript");
        languageMap.put("ts", "typescript");
        languageMap.put("py", "python");
        languageMap.put("java", "java");
        languageMap.put("cpp", "cpp");
        languageMap.put("c", "c");
        languageMap.put("h", "c");
        languageMap.put("html", "html");
        languageMap.put("css", "css");
        languageMap.put("scss", "scss");
        languageMap.put("less", "less");
        languageMap.put("json", "json");
        languageMap.put("xml", "xml");
        languageMap.put("yaml", "yaml");
        languageMap.put("yml", "yaml");
        languageMap.put("sql", "sql");
        languageMap.put("sh", "shell");
        languageMap.put("bat", "batch");
        languageMap.put("ps1", "powershell");
        
        return languageMap.getOrDefault(extension, "plaintext");
    }
}
