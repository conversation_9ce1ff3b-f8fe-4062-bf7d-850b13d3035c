package com.zibbava.edgemind.cortex.service;

import cn.hutool.core.thread.ThreadUtil;
import com.zibbava.edgemind.cortex.dto.ModelDownloadRequest;
import com.zibbava.edgemind.cortex.enums.ModelInfo;
import com.zibbava.edgemind.cortex.model.ModelConfig;
import com.zibbava.edgemind.cortex.model.ModelType;
import com.zibbava.edgemind.cortex.model.ollama.OllamaModel;
import com.zibbava.edgemind.cortex.util.OllamaUtils;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 模型服务，用于管理所有可用的AI模型
 */
@Service
@Slf4j
public class ModelService {

    private final List<ModelConfig> models = new ArrayList<>();

    @Value("${run.env:dev}")
    private String runEnv;

    // 模型下载任务管理
    private final Map<String, ModelDownloadTask> downloadTasks = new ConcurrentHashMap<>();
    private final ExecutorService downloadExecutor = Executors.newFixedThreadPool(3);

    public ModelService() {
    }

    /**
     * 初始化支持的模型列表
     */
    @PostConstruct
    public void init() {
        log.info("初始化模型服务...");

        if ("dev".equals(runEnv)) {
            // 开发环境：尝试检测本地Ollama模型
            try {
                boolean ollamaAvailable = OllamaUtils.isOllamaServiceAvailable();
                if (ollamaAvailable) {
                    List<OllamaModel> llmModels = OllamaUtils.getLocalLLMs(true);
                    if (!llmModels.isEmpty()) {
                        models.clear();
                        log.info("找到{}个本地Ollama大语言模型", llmModels.size());
                        for (OllamaModel model : llmModels) {
                            String modelName = model.getName();
                            models.add(new ModelConfig(modelName, modelName, ModelType.OLLAMA));
                            log.info("添加模型: {}", modelName);
                        }
                    } else {
                        log.info("未找到本地Ollama模型，使用默认配置");
                    }
                } else {
                    log.info("Ollama服务不可用，使用默认配置");
                }
            } catch (Exception e) {
                log.warn("检测Ollama模型时出现异常: {}", e.getMessage());
            }
        } else if ("demo".equals(runEnv)) {
            // 演示环境：使用云端模型
            models.add(new ModelConfig("qwen-turbo", "qwen-turbo", ModelType.OPENAI));
        }

        log.info("模型服务初始化完成，共加载{}个模型", models.size());
    }


    /**
     * 获取所有支持的模型列表
     *
     * @return 模型列表
     */
    public List<ModelConfig> getAllModels() {
        return this.models;
    }

    /**
     * 获取前端可用的模型信息（仅LLM模型用于对话）
     *
     * @return 模型信息列表，每个模型包含value和name
     */
    public List<Map<String, String>> getModelsForFrontend() {
        return this.models.stream()
                .map(ModelConfig::toMap)
                .collect(Collectors.toList());
    }

    /**
     * 根据模型标识符获取模型配置
     *
     * @param modelIdentifier 模型标识符
     * @return 模型配置（可能为空）
     */
    public Optional<ModelConfig> getModelByIdentifier(String modelIdentifier) {
        return this.models.stream()
                .filter(model -> model.getValue().equals(modelIdentifier))
                .findFirst();
    }

    /**
     * 获取可用于下载的模型列表
     *
     * @return 可下载模型列表
     */
    public List<Map<String, Object>> getAvailableModels() {
        List<Map<String, Object>> availableModels = new ArrayList<>();

        try {
            // 获取流行模型列表（来自Ollama官方）
            List<Map<String, Object>> popularModels = getPopularModelsList();

            // 获取本地已安装模型
            List<String> localModelNames = getLocalModelNames();

            // 标记已安装的模型
            for (Map<String, Object> model : popularModels) {
                String modelId = (String) model.get("id");
                model.put("isLocal", localModelNames.contains(modelId));
            }

            availableModels.addAll(popularModels);

        } catch (Exception e) {
            log.error("获取可用模型列表失败", e);
        }

        return availableModels;
    }

    /**
     * 获取Ollama流行模型列表
     * 使用ModelInfo枚举类获取模型信息
     */
    private List<Map<String, Object>> getPopularModelsList() {
        List<Map<String, Object>> models = new ArrayList<>();

        // 遍历所有模型枚举，生成模型列表
        for (ModelInfo modelInfo : ModelInfo.values()) {
            addPopularModelFromEnum(models, modelInfo);
        }

        // 对模型列表进行排序：必需模型排在最前面，然后按类型和大小排序
        models.sort((m1, m2) -> {
            boolean isRequired1 = (Boolean) m1.getOrDefault("isRequired", false);
            boolean isRequired2 = (Boolean) m2.getOrDefault("isRequired", false);

            // 必需模型优先
            if (isRequired1 && !isRequired2) return -1;
            if (!isRequired1 && isRequired2) return 1;

            // 如果都是必需模型或都不是必需模型，按类型排序（embedding优先）
            String type1 = (String) m1.get("type");
            String type2 = (String) m2.get("type");

            if ("embedding".equals(type1) && !"embedding".equals(type2)) return -1;
            if (!"embedding".equals(type1) && "embedding".equals(type2)) return 1;

            // 同类型按大小排序
            Double size1 = (Double) m1.get("size");
            Double size2 = (Double) m2.get("size");
            return Double.compare(size1, size2);
        });

        ThreadUtil.execute(this::init);
        return models;
    }

    /**
     * 获取已安装的模型列表
     *
     * @return 已安装模型列表
     */
    public List<Map<String, Object>> getInstalledModels() {
        List<Map<String, Object>> installedModels = new ArrayList<>();

        try {
            // 使用OllamaUtils获取本地模型列表
            List<OllamaModel> localModels = OllamaUtils.getLocalModels();

            for (OllamaModel model : localModels) {
                Map<String, Object> modelInfo = new HashMap<>();
                modelInfo.put("id", model.getName());
                modelInfo.put("name", formatModelName(model.getName()));
                modelInfo.put("size", formatSize(model.getSize()));
                modelInfo.put("type", model.getModelType().toString());
                modelInfo.put("isLocal", true);

                installedModels.add(modelInfo);
            }

        } catch (Exception e) {
            log.error("获取已安装模型列表失败", e);
        }

        return installedModels;
    }

    // BGE-Large模型检查方法已删除，现在使用本地BGE中文嵌入模型

    // BGE-Large模型检查方法已删除，现在使用本地BGE中文嵌入模型

    /**
     * 检查Ollama服务状态
     *
     * @return Ollama服务状态信息
     */
    public Map<String, Object> checkOllamaStatus() {
        Map<String, Object> result = new HashMap<>();

        try {
            boolean isAvailable = OllamaUtils.isOllamaServiceAvailable();
            result.put("available", isAvailable);
            result.put("message", isAvailable ? "Ollama服务正常运行" : "Ollama服务不可用");

            if (isAvailable) {
                // 如果服务可用，获取一些基本信息
                try {
                    List<OllamaModel> models = OllamaUtils.getLocalLLMs();
                    result.put("installedModelsCount", models.size());
                } catch (Exception e) {
                    log.warn("获取已安装模型数量失败: {}", e.getMessage());
                    result.put("installedModelsCount", 0);
                }
            } else {
                result.put("ollamaDownloadUrl", "https://ollama.com/download");
                result.put("installInstructions", "请访问 https://ollama.com/download 下载并安装Ollama");
            }

        } catch (Exception e) {
            log.error("检查Ollama服务状态时发生错误", e);
            result.put("available", false);
            result.put("message", "检查Ollama服务状态时发生错误: " + e.getMessage());
            result.put("ollamaDownloadUrl", "https://ollama.com/download");
        }

        return result;
    }

    /**
     * 开始下载模型
     *
     * @param request 下载请求
     * @return 下载任务信息
     */
    public Map<String, Object> startModelDownload(ModelDownloadRequest request) {
        String modelName = request.getModelName();

        // 首先检测Ollama服务是否可用
        boolean ollamaAvailable = OllamaUtils.isOllamaServiceAvailable();
        if (!ollamaAvailable) {
            log.warn("Ollama服务不可用，无法下载模型: {}", modelName);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", "OLLAMA_NOT_AVAILABLE");
            result.put("message", "Ollama服务不可用，请先启动Ollama服务");
            result.put("ollamaDownloadUrl", "https://ollama.com/download");
            return result;
        }

        String taskId = UUID.randomUUID().toString();

        // 创建下载任务
        ModelDownloadTask task = new ModelDownloadTask();
        task.setTaskId(taskId);
        task.setModelName(modelName);
        task.setStatus("准备中");
        task.setProgress(0);
        task.setStartTime(System.currentTimeMillis());

        // 存储任务
        downloadTasks.put(taskId, task);

        // 异步执行下载
        downloadExecutor.submit(() -> {
            try {
                modelDownload(taskId, modelName);
            } catch (Exception e) {
                log.error("模型下载失败: {}", modelName, e);
                task.setStatus("失败");
                task.setError(e.getMessage());
            }
        });

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("taskId", taskId);
        result.put("modelName", modelName);
        result.put("status", "started");

        return result;
    }

    /**
     * 获取下载进度
     *
     * @param taskId 任务ID
     * @return 下载进度信息
     */
    public Map<String, Object> getDownloadProgress(String taskId) {
        ModelDownloadTask task = downloadTasks.get(taskId);

        if (task == null) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "任务不存在");
            return error;
        }

        Map<String, Object> progress = new HashMap<>();
        progress.put("taskId", taskId);
        progress.put("modelName", task.getModelName());
        progress.put("status", task.getStatus());
        progress.put("progress", task.getProgress());
        progress.put("startTime", task.getStartTime());

        if (task.getEndTime() > 0) {
            progress.put("endTime", task.getEndTime());
            progress.put("duration", (task.getEndTime() - task.getStartTime()) / 1000 + "秒");
        }

        if (task.getError() != null) {
            progress.put("error", task.getError());
        }

        return progress;
    }

    /**
     * 流式获取下载进度
     * 使用SSE向前端推送实时进度信息
     *
     * @param taskId 任务ID
     * @return SSE发射器
     */
    public SseEmitter streamDownloadProgress(String taskId) {
        ModelDownloadTask task = downloadTasks.get(taskId);

        if (task == null) {
            SseEmitter emitter = new SseEmitter();
            try {
                Map<String, Object> error = new HashMap<>();
                error.put("status", "error");
                error.put("message", "任务不存在");
                emitter.send(SseEmitter.event().data(error));
                emitter.complete();
            } catch (IOException e) {
                emitter.completeWithError(e);
            }
            return emitter;
        }

        // 创建SSE发射器，设置超时时间为4小时，以满足大型模型下载需求1-2小时
        // 4小时 = 14400000毫秒
        SseEmitter emitter = new SseEmitter(14400000L); // 4小时超时

        try {
            // 防止浏览器缓存
            emitter.send(SseEmitter.event().comment("connected"));

            // 将发射器添加到任务的发射器列表中
            task.getEmitters().add(emitter);
        } catch (IOException e) {
            log.error("发送SSE初始连接事件失败: {}", e.getMessage());
            emitter.completeWithError(e);
            return emitter;
        }

        // 设置回调，在各种情况下移除发射器
        emitter.onCompletion(() -> {
            task.getEmitters().remove(emitter);
            log.debug("流式连接完成，任务ID: {}", taskId);
            ThreadUtil.execute(this::init);
        });

        emitter.onTimeout(() -> {
            task.getEmitters().remove(emitter);
            log.warn("流式连接超时，任务ID: {}", taskId);
        });

        emitter.onError(e -> {
            task.getEmitters().remove(emitter);
            log.error("流式连接错误，任务ID: {}, 错误: {}", taskId, e.getMessage());
            ThreadUtil.execute(this::init);
        });

        // 发送当前进度信息
        try {
            // 获取当前任务的最新进度信息
            Map<String, Object> progressData = getDownloadProgress(taskId);
            emitter.send(SseEmitter.event().data(progressData));

            // 如果下载正在进行且没有完成，则尝试开始或继续下载
            if (task.getEndTime() == 0 && task.getProgress() < 100 && !"failed".equals(task.getStatus()) && !"失败".equals(task.getStatus())) {
                // 在另一个线程中尝试直接使用流式 API 下载模型，如果原下载非流式
                if (task.isStreamMode()) {
                    log.debug("模型 {} 已在使用流式模式下载中，直接连接到现有流", task.getModelName());
                } else {
                    // 标记任务为流式模式
                    task.setStreamMode(true);
                    log.info("从非流式切换为流式模式下载模型: {}", task.getModelName());
                }
            }
        } catch (IOException e) {
            emitter.completeWithError(e);
        }

        return emitter;
    }

    /**
     * 使用Ollama API下载模型并获取真实进度
     *
     * @param taskId    任务ID
     * @param modelName 模型名称
     */
    private void modelDownload(String taskId, String modelName) {
        ModelDownloadTask task = downloadTasks.get(taskId);

        if (task == null) return;

        try {
            // 更新状态为准备中
            task.setStatus("准备中");
            task.setProgress(0);
            notifyProgressUpdate(task);

            log.info("开始下载模型: {}", modelName);

            // 创建多线程执行器处理下载请求
            Thread downloadThread = new Thread(() -> {
                try {
                    // 使用OllamaUtils的流式请求直接下载模型并获取实时进度
                    log.info("开始使用流式请求下载模型: {}", modelName);

                    // 设置初始状态
                    task.setStatus("准备下载");
                    task.setProgress(1);
                    notifyProgressUpdate(task);

                    // 使用回调函数处理进度更新
                    boolean downloadSuccess = OllamaUtils.pullModelWithStream(modelName, (completed, total, status) -> {
                        try {
                            // 如果有字节计数信息
                            if (total > 0) {
                                int progressPercent = (int) ((completed * 100) / total);
                                task.setProgress(progressPercent);
                                task.setStatus("下载中: " + formatSize(completed) + " / " + formatSize(total));
                            } else if (status != null && !status.isEmpty()) {
                                // 只有状态信息
                                task.setStatus(status);

                                // 检查是否完成
                                if ("complete".equals(status) || "success".equals(status) || "done".equals(status)) {
                                    task.setProgress(100);
                                    task.setStatus("完成");
                                    ThreadUtil.execute(this::init);
                                }
                            }

                            // 通知进度更新
                            notifyProgressUpdate(task);
                        } catch (Exception e) {
                            log.error("处理进度更新时发生错误", e);
                        }
                    });

                    // 检查下载结果
                    if (!downloadSuccess) {
                        task.setStatus("失败");
                        task.setError("模型下载过程中出错");
                        task.setEndTime(System.currentTimeMillis());
                        notifyProgressUpdate(task);
                        return;
                    }

                    // 下载成功后再次确认模型是否存在
                    List<String> localModels = getLocalModelNames();
                    boolean downloadComplete = localModels.contains(modelName);

                    // 更新最终状态
                    if (downloadComplete) {
                        task.setStatus("完成");
                        task.setProgress(100);
                    } else if ("失败".equals(task.getStatus())) {
                        // 状态已经是失败，无需更改
                    } else {
                        task.setStatus("超时");
                        task.setError("下载超时，请在模型列表中检查状态");
                    }

                    task.setEndTime(System.currentTimeMillis());
                    notifyProgressUpdate(task);

                    // 完成所有SSE连接
                    for (SseEmitter emitter : new ArrayList<>(task.getEmitters())) {
                        try {
                            emitter.complete();
                        } catch (Exception e) {
                            log.error("关闭SSE连接失败", e);
                        }
                    }
                } catch (Exception e) {
                    // 检查是否为中断异常
                    if (e instanceof InterruptedException) {
                        Thread.currentThread().interrupt();
                        task.setStatus("中断");
                        task.setError("下载被中断");
                    } else {
                        log.error("下载模型时发生错误", e);
                        task.setStatus("失败");
                        task.setError(e.getMessage());
                    }
                    notifyProgressUpdate(task);
                }
            });

            downloadThread.setDaemon(true); // 设置为守护线程
            downloadThread.start();

        } catch (Exception e) {
            log.error("启动下载线程时发生错误", e);
            task.setStatus("失败");
            task.setError(e.getMessage());
            notifyProgressUpdate(task);
        }
    }

    /**
     * 从ModelInfo枚举添加模型到列表
     */
    private void addPopularModelFromEnum(List<Map<String, Object>> models, ModelInfo modelInfo) {
        Map<String, Object> model = new HashMap<>();
        model.put("id", modelInfo.getId());
        model.put("name", modelInfo.getName());
        model.put("description", modelInfo.getDescription());
        model.put("size", modelInfo.getSize());
        model.put("type", modelInfo.getType());
        // 添加必需模型标识
        model.put("isRequired", modelInfo.isRequired());
        // isRecommended字段已移除，推荐逻辑由前端根据兼容性评分动态计算
        model.put("supportsThinking", modelInfo.isSupportsThinking());
        model.put("supportsMultimodal", modelInfo.isSupportsMultimodal());

        // 硬件要求
        Map<String, String> requirements = new HashMap<>();
        double size = modelInfo.getSize();
        if (size > 30) {
            requirements.put("minRam", "64GB");
            requirements.put("minVRam", "40GB");
            requirements.put("diskSpace", Math.ceil(size * 1.1) + "GB");
        } else if (size > 10) {
            requirements.put("minRam", "32GB");
            requirements.put("minVRam", "16GB");
            requirements.put("diskSpace", Math.ceil(size * 1.1) + "GB");
        } else if (size > 5) {
            requirements.put("minRam", "16GB");
            requirements.put("minVRam", "8GB");
            requirements.put("diskSpace", Math.ceil(size * 1.1) + "GB");
        } else {
            requirements.put("minRam", "8GB");
            requirements.put("minVRam", "4GB");
            requirements.put("diskSpace", Math.ceil(size * 1.1) + "GB");
        }
        model.put("requirements", requirements);

        models.add(model);
    }


    /**
     * 获取本地已安装模型名称列表
     */
    private List<String> getLocalModelNames() {
        try {
            return OllamaUtils.getLocalModels().stream()
                    .map(OllamaModel::getName)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取本地模型列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 格式化模型名称为更友好的显示形式
     */
    private String formatModelName(String rawName) {
        if (rawName == null || rawName.isEmpty()) {
            return "未知模型";
        }

        // 替换常见前缀和分隔符
        String name = rawName.replace(":", " ").replace("-", " ");

        // 处理常见模型名称
        if (name.toLowerCase().contains("qwen")) {
            name = name.replaceAll("(?i)qwen", "Qwen");
        } else if (name.toLowerCase().contains("llama")) {
            name = name.replaceAll("(?i)llama", "Llama");
        }

        // 参数大小处理
        if (name.contains("7b")) {
            name = name.replaceAll("(?i)7b", "7B");
        } else if (name.contains("13b")) {
            name = name.replaceAll("(?i)13b", "13B");
        } else if (name.contains("14b")) {
            name = name.replaceAll("(?i)14b", "14B");
        } else if (name.contains("70b")) {
            name = name.replaceAll("(?i)70b", "70B");
        }

        return name;
    }

    /**
     * 格式化文件大小
     */
    private String formatSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024 * 1024));
        }
    }

    /**
     * 通知所有订阅者进度更新
     */
    private void notifyProgressUpdate(ModelDownloadTask task) {
        for (SseEmitter emitter : new ArrayList<>(task.getEmitters())) {
            try {
                emitter.send(SseEmitter.event().data(getDownloadProgress(task.getTaskId())));
            } catch (IOException e) {
                emitter.completeWithError(e);
                task.getEmitters().remove(emitter);
            }
        }
    }

    /**
     * 模型下载任务内部类
     */
    private static class ModelDownloadTask {
        private String taskId;
        private String modelName;
        private String status;
        private int progress;
        private long startTime;
        private long endTime;
        private String error;
        private boolean streamMode = false; // 标记是否使用流式模式下载
        private final List<SseEmitter> emitters = new ArrayList<>();

        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public String getModelName() {
            return modelName;
        }

        public void setModelName(String modelName) {
            this.modelName = modelName;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public int getProgress() {
            return progress;
        }

        public void setProgress(int progress) {
            this.progress = progress;
        }

        public long getStartTime() {
            return startTime;
        }

        public void setStartTime(long startTime) {
            this.startTime = startTime;
        }

        public long getEndTime() {
            return endTime;
        }

        public void setEndTime(long endTime) {
            this.endTime = endTime;
        }

        public String getError() {
            return error;
        }

        public void setError(String error) {
            this.error = error;
        }

        public boolean isStreamMode() {
            return streamMode;
        }

        public void setStreamMode(boolean streamMode) {
            this.streamMode = streamMode;
        }

        public List<SseEmitter> getEmitters() {
            return emitters;
        }
    }

    /**
     * 删除模型
     *
     * @param modelName 模型名称
     * @return 是否删除成功
     */
    public boolean deleteModel(String modelName) {
        if (modelName == null || modelName.trim().isEmpty()) {
            log.error("模型名称不能为空");
            return false;
        }

        try {
            log.info("开始删除模型: {}", modelName);
            boolean result = OllamaUtils.deleteModel(modelName);

            if (result) {
                log.info("模型删除成功: {}", modelName);

                // 从缓存的模型列表中移除已删除的模型
                models.removeIf(model -> model.getValue().equals(modelName));
                log.info("从模型列表缓存中移除模型: {}", modelName);

                // 如果需要，可以在这里重新从Ollama获取最新的模型列表
                // refreshModelsList();
            } else {
                log.error("模型删除失败: {}", modelName);
            }

            return result;
        } catch (Exception e) {
            log.error("删除模型时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }
}