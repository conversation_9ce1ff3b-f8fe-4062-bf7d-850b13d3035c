package com.zibbava.edgemind.cortex.service;

import cn.dev33.satoken.secure.SaSecureUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.List;
import java.util.Map;

/**
 * 密码初始化服务
 * 用于首次启动时生成随机密码
 */
@Service
public class PasswordInitService implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(PasswordInitService.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private SystemSettingsService systemSettingsService;

    // 默认密码（schema.sql中设置的）
    private static final String DEFAULT_PASSWORD = "duanzhiadmin";
    private static final String DEFAULT_USERNAME = "admin";

    // 首次启动标记键
    private static final String FIRST_START_KEY = "system.first_start_completed";

    // 生成的密码键
    private static final String GENERATED_PASSWORD_KEY = "system.generated_password";

    // 密码字符集
    private static final String PASSWORD_CHARS = "ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789";
    private static final int PASSWORD_LENGTH = 8;
    
    @Override
    public void run(String... args) throws Exception {
        // 统一执行密码初始化
        initializePassword();
    }

    /**
     * 初始化密码
     */
    private void initializePassword() {
        try {
            // 检查是否存在用户表
            if (!isUserTableExists()) {
                logger.warn("用户表不存在，跳过密码初始化");
                return;
            }

            // 优先检查是否已完成首次启动
            String firstStartCompleted = systemSettingsService.getSettingValue(FIRST_START_KEY);
            if ("true".equals(firstStartCompleted)) {
                logger.info("首次启动已完成，跳过密码初始化");
                return;
            }

            // 检查admin用户是否使用默认密码
            if (isUsingDefaultPassword()) {
                // 生成新密码并更新数据库
                String newPassword = generateRandomPassword();
                updateAdminPassword(newPassword);

                // 保存生成的密码到系统设置
                systemSettingsService.updateSettingValue(GENERATED_PASSWORD_KEY, newPassword, "首次启动生成的管理员密码");

                logger.info("系统密码已初始化，请访问登录页面查看密码提示");
            } else {
                // 如果密码已被修改，检查是否已有生成的密码记录
                String existingPassword = systemSettingsService.getSettingValue(GENERATED_PASSWORD_KEY);
                if (existingPassword == null || existingPassword.trim().isEmpty()) {
                    // 没有生成密码记录，说明是用户手动修改的密码，标记首次启动完成
                    systemSettingsService.updateSettingValue(FIRST_START_KEY, "true", "首次启动完成标记");
                    logger.info("检测到用户已手动修改密码，标记首次启动完成");
                } else {
                    // 有生成密码记录，说明是系统生成的密码，等待用户登录后标记完成
                    logger.info("检测到系统已生成密码，等待用户首次登录");
                }
            }

        } catch (Exception e) {
            logger.error("密码初始化失败", e);
        }
    }
    
    /**
     * 检查用户表是否存在
     */
    private boolean isUserTableExists() {
        try {
            jdbcTemplate.queryForObject("SELECT COUNT(*) FROM sys_user LIMIT 1", Integer.class);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检查是否使用默认密码
     */
    private boolean isUsingDefaultPassword() {
        try {
            // 查询admin用户的密码
            List<Map<String, Object>> users = jdbcTemplate.queryForList(
                "SELECT password FROM sys_user WHERE username = ?", DEFAULT_USERNAME);
            
            if (users.isEmpty()) {
                logger.warn("未找到admin用户");
                return false;
            }
            
            String storedPassword = (String) users.get(0).get("password");
            
            // 检查是否为默认密码的MD5加密结果
            return storedPassword.equals(SaSecureUtil.md5(DEFAULT_PASSWORD));
            
        } catch (Exception e) {
            logger.error("检查默认密码失败", e);
            return false;
        }
    }
    
    /**
     * 生成随机密码
     */
    private String generateRandomPassword() {
        SecureRandom random = new SecureRandom();
        StringBuilder password = new StringBuilder(PASSWORD_LENGTH);
        
        for (int i = 0; i < PASSWORD_LENGTH; i++) {
            int index = random.nextInt(PASSWORD_CHARS.length());
            password.append(PASSWORD_CHARS.charAt(index));
        }
        
        return password.toString();
    }
    
    /**
     * 更新admin用户密码
     */
    private void updateAdminPassword(String newPassword) {
        try {
            String encodedPassword = SaSecureUtil.md5(newPassword);
            
            int updated = jdbcTemplate.update(
                "UPDATE sys_user SET password = ? WHERE username = ?", 
                encodedPassword, DEFAULT_USERNAME);
            
            if (updated > 0) {
                logger.info("企业版admin密码已更新");
            } else {
                logger.warn("admin密码更新失败");
            }
            
        } catch (Exception e) {
            logger.error("更新admin密码失败", e);
        }
    }

    /**
     * 检查是否首次启动
     */
    public boolean isFirstStart() {
        String firstStartCompleted = systemSettingsService.getSettingValue(FIRST_START_KEY);
        return !"true".equals(firstStartCompleted);
    }

    /**
     * 获取首次启动生成的密码
     */
    public String getFirstStartPassword() {
        return systemSettingsService.getSettingValue(GENERATED_PASSWORD_KEY);
    }

    /**
     * 标记首次启动完成
     */
    public void markFirstStartCompleted() {
        systemSettingsService.updateSettingValue(FIRST_START_KEY, "true", "首次启动完成标记");
        logger.info("首次启动已完成");
    }
}
