package com.zibbava.edgemind.cortex.browser.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 浏览器内容处理服务
 * 负责将从浏览器获取的原始HTML转换为智能提取的文本内容
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BrowserContentProcessorService {

    private final ObjectMapper objectMapper;
    
    // 主要内容选择器（按优先级排序）
    private static final String[] MAIN_CONTENT_SELECTORS = {
        "main", "article", "[role='main']", ".main-content", ".content",
        ".post-content", ".article-content", ".entry-content", ".post-body",
        "#content", "#main", "#article", ".article", ".post"
    };
    
    // 需要排除的元素选择器
    private static final String[] EXCLUDE_SELECTORS = {
        "nav", "header", "footer", "aside", ".nav", ".navigation", ".menu",
        ".sidebar", ".side-bar", ".widget", ".header", ".footer", ".breadcrumb",
        ".advertisement", ".ads", ".ad", ".banner", ".social", ".share", ".sharing",
        ".comments", ".comment", ".comment-form", ".related", ".recommended",
        ".suggestions", "script", "style", "noscript"
    };
    
    // 清理文本的正则表达式
    private static final Pattern WHITESPACE_PATTERN = Pattern.compile("\\s+");
    private static final Pattern NEWLINE_PATTERN = Pattern.compile("\\n\\s*\\n");
    
    /**
     * 处理浏览器返回的内容
     */
    public ContentProcessResult processContent(String htmlContent, Map<String, Object> metadata) {
        try {
            log.info("🔍 开始处理浏览器内容: url={}", metadata.get("url"));
            
            // 解析HTML
            Document doc = Jsoup.parse(htmlContent);
            
            // 获取请求格式
            String requestedFormat = (String) metadata.getOrDefault("requestedFormat", "text");
            boolean includeHidden = (Boolean) metadata.getOrDefault("includeHidden", false);
            
            ContentProcessResult result = new ContentProcessResult();
            result.setUrl((String) metadata.get("url"));
            result.setTitle((String) metadata.get("title"));
            result.setMetadata(metadata);
            
            if ("html".equals(requestedFormat)) {
                // 返回HTML格式
                result.setContent(htmlContent);
                result.setContentType("html");
                result.setLength(htmlContent.length());
            } else {
                // 智能文本提取
                String textContent = extractIntelligentText(doc, includeHidden);
                result.setContent(textContent);
                result.setContentType("text");
                result.setLength(textContent.length());
                result.setWordCount(countWords(textContent));
                
                // 提取结构化数据内容
                String structuredContent = extractStructuredContent(metadata);
                if (!structuredContent.isEmpty()) {
                    result.setContent(result.getContent() + "\n\n--- 结构化数据 ---\n\n" + structuredContent);
                }
            }
            
            log.info("✅ 内容处理完成: 长度={}, 词数={}", result.getLength(), result.getWordCount());
            return result;
            
        } catch (Exception e) {
            log.error("❌ 内容处理失败: {}", e.getMessage(), e);
            
            // 返回错误结果
            ContentProcessResult errorResult = new ContentProcessResult();
            errorResult.setContent("内容处理失败: " + e.getMessage());
            errorResult.setContentType("error");
            errorResult.setMetadata(metadata);
            return errorResult;
        }
    }
    
    /**
     * 智能文本提取
     */
    private String extractIntelligentText(Document doc, boolean includeHidden) {
        try {
            // 1. 尝试找到主要内容区域
            Element mainElement = findMainContentElement(doc);
            
            if (mainElement != null) {
                log.debug("找到主要内容区域: {}", mainElement.tagName());
                return cleanText(extractTextFromElement(mainElement, includeHidden));
            }
            
            // 2. 如果没有找到主要内容区域，处理整个body
            Element body = doc.body();
            if (body != null) {
                // 移除不需要的元素
                removeUnwantedElements(body);
                return cleanText(extractTextFromElement(body, includeHidden));
            }
            
            // 3. 降级到整个文档
            return cleanText(doc.text());
            
        } catch (Exception e) {
            log.warn("智能文本提取失败，使用基础提取: {}", e.getMessage());
            return cleanText(doc.text());
        }
    }
    
    /**
     * 查找主要内容元素
     */
    private Element findMainContentElement(Document doc) {
        for (String selector : MAIN_CONTENT_SELECTORS) {
            Elements elements = doc.select(selector);
            for (Element element : elements) {
                String text = element.text();
                if (text != null && text.trim().length() > 100) {
                    return element;
                }
            }
        }
        return null;
    }
    
    /**
     * 移除不需要的元素
     */
    private void removeUnwantedElements(Element element) {
        for (String selector : EXCLUDE_SELECTORS) {
            element.select(selector).remove();
        }
    }
    
    /**
     * 从元素提取文本
     */
    private String extractTextFromElement(Element element, boolean includeHidden) {
        if (!includeHidden) {
            // 移除隐藏元素
            element.select("[style*='display:none'], [style*='display: none'], [hidden]").remove();
        }
        
        return element.text();
    }
    
    /**
     * 清理文本
     */
    private String cleanText(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }
        
        return NEWLINE_PATTERN.matcher(
            WHITESPACE_PATTERN.matcher(text.trim()).replaceAll(" ")
        ).replaceAll("\n\n").substring(0, Math.min(text.length(), 50000));
    }
    
    /**
     * 提取结构化数据内容
     */
    private String extractStructuredContent(Map<String, Object> metadata) {
        try {
            @SuppressWarnings("unchecked")
            List<Object> jsonLdData = (List<Object>) metadata.get("jsonLd");
            
            if (jsonLdData == null || jsonLdData.isEmpty()) {
                return "";
            }
            
            StringBuilder content = new StringBuilder();
            
            for (Object data : jsonLdData) {
                try {
                    JsonNode node = objectMapper.valueToTree(data);
                    
                    // 提取文章内容
                    if (node.has("@type")) {
                        String type = node.get("@type").asText();
                        if ("Article".equals(type) || "NewsArticle".equals(type) || "BlogPosting".equals(type)) {
                            if (node.has("articleBody")) {
                                content.append(node.get("articleBody").asText()).append("\n\n");
                            }
                            if (node.has("description")) {
                                content.append(node.get("description").asText()).append("\n\n");
                            }
                        }
                    }
                } catch (Exception e) {
                    log.debug("处理JSON-LD数据失败: {}", e.getMessage());
                }
            }
            
            return content.toString().trim();
            
        } catch (Exception e) {
            log.warn("提取结构化数据内容失败: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * 统计词数
     */
    private int countWords(String text) {
        if (text == null || text.trim().isEmpty()) {
            return 0;
        }
        
        return text.trim().split("\\s+").length;
    }
    
    /**
     * 内容处理结果
     */
    public static class ContentProcessResult {
        private String content;
        private String contentType;
        private String url;
        private String title;
        private int length;
        private int wordCount;
        private Map<String, Object> metadata;
        
        // Getters and Setters
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        
        public String getContentType() { return contentType; }
        public void setContentType(String contentType) { this.contentType = contentType; }
        
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        
        public int getLength() { return length; }
        public void setLength(int length) { this.length = length; }
        
        public int getWordCount() { return wordCount; }
        public void setWordCount(int wordCount) { this.wordCount = wordCount; }
        
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    }
}
