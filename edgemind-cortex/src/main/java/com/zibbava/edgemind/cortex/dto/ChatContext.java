package com.zibbava.edgemind.cortex.dto;

import com.zibbava.edgemind.cortex.entity.ChatConversation;
import lombok.Builder;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 聊天上下文 - 包含所有聊天相关的参数和状态
 */
@Data
@Builder
public class ChatContext {
    // 基本请求信息
    private String prompt;         // 用户输入的提示词
    private String modelName;      // 模型名称
    private MultipartFile imageFile; // 图片文件
    private Long conversationId;   // 会话ID
    private String title;          // 聊天标题
    
    // 知识库相关参数
    private String knowledgeNodeId; // 知识库节点ID
    private boolean isKnowledgeChat; // 是否为知识库聊天
    
    // 深度思考参数
    private Boolean enableThinking; // 是否启用深度思考功能
    
    // 增强检索参数
    private Boolean enableEnhancedRetrieval; // 是否启用增强检索功能

    // 工具调用参数
    private Boolean enableToolCalling; // 是否启用工具调用功能
    
    // 用户信息
    private Long userId;           // 用户ID
    
    // 会话信息
    private ChatConversation conversation; // 会话对象
    
    // 中间状态存储
    private String retrievedContext; // 从知识库检索的上下文
    private String finalPrompt;      // 最终提交给模型的提示词
    private String documentSources;  // 文档来源信息（用于前端显示）
    
    // 响应收集器
    private StringBuilder mainContentBuilder;     // 主要内容收集器
    private StringBuilder thinkingContentBuilder; // 思考内容收集器
    private AtomicBoolean isInsideThinkingBlock;  // 是否在思考块内的状态标志
    
    /**
     * 从统一请求对象创建聊天上下文
     */
    public static ChatContext fromRequest(UnifiedChatRequest request, Long userId) {
        return ChatContext.builder()
                .prompt(request.getPrompt())
                .modelName(request.getModel())
                .imageFile(request.getImage())
                .conversationId(request.getConversationId())
                .title(request.getTitle())
                .knowledgeNodeId(request.getKnowledgeNodeId())
                .isKnowledgeChat(request.getKnowledgeNodeId() != null && !request.getKnowledgeNodeId().isEmpty())
                .enableThinking(request.getEnableThinking())
                .enableEnhancedRetrieval(request.getEnableEnhancedRetrieval())
                .enableToolCalling(request.getEnableToolCalling())
                .userId(userId)
                .mainContentBuilder(new StringBuilder())
                .thinkingContentBuilder(new StringBuilder())
                .isInsideThinkingBlock(new AtomicBoolean(false))
                .build();
    }
    
    /**
     * 获取思考块状态
     */
    public AtomicBoolean getIsInsideThinkingBlock() {
        return isInsideThinkingBlock;
    }
}
