package com.zibbava.edgemind.cortex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zibbava.edgemind.cortex.entity.RemoteModelConfig;
import com.zibbava.edgemind.cortex.enums.RemoteModelInfo;
import com.zibbava.edgemind.cortex.mapper.RemoteModelConfigMapper;
import com.zibbava.edgemind.cortex.service.RemoteModelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 远程模型服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RemoteModelServiceImpl implements RemoteModelService {

    private final RemoteModelConfigMapper remoteModelConfigMapper;
    private final RestTemplate restTemplate;

    @Override
    public List<Map<String, Object>> getAllRemoteModels() {
        log.info("获取所有支持的远程模型信息");
        
        List<Map<String, Object>> models = new ArrayList<>();
        
        for (RemoteModelInfo modelInfo : RemoteModelInfo.values()) {
            Map<String, Object> modelMap = new HashMap<>();
            modelMap.put("id", modelInfo.getModelId());
            modelMap.put("name", modelInfo.getName());
            modelMap.put("description", modelInfo.getDescription());
            modelMap.put("provider", modelInfo.getProvider());
            modelMap.put("type", modelInfo.getType());
            modelMap.put("supportsThinking", modelInfo.isSupportsThinking());
            modelMap.put("supportsMultimodal", modelInfo.isSupportsMultimodal());
            modelMap.put("pricing", modelInfo.getPricing());
            modelMap.put("isRemote", true);
            modelMap.put("isLocal", false);
            
            models.add(modelMap);
        }
        
        log.info("返回{}个远程模型", models.size());
        return models;
    }

    @Override
    public List<Map<String, Object>> getRemoteModelsByProvider(String provider) {
        log.info("获取提供商{}的远程模型信息", provider);
        
        if (provider == null || provider.trim().isEmpty()) {
            return getAllRemoteModels();
        }
        
        RemoteModelInfo[] models = RemoteModelInfo.getModelsByProvider(provider);
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (RemoteModelInfo modelInfo : models) {
            Map<String, Object> modelMap = new HashMap<>();
            modelMap.put("id", modelInfo.getModelId());
            modelMap.put("name", modelInfo.getName());
            modelMap.put("description", modelInfo.getDescription());
            modelMap.put("provider", modelInfo.getProvider());
            modelMap.put("type", modelInfo.getType());
            modelMap.put("supportsThinking", modelInfo.isSupportsThinking());
            modelMap.put("supportsMultimodal", modelInfo.isSupportsMultimodal());
            modelMap.put("pricing", modelInfo.getPricing());
            modelMap.put("isRemote", true);
            modelMap.put("isLocal", false);
            
            result.add(modelMap);
        }
        
        log.info("返回提供商{}的{}个远程模型", provider, result.size());
        return result;
    }

    @Override
    public List<RemoteModelConfig> getAllRemoteModelConfigs() {
        log.info("获取所有远程模型配置");

        LambdaQueryWrapper<RemoteModelConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(RemoteModelConfig::getCreateTime);

        List<RemoteModelConfig> configs = remoteModelConfigMapper.selectList(queryWrapper);
        log.info("共有{}个远程模型配置", configs.size());

        return configs;
    }

    @Override
    public List<RemoteModelConfig> getEnabledRemoteModelConfigs() {
        log.info("获取所有启用的远程模型配置");

        List<RemoteModelConfig> configs = remoteModelConfigMapper.selectAllEnabled();
        log.info("共有{}个启用的远程模型配置", configs.size());

        return configs;
    }

    @Override
    public RemoteModelConfig saveOrUpdateRemoteModelConfig(RemoteModelConfig config) {
        log.info("保存或更新远程模型配置: 模型={}", config.getModelId());

        // 检查是否已存在相同配置
        RemoteModelConfig existingConfig = remoteModelConfigMapper.selectByModelId(config.getModelId());

        if (existingConfig != null) {
            // 更新现有配置
            config.setId(existingConfig.getId());
            config.setUpdateTime(LocalDateTime.now());
            remoteModelConfigMapper.updateById(config);
            log.info("更新远程模型配置成功: ID={}", config.getId());
        } else {
            // 创建新配置
            config.setCreateTime(LocalDateTime.now());
            config.setUpdateTime(LocalDateTime.now());
            config.setUsageCount(0L);
            remoteModelConfigMapper.insert(config);
            log.info("创建远程模型配置成功: ID={}", config.getId());
        }

        return config;
    }

    @Override
    public boolean deleteRemoteModelConfig(Long configId) {
        log.info("删除远程模型配置: 配置ID={}", configId);

        int result = remoteModelConfigMapper.deleteById(configId);
        boolean success = result > 0;

        log.info("删除远程模型配置{}: 配置ID={}", success ? "成功" : "失败", configId);
        return success;
    }

    @Override
    public boolean toggleRemoteModelConfig(Long configId, boolean enabled) {
        log.info("{}远程模型配置: 配置ID={}", enabled ? "启用" : "禁用", configId);

        RemoteModelConfig config = new RemoteModelConfig();
        config.setId(configId);
        config.setEnabled(enabled);
        config.setUpdateTime(LocalDateTime.now());

        int result = remoteModelConfigMapper.updateById(config);
        boolean success = result > 0;

        log.info("{}远程模型配置{}: 配置ID={}",
                enabled ? "启用" : "禁用", success ? "成功" : "失败", configId);
        return success;
    }

    @Override
    public Map<String, Object> testRemoteModelConnection(String modelId, String apiKey) {
        log.info("测试远程模型连接: 模型={}", modelId);

        Map<String, Object> result = new HashMap<>();

        try {
            // 根据模型ID获取模型信息
            RemoteModelInfo modelInfo = RemoteModelInfo.findByModelId(modelId);
            if (modelInfo == null) {
                result.put("success", false);
                result.put("message", "不支持的模型: " + modelId);
                return result;
            }

            // 根据提供商进行不同的连接测试
            switch (modelInfo.getProvider().toLowerCase()) {
                case "deepseek":
                    result = testDeepSeekConnection(modelId, apiKey);
                    break;
                default:
                    result.put("success", false);
                    result.put("message", "不支持的提供商: " + modelInfo.getProvider());
                    break;
            }
        } catch (Exception e) {
            log.error("测试远程模型连接失败", e);
            result.put("success", false);
            result.put("message", "连接测试失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public RemoteModelConfig getRemoteModelConfig(String modelId) {
        log.info("获取远程模型配置: 模型={}", modelId);

        return remoteModelConfigMapper.selectByModelId(modelId);
    }

    @Override
    public void updateModelUsageStats(Long configId) {
        log.debug("更新模型使用统计: 配置ID={}", configId);
        
        remoteModelConfigMapper.updateLastUsedTime(configId);
    }

    @Override
    public List<String> getAllProviders() {
        log.info("获取所有支持的提供商");
        
        String[] providers = RemoteModelInfo.getAllProviders();
        List<String> result = Arrays.asList(providers);
        
        log.info("返回{}个提供商: {}", result.size(), result);
        return result;
    }

    /**
     * 测试DeepSeek连接
     */
    private Map<String, Object> testDeepSeekConnection(String modelId, String apiKey) {
        Map<String, Object> result = new HashMap<>();

        // 验证必要参数
        if (apiKey == null || apiKey.trim().isEmpty()) {
            result.put("success", false);
            result.put("message", "API密钥不能为空");
            return result;
        }

        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + apiKey);
            headers.set("Content-Type", "application/json");

            // 构建测试请求体 - 发送一个简单的chat completion请求
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", modelId);
            requestBody.put("messages", List.of(
                Map.of("role", "user", "content", "Hello")
            ));
            requestBody.put("max_tokens", 10);
            requestBody.put("temperature", 0.1);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 发送请求到DeepSeek API
            String apiUrl = "https://api.deepseek.com/v1/chat/completions";
            log.info("测试DeepSeek API连接: {}", apiUrl);

            ResponseEntity<Map> response = restTemplate.exchange(
                apiUrl,
                HttpMethod.POST,
                entity,
                Map.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> responseBody = response.getBody();
                if (responseBody != null && responseBody.containsKey("choices")) {
                    result.put("success", true);
                    result.put("message", "连接测试成功");
                    result.put("modelId", modelId);
                    result.put("response", "API响应正常");
                } else {
                    result.put("success", false);
                    result.put("message", "API响应格式异常");
                }
            } else {
                result.put("success", false);
                result.put("message", "API请求失败，状态码: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("DeepSeek连接测试失败", e);
            result.put("success", false);
            result.put("message", "连接测试失败: " + e.getMessage());
        }

        return result;
    }
}
