package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.SystemSettings;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 系统设置 Mapper 接口
 */
@Mapper
public interface SystemSettingsMapper extends BaseMapper<SystemSettings> {

    /**
     * 根据键名获取设置值
     * @param key 键名
     * @return 设置值
     */
    @Select("SELECT setting_value FROM sys_settings WHERE setting_key = #{key}")
    String getSettingValueByKey(@Param("key") String key);

    /**
     * 更新设置值
     * @param key 键名
     * @param value 设置值
     * @return 影响的行数
     */
    @Update("UPDATE sys_settings SET setting_value = #{value}, update_time = NOW() WHERE setting_key = #{key}")
    int updateSettingValueByKey(@Param("key") String key, @Param("value") String value);

    /**
     * 根据键名获取设置对象
     * @param key 键名
     * @return 设置对象
     */
    @Select("SELECT * FROM sys_settings WHERE setting_key = #{key}")
    SystemSettings getSettingByKey(@Param("key") String key);

    /**
     * 根据键名前缀获取设置列表
     * @param keyPrefix 键名前缀
     * @return 设置列表
     */
    @Select("SELECT * FROM sys_settings WHERE setting_key LIKE CONCAT(#{keyPrefix}, '%')")
    List<SystemSettings> getSettingsByKeyPrefix(@Param("keyPrefix") String keyPrefix);

    /**
     * 检查设置是否存在
     * @param key 键名
     * @return 存在返回 true，否则返回 false
     */
    @Select("SELECT COUNT(*) FROM sys_settings WHERE setting_key = #{key}")
    boolean existsByKey(@Param("key") String key);

    /**
     * 插入或更新设置
     * @param key 键名
     * @param value 设置值
     * @param description 描述
     * @return 影响的行数
     */
    @Insert("INSERT INTO sys_settings(setting_key, setting_value, description) VALUES(#{key}, #{value}, #{description}) " +
           "ON DUPLICATE KEY UPDATE setting_value = #{value}, description = #{description}, update_time = NOW()")
    int insertOrUpdateSetting(@Param("key") String key, @Param("value") String value, @Param("description") String description);
}
