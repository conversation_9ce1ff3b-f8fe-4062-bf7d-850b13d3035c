package com.zibbava.edgemind.cortex.service;

import com.zibbava.edgemind.cortex.entity.RemoteModelConfig;
import com.zibbava.edgemind.cortex.enums.RemoteModelInfo;

import java.util.List;
import java.util.Map;

/**
 * 远程模型服务接口
 */
public interface RemoteModelService {

    /**
     * 获取所有支持的远程模型信息
     * @return 远程模型信息列表
     */
    List<Map<String, Object>> getAllRemoteModels();

    /**
     * 根据提供商获取远程模型信息
     * @param provider 提供商名称
     * @return 远程模型信息列表
     */
    List<Map<String, Object>> getRemoteModelsByProvider(String provider);

    /**
     * 获取所有远程模型配置
     * @return 远程模型配置列表
     */
    List<RemoteModelConfig> getAllRemoteModelConfigs();

    /**
     * 获取所有启用的远程模型配置
     * @return 启用的远程模型配置列表
     */
    List<RemoteModelConfig> getEnabledRemoteModelConfigs();

    /**
     * 保存或更新远程模型配置
     * @param config 远程模型配置
     * @return 保存后的配置
     */
    RemoteModelConfig saveOrUpdateRemoteModelConfig(RemoteModelConfig config);

    /**
     * 删除远程模型配置
     * @param configId 配置ID
     * @return 是否删除成功
     */
    boolean deleteRemoteModelConfig(Long configId);

    /**
     * 启用/禁用远程模型配置
     * @param configId 配置ID
     * @param enabled 是否启用
     * @return 是否操作成功
     */
    boolean toggleRemoteModelConfig(Long configId, boolean enabled);

    /**
     * 测试远程模型连接
     * @param modelId 模型ID
     * @param apiKey API密钥
     * @return 测试结果
     */
    Map<String, Object> testRemoteModelConnection(String modelId, String apiKey);

    /**
     * 获取指定的远程模型配置
     * @param modelId 模型ID
     * @return 远程模型配置
     */
    RemoteModelConfig getRemoteModelConfig(String modelId);

    /**
     * 更新模型使用统计
     * @param configId 配置ID
     */
    void updateModelUsageStats(Long configId);

    /**
     * 获取所有支持的提供商
     * @return 提供商列表
     */
    List<String> getAllProviders();
}
