package com.zibbava.edgemind.cortex.enums;

import lombok.Getter;

/**
 * 远程模型信息枚举类
 * 定义了各种远程AI模型的基本信息和支持的功能
 */
@Getter
public enum RemoteModelInfo {
    // DeepSeek系列 - 远程API模型
    DEEPSEEK_CHAT("deepseek-chat", "DeepSeek Chat", "DeepSeek-V3通用对话模型，擅长推理和代码生成，适合日常对话和编程任务。",
                  "deepseek", "llm", true, false, ""),

    DEEPSEEK_REASONER("deepseek-reasoner", "DeepSeek Reasoner", "DeepSeek-R1推理模型，专门优化复杂推理能力，适合需要深度思考的任务。",
                      "deepseek", "llm", true, false, ""),

    // 未来可扩展其他远程模型提供商
    // OPENAI_GPT4("gpt-4", "GPT-4", "OpenAI GPT-4模型", "openai", "llm", false, true, "价格待定"),
    // CLAUDE_3("claude-3-sonnet", "Claude 3 Sonnet", "Anthropic Claude 3 Sonnet", "anthropic", "llm", false, true, "价格待定")
    ;

    private final String modelId;           // 模型ID
    private final String name;              // 模型名称
    private final String description;       // 模型描述
    private final String provider;          // 提供商（deepseek, openai, anthropic等）
    private final String type;              // 模型类型（llm, embedding等）
    private final boolean supportsThinking; // 是否支持推理功能
    private final boolean supportsMultimodal; // 是否支持多模态
    private final String pricing;           // 价格信息

    RemoteModelInfo(String modelId, String name, String description, String provider, String type,
                   boolean supportsThinking, boolean supportsMultimodal, String pricing) {
        this.modelId = modelId;
        this.name = name;
        this.description = description;
        this.provider = provider;
        this.type = type;
        this.supportsThinking = supportsThinking;
        this.supportsMultimodal = supportsMultimodal;
        this.pricing = pricing;
    }

    /**
     * 根据模型ID查找远程模型信息
     * @param modelId 模型ID
     * @return 远程模型信息，如果未找到返回null
     */
    public static RemoteModelInfo findByModelId(String modelId) {
        if (modelId == null) {
            return null;
        }
        
        for (RemoteModelInfo model : values()) {
            if (model.getModelId().equalsIgnoreCase(modelId)) {
                return model;
            }
        }
        return null;
    }

    /**
     * 根据提供商获取所有模型
     * @param provider 提供商名称
     * @return 该提供商的所有模型
     */
    public static RemoteModelInfo[] getModelsByProvider(String provider) {
        if (provider == null) {
            return new RemoteModelInfo[0];
        }
        
        return java.util.Arrays.stream(values())
                .filter(model -> provider.equalsIgnoreCase(model.getProvider()))
                .toArray(RemoteModelInfo[]::new);
    }

    /**
     * 获取所有支持的提供商
     * @return 提供商列表
     */
    public static String[] getAllProviders() {
        return java.util.Arrays.stream(values())
                .map(RemoteModelInfo::getProvider)
                .distinct()
                .toArray(String[]::new);
    }

    /**
     * 检查指定模型是否支持推理功能
     * @param modelId 模型ID
     * @return 是否支持推理功能
     */
    public static boolean supportsThinking(String modelId) {
        RemoteModelInfo model = findByModelId(modelId);
        return model != null && model.isSupportsThinking();
    }

    /**
     * 检查指定模型是否支持多模态功能
     * @param modelId 模型ID
     * @return 是否支持多模态功能
     */
    public static boolean supportsMultimodal(String modelId) {
        RemoteModelInfo model = findByModelId(modelId);
        return model != null && model.isSupportsMultimodal();
    }
}
