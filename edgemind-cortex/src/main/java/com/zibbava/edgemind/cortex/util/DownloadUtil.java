package com.zibbava.edgemind.cortex.util;

import javax.swing.*;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * 下载工具类
 * 提供带进度显示的文件下载功能
 */
public class DownloadUtil {

    /**
     * 下载进度回调接口
     */
    public interface ProgressCallback {
        void onProgress(long downloaded, long total, int percentage);
        void onComplete(File downloadedFile);
        void onError(Exception error);
    }

    /**
     * 同步下载文件
     */
    private static File downloadFileSync(String downloadUrl, File targetFile, ProgressCallback callback) throws IOException {
        URL url = new URL(downloadUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 设置更完整的请求头，模拟真实浏览器
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(30000); // 30秒连接超时
        connection.setReadTimeout(120000);   // 120秒读取超时（大文件需要更长时间）
        connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        connection.setRequestProperty("Accept", "*/*");
        connection.setRequestProperty("Accept-Language", "en-US,en;q=0.9");
        connection.setRequestProperty("Accept-Encoding", "identity"); // 不使用压缩，便于进度计算
        connection.setRequestProperty("Connection", "keep-alive");

        // 允许重定向
        connection.setInstanceFollowRedirects(true);
        
        // 检查响应码
        int responseCode = connection.getResponseCode();
        if (responseCode != HttpURLConnection.HTTP_OK) {
            throw new IOException("下载失败，HTTP响应码: " + responseCode);
        }
        
        long fileSize = connection.getContentLengthLong();
        
        // 确保目标目录存在
        File parentDir = targetFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }
        
        try (InputStream inputStream = connection.getInputStream();
             FileOutputStream outputStream = new FileOutputStream(targetFile);
             BufferedInputStream bufferedInput = new BufferedInputStream(inputStream);
             BufferedOutputStream bufferedOutput = new BufferedOutputStream(outputStream)) {
            
            byte[] buffer = new byte[8192];
            long downloaded = 0;
            int bytesRead;
            
            while ((bytesRead = bufferedInput.read(buffer)) != -1) {
                bufferedOutput.write(buffer, 0, bytesRead);
                downloaded += bytesRead;
                
                // 更新进度
                if (callback != null && fileSize > 0) {
                    final long finalDownloaded = downloaded;
                    final long finalFileSize = fileSize;
                    int percentage = (int) ((downloaded * 100) / fileSize);
                    final int finalPercentage = percentage;
                    SwingUtilities.invokeLater(() ->
                        callback.onProgress(finalDownloaded, finalFileSize, finalPercentage));
                }
            }
            
            bufferedOutput.flush();
        }
        
        // 验证下载完整性
        if (fileSize > 0 && targetFile.length() != fileSize) {
            targetFile.delete();
            throw new IOException("下载文件大小不匹配，预期: " + fileSize + "，实际: " + targetFile.length());
        }
        
        if (callback != null) {
            SwingUtilities.invokeLater(() -> callback.onComplete(targetFile));
        }
        
        return targetFile;
    }

    /**
     * 简单的同步下载方法（带进度回调）
     *
     * @param downloadUrl 下载URL
     * @param targetFile 目标文件
     * @param progressCallback 进度回调函数 (progress, speed) -> void
     * @return boolean 下载是否成功
     */
    public static boolean downloadFile(String downloadUrl, File targetFile,
                                     java.util.function.BiConsumer<Integer, Double> progressCallback) {
        try {
            final long[] lastUpdateTime = {System.currentTimeMillis()};
            final long[] lastDownloaded = {0};

            ProgressCallback callback = new ProgressCallback() {
                @Override
                public void onProgress(long downloaded, long total, int percentage) {
                    if (progressCallback != null) {
                        // 计算下载速度 (MB/s)
                        long currentTime = System.currentTimeMillis();
                        long timeDiff = currentTime - lastUpdateTime[0];

                        if (timeDiff >= 1000) { // 每秒更新一次速度
                            long downloadedDiff = downloaded - lastDownloaded[0];
                            double speed = (downloadedDiff / 1024.0 / 1024.0) / (timeDiff / 1000.0); // MB/s
                            progressCallback.accept(percentage, speed);

                            lastUpdateTime[0] = currentTime;
                            lastDownloaded[0] = downloaded;
                        } else {
                            // 第一次调用或时间间隔太短，使用0速度
                            progressCallback.accept(percentage, 0.0);
                        }
                    }
                }

                @Override
                public void onComplete(File downloadedFile) {
                    if (progressCallback != null) {
                        progressCallback.accept(100, 0.0);
                    }
                }

                @Override
                public void onError(Exception error) {
                    // 错误处理在外层catch中处理
                }
            };

            downloadFileSync(downloadUrl, targetFile, callback);
            return true;

        } catch (Exception e) {
            System.err.println("下载文件失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 带重试机制的下载
     *
     * @param downloadUrl 下载URL
     * @param targetFile 目标文件
     * @param maxRetries 最大重试次数
     * @param callback 进度回调
     * @return CompletableFuture<File> 异步下载任务
     */
    public static CompletableFuture<File> downloadWithRetry(String downloadUrl, File targetFile,
                                                           int maxRetries, ProgressCallback callback) {
        return CompletableFuture.supplyAsync(() -> {
            Exception lastException = null;
            
            for (int attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    System.out.println("开始下载 (尝试 " + attempt + "/" + maxRetries + "): " + downloadUrl);
                    return downloadFileSync(downloadUrl, targetFile, callback);
                } catch (Exception e) {
                    lastException = e;
                    System.err.println("下载失败 (尝试 " + attempt + "/" + maxRetries + "): " + e.getMessage());
                    
                    // 如果不是最后一次尝试，等待一段时间后重试
                    if (attempt < maxRetries) {
                        try {
                            Thread.sleep(2000 * attempt); // 递增等待时间
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                        
                        // 删除部分下载的文件
                        if (targetFile.exists()) {
                            targetFile.delete();
                        }
                    }
                }
            }
            
            if (callback != null) {
                callback.onError(lastException);
            }
            throw new RuntimeException("下载失败，已重试 " + maxRetries + " 次", lastException);
        });
    }

    /**
     * 获取文件大小（不下载文件）
     * 简化版本：如果无法获取则返回-1
     */
    public static long getFileSize(String downloadUrl) throws IOException {
        try {
            URL url = new URL(downloadUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // 设置基本的请求头
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            connection.setInstanceFollowRedirects(true);

            int responseCode = connection.getResponseCode();

            if (responseCode == HttpURLConnection.HTTP_OK) {
                long contentLength = connection.getContentLengthLong();
                connection.disconnect();

                if (contentLength > 0) {
                    return contentLength;
                } else {
                    System.out.println("服务器未提供Content-Length信息: " + downloadUrl);
                    return -1; // 表示无法获取文件大小
                }
            } else {
                connection.disconnect();
                System.out.println("无法获取文件大小，HTTP响应码: " + responseCode + " URL: " + downloadUrl);
                return -1; // 表示无法获取文件大小
            }

        } catch (Exception e) {
            System.out.println("获取文件大小时发生异常: " + e.getMessage() + " URL: " + downloadUrl);
            return -1; // 表示无法获取文件大小
        }
    }

    /**
     * 检查URL是否可访问
     * 简化版本：只检查域名连通性，不检查具体文件
     */
    public static boolean isUrlAccessible(String url) {
        try {

            // 对于其他URL，进行简单的连通性检查
            URL urlObj = new URL(url);
            String host = urlObj.getHost();

            // 简单的ping检查
            try {
                java.net.InetAddress.getByName(host);
                System.out.println("域名解析成功: " + host);
                return true;
            } catch (Exception e) {
                System.out.println("域名解析失败: " + host + " - " + e.getMessage());
                return false;
            }

        } catch (Exception e) {
            System.out.println("URL解析失败: " + url + " - " + e.getMessage());
            return false;
        }
    }

    /**
     * 格式化文件大小显示
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    public static void main(String[] args) throws IOException {
        System.out.println(isUrlAccessible("https://desktop.docker.com/win/main/amd64/Docker Desktop Installer.exe?utm_source=docker&utm_medium=webreferral&utm_campaign=dd-smartbutton&utm_location=module"));
        System.out.println(getFileSize("https://desktop.docker.com/win/main/amd64/Docker Desktop Installer.exe?utm_source=docker&utm_medium=webreferral&utm_campaign=dd-smartbutton&utm_location=module"));
    }
}
