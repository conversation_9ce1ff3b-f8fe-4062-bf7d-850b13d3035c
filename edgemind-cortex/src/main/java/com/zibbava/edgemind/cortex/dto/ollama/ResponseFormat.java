package com.zibbava.edgemind.cortex.dto.ollama;

import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 格式定义类
 * 支持JSON字符串和结构化Schema
 */
@Data
@Builder
public class ResponseFormat {
    private String type; // "json" 或 "schema"
    private JsonSchema schema; // 当type为"schema"时使用

    public static ResponseFormat json() {
        return ResponseFormat.builder()
                .type("json")
                .build();
    }

    public static ResponseFormat schema(JsonSchema schema) {
        return ResponseFormat.builder()
                .type("schema")
                .schema(schema)
                .build();
    }

    public Object toRequestFormat() {
        if ("json".equals(type)) {
            return "json";
        } else if ("schema".equals(type) && schema != null) {
            return convertSchemaToMap(schema);
        }
        return null;
    }

    private Map<String, Object> convertSchemaToMap(JsonSchema schema) {
        Map<String, Object> map = new HashMap<>();
        if (schema.getType() != null) map.put("type", schema.getType());
        if (schema.getDescription() != null) map.put("description", schema.getDescription());
        if (schema.getRequired() != null) map.put("required", schema.getRequired());
        
        if (schema.getProperties() != null) {
            Map<String, Object> propertiesMap = new HashMap<>();
            for (Map.Entry<String, JsonSchemaProperty> entry : schema.getProperties().entrySet()) {
                propertiesMap.put(entry.getKey(), convertPropertyToMap(entry.getValue()));
            }
            map.put("properties", propertiesMap);
        }
        
        return map;
    }

    private Map<String, Object> convertPropertyToMap(JsonSchemaProperty property) {
        Map<String, Object> map = new HashMap<>();
        if (property.getType() != null) map.put("type", property.getType());
        if (property.getDescription() != null) map.put("description", property.getDescription());
        if (property.getEnumValues() != null) map.put("enum", property.getEnumValues());
        
        if (property.getItems() != null) {
            map.put("items", convertSchemaToMap(property.getItems()));
        }
        
        if (property.getProperties() != null) {
            Map<String, Object> propertiesMap = new HashMap<>();
            for (Map.Entry<String, JsonSchemaProperty> entry : property.getProperties().entrySet()) {
                propertiesMap.put(entry.getKey(), convertPropertyToMap(entry.getValue()));
            }
            map.put("properties", propertiesMap);
        }
        
        return map;
    }
} 