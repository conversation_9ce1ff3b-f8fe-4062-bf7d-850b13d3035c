package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.RemoteModelConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 远程模型配置Mapper接口
 */
@Mapper
public interface RemoteModelConfigMapper extends BaseMapper<RemoteModelConfig> {

    /**
     * 获取所有启用的远程模型配置
     * @return 启用的远程模型配置列表
     */
    List<RemoteModelConfig> selectAllEnabled();

    /**
     * 根据模型ID获取配置
     * @param modelId 模型ID
     * @return 配置信息
     */
    RemoteModelConfig selectByModelId(@Param("modelId") String modelId);

    /**
     * 更新最后使用时间和使用次数
     * @param id 配置ID
     */
    void updateLastUsedTime(@Param("id") Long id);
}
