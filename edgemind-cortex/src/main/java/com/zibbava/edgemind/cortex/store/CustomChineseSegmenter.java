package com.zibbava.edgemind.cortex.store;

import com.huaban.analysis.jieba.JiebaSegmenter;
import com.huaban.analysis.jieba.SegToken;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 自定义中文分词器
 * 使用jieba分词器避免Milvus内置分词器的中文编码问题
 * 
 * 核心特性：
 * - 使用jieba搜索引擎模式分词，提高召回率
 * - 智能过滤停用词和无效词
 * - 支持中英文混合文本处理
 * - 提供降级方案，确保分词稳定性
 */
@Slf4j
public class CustomChineseSegmenter {
    
    // 全局jieba分词器实例，避免重复初始化
    private static final JiebaSegmenter GLOBAL_SEGMENTER = new JiebaSegmenter();
    
    // 中文停用词表 - 可根据业务需求扩展
    private static final Set<String> STOP_WORDS = new HashSet<>(Arrays.asList(
        // 常用停用词
        "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "个", "也", "上", "来", "到", "时", "大", "地", "为", "子",
        "中", "你", "说", "生", "国", "年", "着", "就", "那", "和", "要", "她", "出", "也", "得", "里", "后", "自", "以", "会", "家", "可",
        "下", "而", "过", "天", "去", "能", "对", "小", "多", "然", "于", "心", "学", "么", "之", "都", "好", "看", "起", "发", "当", "没",
        "成", "只", "如", "事", "把", "还", "用", "第", "样", "道", "想", "作", "种", "开", "美", "总", "从", "无", "情", "己", "面", "最",
        "女", "但", "现", "前", "些", "所", "同", "日", "手", "又", "行", "意", "动", "方", "期", "它", "头", "经", "长", "儿", "回", "位",
        "分", "爱", "老", "因", "很", "给", "名", "法", "间", "斯", "知", "世", "什", "两", "次", "使", "身", "者", "被", "高", "已", "亲",
        "其", "进", "此", "话", "常", "与", "活", "正", "感", "见", "明", "问", "力", "理", "尔", "点", "文", "几", "定", "本", "公", "特",
        "做", "外", "孩", "相", "西", "果", "走", "将", "月", "十", "实", "向", "声", "车", "全", "信", "重", "三", "机", "工", "物", "气",
        "每", "并", "别", "真", "打", "太", "新", "比", "才", "便", "夫", "再", "书", "部", "水", "像", "眼", "等", "体", "却", "加", "电",
        "主", "界", "门", "利", "海", "受", "听", "表", "德", "少", "克", "代", "员", "许", "稜", "先", "口", "由", "死", "安", "写", "性",
        "马", "光", "白", "或", "住", "难", "望", "教", "命", "花", "结", "乐", "色", "更", "拉", "东", "神", "记", "处", "让", "母", "父",
        "应", "直", "字", "场", "平", "报", "友", "关", "放", "至", "张", "认", "接", "告", "入", "笑", "内", "英", "军", "候", "民", "岁",
        "往", "何", "度", "山", "觉", "路", "带", "万", "男", "边", "风", "解", "叫", "任", "金", "快", "原", "吃", "妈", "变", "通", "师",
        "立", "象", "数", "四", "失", "满", "战", "远", "格", "士", "音", "轻", "目", "条", "呢", "病", "始", "达", "深", "完", "今", "提",
        "求", "清", "王", "化", "空", "业", "思", "切", "怎", "非", "找", "片", "罗", "钱", "紶", "吗", "语", "元", "喜", "曾", "离", "飞",
        "科", "言", "干", "流", "欢", "约", "各", "即", "指", "合", "反", "题", "必", "该", "论", "交", "终", "林", "请", "医", "晚", "制",
        "球", "决", "窢", "传", "画", "保", "读", "运", "及", "则", "房", "早", "院", "量", "苦", "火", "布", "品", "近", "坐", "产", "答",
        "星", "精", "视", "五", "连", "司", "巴", "奇", "管", "类", "未", "朋", "且", "婚", "台", "夜", "青", "北", "队", "久", "乎", "越",
        "观", "落", "尽", "形", "影", "红", "爸", "百", "令", "周", "吧", "识", "步", "希", "亚", "术", "留", "市", "半", "热", "送", "兴",
        "造", "谈", "容", "极", "随", "演", "收", "首", "根", "讲", "整", "式", "取", "照", "办", "强", "石", "古", "华", "諣", "拿", "计",
        "您", "装", "似", "足", "双", "妻", "尼", "转", "诉", "米", "称", "丽", "客", "南", "领", "节", "衣", "站", "黑", "刻", "统", "断",
        "福", "城", "故", "历", "惊", "脸", "选", "包", "紧", "争", "另", "建", "维", "绝", "树", "系", "伤", "示", "愿", "持", "千", "史",
        "谁", "准", "联", "妇", "纪", "基", "买", "志", "静", "阿", "诗", "独", "复", "痛", "消", "社", "算", "义", "竟", "确", "酒", "需",
        "单", "治", "卡", "幸", "兰", "念", "举", "仅", "钟", "怕", "共", "毛", "句", "息", "功", "官", "待", "究", "跟", "穿", "室", "易",
        "游", "程", "号", "居", "考", "突", "皮", "哪", "费", "倒", "价", "图", "具", "刚", "脑", "永", "歌", "响", "商", "礼", "细", "专",
        "黄", "块", "脚", "味", "灵", "改", "据", "般", "破", "引", "食", "仍", "存", "众", "注", "笔", "甚", "某", "沉", "血", "备", "习",
        "校", "默", "务", "土", "微", "娘", "须", "试", "怀", "料", "调", "广", "蜖", "苏", "显", "赛", "查", "密", "议", "底", "列", "富",
        "梦", "错", "座", "参", "八", "除", "跑", "亮", "假", "印", "设", "线", "温", "虽", "掉", "京", "初", "养", "香", "停", "际", "致",
        "阳", "纸", "李", "纳", "验", "助", "激", "够", "严", "证", "帝", "饭", "忘", "趣", "支", "春", "集", "丈", "木", "研", "班", "普",
        "导", "顿", "睡", "展", "跳", "获", "艺", "六", "波", "察", "群", "皇", "段", "急", "庭", "创", "区", "奥", "器", "谢", "弟", "店",
        "否", "害", "草", "排", "背", "止", "组", "州", "朝", "封", "睛", "板", "角", "况", "曲", "馆", "育", "忙", "质", "河", "续", "哥",
        "呼", "若", "推", "顺", "剧", "江", "哈", "黄", "共", "圆", "御", "妹", "应", "细", "穿", "完", "消", "息", "标", "联", "指", "德",
        "增", "争", "土", "阳", "吸", "呢", "味", "最", "足", "团", "购", "季", "奇", "希", "伤", "操", "早", "班", "麻", "雨", "环", "安",
        "常", "育", "届", "识", "拜", "样", "脱", "适", "武", "密", "宣", "环", "本", "性", "帝", "杂", "采", "天", "感", "步", "宁", "科",
        "议", "梦", "温", "测", "聊", "建", "始", "额", "首", "哦", "股", "势", "训", "爱", "印", "怕", "包", "慢", "宿", "影", "春", "牛",
        "约", "炎", "南", "征", "败", "稍", "效", "庆", "网", "帮", "岁", "火", "权", "病", "制", "续", "逐", "趋", "软", "渐", "苦", "宝",
        "族", "担", "拿", "确", "究", "太", "获", "改", "促", "围", "定", "石", "省", "旁", "路", "范", "卖", "号", "举", "片", "土", "势",
        "减", "阿", "爸", "永", "治", "秒", "编", "角", "降", "包", "题", "注", "平", "试", "宫", "宁", "林", "换", "档", "秘", "承", "苦",
        "刘", "啊", "底", "针", "怎", "买", "方", "苗", "社", "交", "忙", "班", "秘", "宣", "缺", "忘", "郎", "差", "重", "假", "王", "代",
        "湾", "丝", "牌", "农", "流", "势", "众", "爱", "河", "笑", "承", "绝", "纪", "星", "伴", "革", "损", "健", "络", "微", "型", "康",
        "轨", "族", "额", "论", "沿", "避", "际", "双", "华", "册", "户", "彩", "哭", "维", "管", "纷", "乱", "振", "守", "挑", "扩", "封",
        "毁", "纯", "宿", "鉴", "稳", "紧", "善", "妙", "森", "阅", "竞", "立", "拢", "轻", "诸", "威", "液", "倾", "油", "旧", "恶", "补",
        "杰", "酷", "威", "伸", "敌", "坚", "暗", "洗", "湖", "羊", "虽", "厉", "怖", "钢", "缘", "敢", "虚", "弃", "周", "脱", "奶", "罪",
        "笔", "艺", "抓", "鱼", "朝", "碎", "邻", "握", "鸡", "诗", "喝", "购", "羽", "献", "柔", "摆", "忆", "禁", "阿", "胜", "耳", "惊",
        "挥", "怒", "含", "蛋", "岗", "斗", "绪", "冬", "净", "毕", "礼", "滑", "编", "典", "袋", "莫", "困", "酸", "祖", "潜", "春", "助",
        "升", "王", "编", "典", "袋", "莫", "困", "酸", "祖", "潜", "春", "助", "升", "王", "旅", "舞", "爪", "乾", "杯", "桌", "述", "忆",
        "吸", "脉", "施", "仁", "庙", "尤", "艰", "萨", "惯", "减", "罗", "趋", "慢", "银", "皮", "兵", "博", "巨", "炮", "羊", "圈", "铁",
        "怎", "忽", "灰", "猜", "访", "评", "课", "翻", "微", "街", "亲", "乘", "伙", "判", "兄", "析", "佛", "农", "纳", "腰", "挤", "弹",
        "随", "违", "夏", "吓", "哈", "苦", "笑", "敏", "维", "芳", "乐", "汗", "漫", "狗", "熊", "猫", "鸟", "鱼", "虫", "草", "花", "树"
    ));
    
    // 英文停用词表
    private static final Set<String> ENGLISH_STOP_WORDS = new HashSet<>(Arrays.asList(
        "a", "an", "and", "are", "as", "at", "be", "by", "for", "from", "has", "he", "in", "is", "it", "its", "of", "on", 
        "that", "the", "to", "was", "will", "with", "the", "this", "but", "they", "have", "had", "what", "said", "each", 
        "which", "she", "do", "how", "their", "if", "up", "out", "many", "then", "them", "these", "so", "some", "her", 
        "would", "make", "like", "into", "him", "time", "two", "more", "go", "no", "way", "could", "my", "than", "first", 
        "been", "call", "who", "oil", "sit", "now", "find", "down", "day", "did", "get", "come", "made", "may", "part"
    ));
    
    // 无效字符模式
    private static final Pattern INVALID_PATTERN = Pattern.compile("[\\d\\p{Punct}\\s]+");
    private static final Pattern SINGLE_CHAR_PATTERN = Pattern.compile("^[\\p{Punct}\\s]$");
    
    /**
     * 对文本进行分词处理
     * 
     * @param text 待分词的文本
     * @return 分词结果列表
     */
    public static List<String> segment(String text) {
        if (text == null || text.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        try {
            return jiebaSegment(text);
        } catch (Exception e) {
            log.warn("jieba分词失败，使用简单分词降级方案: {}", e.getMessage());
            return simpleSegment(text);
        }
    }
    
    /**
     * 使用jieba进行专业分词
     */
    private static List<String> jiebaSegment(String text) {
        // 使用搜索引擎模式分词，提高召回率
        List<SegToken> tokens = GLOBAL_SEGMENTER.process(text, JiebaSegmenter.SegMode.SEARCH);
        
        List<String> keywords = new ArrayList<>();
        Set<String> seenWords = new HashSet<>(); // 去重
        
        for (SegToken token : tokens) {
            String word = token.word.trim().toLowerCase();
            
            // 过滤条件：
            // 1. 长度大于1（避免单字符）
            // 2. 不在停用词表中
            // 3. 不是纯数字、标点或空白字符
            // 4. 未重复出现
            if (isValidWord(word) && !seenWords.contains(word)) {
                keywords.add(word);
                seenWords.add(word);
            }
        }
        
        log.debug("jieba分词结果: {} -> {}", text, keywords);
        return keywords;
    }
    
    /**
     * 简单分词降级方案
     * 当jieba分词失败时使用
     */
    private static List<String> simpleSegment(String text) {
        List<String> keywords = new ArrayList<>();
        Set<String> seenWords = new HashSet<>();
        
        // 按空格和标点分割
        String[] words = text.toLowerCase()
                .replaceAll("[\\p{Punct}]+", " ")
                .split("\\s+");
        
        for (String word : words) {
            word = word.trim();
            if (isValidWord(word) && !seenWords.contains(word)) {
                keywords.add(word);
                seenWords.add(word);
            }
        }
        
        log.debug("简单分词结果: {} -> {}", text, keywords);
        return keywords;
    }
    
    /**
     * 判断词汇是否有效
     */
    private static boolean isValidWord(String word) {
        if (word == null || word.length() <= 1) {
            return false;
        }
        
        // 检查是否为停用词
        if (STOP_WORDS.contains(word) || ENGLISH_STOP_WORDS.contains(word)) {
            return false;
        }
        
        // 检查是否为无效字符（纯数字、标点、空白）
        if (INVALID_PATTERN.matcher(word).matches()) {
            return false;
        }
        
        // 检查是否为单个标点或空白字符
        if (SINGLE_CHAR_PATTERN.matcher(word).matches()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 批量分词处理
     * 用于构建全局词汇表时的批量处理
     */
    public static List<List<String>> batchSegment(List<String> texts) {
        List<List<String>> results = new ArrayList<>();
        
        for (String text : texts) {
            results.add(segment(text));
        }
        
        return results;
    }
    
    /**
     * 获取分词统计信息
     */
    public static SegmentationStats getSegmentationStats(String text) {
        List<String> keywords = segment(text);
        
        int totalWords = keywords.size();
        long chineseWords = keywords.stream()
                .mapToLong(word -> word.matches(".*[\\u4e00-\\u9fa5].*") ? 1 : 0)
                .sum();
        long englishWords = totalWords - chineseWords;
        
        return new SegmentationStats(totalWords, (int) chineseWords, (int) englishWords, keywords);
    }
    
    /**
     * 分词统计信息
     */
    public static class SegmentationStats {
        public final int totalWords;
        public final int chineseWords;
        public final int englishWords;
        public final List<String> keywords;
        
        public SegmentationStats(int totalWords, int chineseWords, int englishWords, List<String> keywords) {
            this.totalWords = totalWords;
            this.chineseWords = chineseWords;
            this.englishWords = englishWords;
            this.keywords = keywords;
        }
        
        @Override
        public String toString() {
            return String.format("总词数: %d, 中文词: %d, 英文词: %d, 关键词: %s", 
                    totalWords, chineseWords, englishWords, keywords);
        }
    }
} 