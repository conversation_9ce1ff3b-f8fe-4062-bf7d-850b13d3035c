package com.zibbava.edgemind.cortex.browser.tools;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zibbava.edgemind.cortex.browser.dto.BrowserToolRequest;
import com.zibbava.edgemind.cortex.browser.dto.BrowserToolResponse;
import com.zibbava.edgemind.cortex.browser.websocket.WebSocketManager;
import dev.langchain4j.agent.tool.ToolExecutionRequest;
import dev.langchain4j.agent.tool.ToolSpecification;
import dev.langchain4j.model.chat.request.json.JsonObjectSchema;
import dev.langchain4j.service.tool.ToolExecutor;
import dev.langchain4j.service.tool.ToolProvider;
import dev.langchain4j.service.tool.ToolProviderRequest;
import dev.langchain4j.service.tool.ToolProviderResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.*;

/**
 * 浏览器工具提供者
 * 实现LangChain4j的ToolProvider接口，为AI提供浏览器自动化工具
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class BrowserToolProvider implements ToolProvider {

    private final WebSocketManager webSocketManager;
    private final ObjectMapper objectMapper;

    // 用于存储当前聊天上下文的工具调用状态
    private static final ThreadLocal<Boolean> toolCallingEnabled = new ThreadLocal<>();

    /**
     * 设置工具调用状态（由Strategy调用）
     */
    public static void setToolCallingEnabled(boolean enabled) {
        toolCallingEnabled.set(enabled);
    }

    /**
     * 清理工具调用状态
     */
    public static void clearToolCallingState() {
        toolCallingEnabled.remove();
    }
    
    @Override
    public ToolProviderResult provideTools(ToolProviderRequest request) {
        log.info("🔧 提供浏览器工具集合");

        // 首先检查是否启用了工具调用（通过前端按钮控制）
        boolean toolCallingEnabled = isToolCallingEnabled(request);
        if (!toolCallingEnabled) {
            log.info("🔧 工具调用未启用，不提供浏览器工具");
            return null;
        }

        // 检查用户消息是否包含浏览器相关内容
        String userMessage = request.userMessage().singleText();
        if (containsBrowserIntent(userMessage)) {

            ToolProviderResult.Builder builder = ToolProviderResult.builder();

            // 创建工具执行器
            ToolExecutor toolExecutor = (ToolExecutionRequest toolRequest, Object memoryId) ->
                executeTool(toolRequest.name(), toolRequest.arguments());

            // 添加基础浏览器工具
            builder.add(createNavigateTool(), toolExecutor);
            builder.add(createClickTool(), toolExecutor);
            builder.add(createFillTool(), toolExecutor);
            builder.add(createScreenshotTool(), toolExecutor);
            builder.add(createGetContentTool(), toolExecutor);

            // 添加高级浏览器工具
            builder.add(createGetWindowsTabsTool(), toolExecutor);
            builder.add(createNetworkCaptureTool(), toolExecutor);
            builder.add(createHistoryTool(), toolExecutor);
            builder.add(createBookmarkSearchTool(), toolExecutor);
            builder.add(createKeyboardTool(), toolExecutor);
            builder.add(createGetInteractiveElementsTool(), toolExecutor);
            builder.add(createSearchTabsContentTool(), toolExecutor);
            builder.add(createNetworkDebuggerTool(), toolExecutor);
            builder.add(createNetworkRequestTool(), toolExecutor);
            builder.add(createCloseTabsTool(), toolExecutor);
            builder.add(createGoBackForwardTool(), toolExecutor);
            builder.add(createSwitchTabTool(), toolExecutor);

            return builder.build();
        }

        return null; // 不提供工具
    }

    /**
     * 检查是否启用了工具调用
     * 通过ThreadLocal获取当前上下文的工具调用状态
     */
    private boolean isToolCallingEnabled(ToolProviderRequest request) {
        Boolean enabled = toolCallingEnabled.get();
        if (enabled != null) {
            log.info("🔧 工具调用状态: {}", enabled);
            return enabled;
        }

        // 如果没有设置状态，默认返回false（不提供工具）
        log.info("🔧 未设置工具调用状态，默认禁用工具调用");
        return false;
    }

    /**
     * 检查是否包含浏览器意图
     */
    private boolean containsBrowserIntent(String userMessage) {
        String lowerMessage = userMessage.toLowerCase();
        return lowerMessage.contains("浏览器") ||
               lowerMessage.contains("网页") ||
               lowerMessage.contains("打开") ||
               lowerMessage.contains("点击") ||
               lowerMessage.contains("搜索") ||
               lowerMessage.contains("截图") ||
               lowerMessage.contains("填写") ||
               lowerMessage.contains("导航") ||
               lowerMessage.contains("访问") ||
               lowerMessage.matches(".*https?://.*");
    }
    
    /**
     * 执行工具调用
     */
    private String executeTool(String toolName, String arguments) {
        try {
            log.info("🚀 执行浏览器工具: {} with args: {}", toolName, arguments);

            // 创建工具请求
            BrowserToolRequest request = BrowserToolRequest.create(toolName, arguments);

            // 通过WebSocket发送到浏览器插件
            BrowserToolResponse response = webSocketManager
                    .sendToolRequest(request)
                    .timeout(Duration.ofSeconds(30))
                    .block();

            if (response == null) {
                throw new RuntimeException("浏览器工具执行超时");
            }

            if (!response.isSuccess()) {
                throw new RuntimeException("浏览器工具执行失败: " + response.getError());
            }

            log.info("✅ 工具执行成功: 工具={}, 结果={}ms", toolName, response);

            // 返回执行结果
            return objectMapper.writeValueAsString(Map.of(
                "success", true,
                "data", response.getData(),
                "toolName", toolName,
                "executionTime", response.getExecutionTimeMs(),
                "timestamp", System.currentTimeMillis()
            ));

        } catch (Exception e) {
            log.error("❌ 执行浏览器工具失败: toolName={}, error={}", toolName, e.getMessage(), e);

            try {
                return objectMapper.writeValueAsString(Map.of(
                    "success", false,
                    "error", e.getMessage(),
                    "toolName", toolName,
                    "timestamp", System.currentTimeMillis()
                ));
            } catch (Exception jsonError) {
                return "{\"success\":false,\"error\":\"" + e.getMessage().replace("\"", "\\\"") + "\"}";
            }
        }
    }

    /**
     * 创建导航工具
     */
    private ToolSpecification createNavigateTool() {
        return ToolSpecification.builder()
                .name("browser_navigate")
                .description("导航到指定的URL地址")
                .parameters(JsonObjectSchema.builder()
                        .addStringProperty("url", "要访问的URL地址")
                        .addBooleanProperty("newWindow", "是否在新窗口中打开（默认false）")
                        .required("url")
                        .build())
                .build();
    }

    /**
     * 创建点击工具
     */
    private ToolSpecification createClickTool() {
        return ToolSpecification.builder()
                .name("browser_click")
                .description("点击页面上的元素")
                .parameters(JsonObjectSchema.builder()
                        .addStringProperty("selector", "CSS选择器，用于定位要点击的元素")
                        .addBooleanProperty("waitForNavigation", "是否等待页面导航完成（默认false）")
                        .build())
                .build();
    }

    /**
     * 创建填写工具
     */
    private ToolSpecification createFillTool() {
        return ToolSpecification.builder()
                .name("browser_fill")
                .description("填写表单字段或输入框")
                .parameters(JsonObjectSchema.builder()
                        .addStringProperty("selector", "CSS选择器，用于定位输入元素")
                        .addStringProperty("value", "要填写的值")
                        .addBooleanProperty("clear", "是否先清空现有内容（默认true）")
                        .required("selector", "value")
                        .build())
                .build();
    }

    /**
     * 创建截图工具
     */
    private ToolSpecification createScreenshotTool() {
        return ToolSpecification.builder()
                .name("browser_screenshot")
                .description("截取浏览器页面截图，支持可见区域、全页面和元素截图")
                .parameters(JsonObjectSchema.builder()
                        .addStringProperty("format", "图片格式：png或jpeg（默认png）")
                        .addIntegerProperty("quality", "JPEG质量（1-100，默认90）")
                        .addStringProperty("type", "截图类型：visible（可见区域）、fullpage（全页面）、element（元素截图），默认visible")
                        .addStringProperty("selector", "元素选择器（type为element时必需）")
                        .addStringProperty("hideElements", "要隐藏的元素选择器，多个用逗号分隔")
                        .addIntegerProperty("waitTime", "截图前等待时间（毫秒，默认1000）")
                        .build())
                .build();
    }

    /**
     * 创建内容获取工具 - 默认返回HTML内容
     */
    private ToolSpecification createGetContentTool() {
        return ToolSpecification.builder()
                .name("browser_get_content")
                .description("获取网页内容，默认返回HTML格式，支持智能文本提取")
                .parameters(JsonObjectSchema.builder()
                        .addStringProperty("url", "要获取内容的URL（可选，不提供则使用当前活跃标签页）")
                        .addBooleanProperty("htmlContent", "获取HTML内容（默认true）")
                        .addBooleanProperty("textContent", "获取智能提取的文本内容（默认false）")
                        .addStringProperty("selector", "CSS选择器，获取特定元素内容（可选）")
                        .build())
                .build();
    }

    /**
     * 创建获取窗口标签页工具
     */
    private ToolSpecification createGetWindowsTabsTool() {
        return ToolSpecification.builder()
                .name("browser_get_windows_tabs")
                .description("获取所有窗口和标签页信息")
                .parameters(JsonObjectSchema.builder()
                        .addBooleanProperty("includeContent", "是否包含页面内容摘要（默认false）")
                        .addBooleanProperty("activeOnly", "是否只返回活跃标签页（默认false）")
                        .build())
                .build();
    }

    /**
     * 创建网络捕获工具
     */
    private ToolSpecification createNetworkCaptureTool() {
        return ToolSpecification.builder()
                .name("browser_network_capture")
                .description("开始或停止网络请求捕获")
                .parameters(JsonObjectSchema.builder()
                        .addStringProperty("action", "操作类型：start（开始）或stop（停止）")
                        .addStringProperty("filters", "请求过滤器（可选，多个用逗号分隔）")
                        .addIntegerProperty("maxRequests", "最大捕获请求数（默认100）")
                        .required("action")
                        .build())
                .build();
    }

    /**
     * 创建历史记录搜索工具
     */
    private ToolSpecification createHistoryTool() {
        return ToolSpecification.builder()
                .name("browser_history")
                .description("搜索浏览器历史记录")
                .parameters(JsonObjectSchema.builder()
                        .addStringProperty("query", "搜索关键词（可选）")
                        .addIntegerProperty("maxResults", "最大结果数（默认20）")
                        .addIntegerProperty("days", "搜索天数范围（默认7天）")
                        .build())
                .build();
    }

    /**
     * 创建书签搜索工具
     */
    private ToolSpecification createBookmarkSearchTool() {
        return ToolSpecification.builder()
                .name("browser_bookmark_search")
                .description("搜索书签")
                .parameters(JsonObjectSchema.builder()
                        .addStringProperty("query", "搜索关键词")
                        .addIntegerProperty("maxResults", "最大结果数（默认20）")
                        .required("query")
                        .build())
                .build();
    }

    /**
     * 创建键盘输入工具
     */
    private ToolSpecification createKeyboardTool() {
        return ToolSpecification.builder()
                .name("browser_keyboard")
                .description("模拟键盘输入")
                .parameters(JsonObjectSchema.builder()
                        .addStringProperty("keys", "按键序列，如'Ctrl+C'、'Enter'等")
                        .addStringProperty("text", "要输入的文本（与keys二选一）")
                        .addIntegerProperty("delay", "按键间隔（毫秒，默认100）")
                        .build())
                .build();
    }

    /**
     * 创建获取交互元素工具
     */
    private ToolSpecification createGetInteractiveElementsTool() {
        return ToolSpecification.builder()
                .name("browser_get_interactive_elements")
                .description("获取页面上所有可交互元素")
                .parameters(JsonObjectSchema.builder()
                        .addStringProperty("types", "元素类型过滤器，多个用逗号分隔：button,input,link,select等")
                        .addBooleanProperty("visibleOnly", "是否只返回可见元素（默认true）")
                        .addIntegerProperty("maxElements", "最大元素数（默认50）")
                        .build())
                .build();
    }

    /**
     * 创建语义搜索标签页内容工具
     */
    private ToolSpecification createSearchTabsContentTool() {
        return ToolSpecification.builder()
                .name("browser_search_tabs_content")
                .description("使用语义搜索在所有打开的标签页中查找相关内容")
                .parameters(JsonObjectSchema.builder()
                        .addStringProperty("query", "搜索查询，支持自然语言描述")
                        .addIntegerProperty("maxResults", "最大结果数（默认10）")
                        .addNumberProperty("minScore", "最小相似度分数（0-1，默认0.7）")
                        .addBooleanProperty("includeContent", "是否包含匹配的内容片段（默认true）")
                        .required("query")
                        .build())
                .build();
    }

    /**
     * 创建网络调试器工具
     */
    private ToolSpecification createNetworkDebuggerTool() {
        return ToolSpecification.builder()
                .name("browser_network_debugger")
                .description("使用Chrome调试器API进行高级网络监控，可获取完整的请求/响应数据")
                .parameters(JsonObjectSchema.builder()
                        .addStringProperty("action", "操作类型：start（开始）或stop（停止）")
                        .addIntegerProperty("maxRequests", "最大捕获请求数（默认100）")
                        .addIntegerProperty("maxResponseSize", "最大响应体大小（字节，默认1MB）")
                        .required("action")
                        .build())
                .build();
    }

    /**
     * 创建网络请求工具
     */
    private ToolSpecification createNetworkRequestTool() {
        return ToolSpecification.builder()
                .name("browser_network_request")
                .description("在浏览器上下文中发送自定义HTTP请求")
                .parameters(JsonObjectSchema.builder()
                        .addStringProperty("url", "请求URL")
                        .addStringProperty("method", "HTTP方法（GET, POST, PUT, DELETE等，默认GET）")
                        .addStringProperty("headers", "请求头，JSON格式字符串")
                        .addStringProperty("body", "请求体（POST/PUT时使用）")
                        .addIntegerProperty("timeout", "超时时间（毫秒，默认30000）")
                        .addBooleanProperty("followRedirects", "是否跟随重定向（默认true）")
                        .required("url")
                        .build())
                .build();
    }

    /**
     * 创建关闭标签页工具
     */
    private ToolSpecification createCloseTabsTool() {
        return ToolSpecification.builder()
                .name("browser_close_tabs")
                .description("关闭指定的标签页或匹配模式的标签页")
                .parameters(JsonObjectSchema.builder()
                        .addStringProperty("tabIds", "要关闭的标签页ID列表，多个用逗号分隔")
                        .addStringProperty("pattern", "匹配模式，关闭URL或标题包含此模式的标签页")
                        .addBooleanProperty("keepActive", "是否保留当前活跃标签页（默认true）")
                        .addBooleanProperty("confirmClose", "关闭多个标签页时是否确认（默认false）")
                        .build())
                .build();
    }

    /**
     * 创建前进后退导航工具
     */
    private ToolSpecification createGoBackForwardTool() {
        return ToolSpecification.builder()
                .name("browser_go_back_forward")
                .description("在当前标签页中执行前进或后退导航")
                .parameters(JsonObjectSchema.builder()
                        .addStringProperty("action", "导航动作：back（后退）或forward（前进）")
                        .addIntegerProperty("steps", "导航步数（默认1）")
                        .required("action")
                        .build())
                .build();
    }

    /**
     * 创建切换标签页工具
     */
    private ToolSpecification createSwitchTabTool() {
        return ToolSpecification.builder()
                .name("browser_switch_tab")
                .description("切换到指定的标签页")
                .parameters(JsonObjectSchema.builder()
                        .addIntegerProperty("tabId", "目标标签页ID")
                        .addStringProperty("direction", "切换方向：next（下一个）或prev（上一个）")
                        .addStringProperty("pattern", "匹配模式，切换到URL或标题包含此模式的标签页")
                        .addIntegerProperty("index", "标签页索引（从0开始）")
                        .build())
                .build();
    }
}
