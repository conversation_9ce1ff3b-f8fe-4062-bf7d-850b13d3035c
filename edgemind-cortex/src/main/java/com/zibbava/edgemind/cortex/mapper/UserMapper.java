package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zibbava.edgemind.cortex.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

@Mapper // 显式添加 @Mapper 注解
public interface UserMapper extends BaseMapper<User> {

    // BaseMapper 已经提供了根据 ID 查询、插入、更新、删除等常用方法
    // 对于根据 username 查询，我们将在 Service 层使用 QueryWrapper
    // 如果需要更复杂的 SQL 或者性能优化，可以在这里定义方法并使用 @Select 注解或 XML 实现

    /**
     * 分页查询用户列表（包含部门名称）
     */
    @Select("<script>" +
            "SELECT u.*, d.dept_name " +
            "FROM sys_user u " +
            "LEFT JOIN sys_department d ON u.dept_id = d.id " +
            "WHERE 1=1 " +
            "<if test='params.username != null and params.username != \"\"'>" +
            "AND u.username LIKE CONCAT('%', #{params.username}, '%') " +
            "</if>" +
            "<if test='params.nickname != null and params.nickname != \"\"'>" +
            "AND u.nickname LIKE CONCAT('%', #{params.nickname}, '%') " +
            "</if>" +
            "<if test='params.email != null and params.email != \"\"'>" +
            "AND u.email LIKE CONCAT('%', #{params.email}, '%') " +
            "</if>" +
            "<if test='params.phone != null and params.phone != \"\"'>" +
            "AND u.phone LIKE CONCAT('%', #{params.phone}, '%') " +
            "</if>" +
            "<if test='params.deptId != null'>" +
            "AND u.dept_id = #{params.deptId} " +
            "</if>" +
            "<if test='params.status != null'>" +
            "AND u.status = #{params.status} " +
            "</if>" +
            "ORDER BY u.create_time DESC" +
            "</script>")
    IPage<Map<String, Object>> selectUserPageWithDept(Page<?> page, @Param("params") Map<String, Object> params);

}