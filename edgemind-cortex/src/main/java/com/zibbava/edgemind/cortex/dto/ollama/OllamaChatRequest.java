package com.zibbava.edgemind.cortex.dto.ollama;

import dev.langchain4j.service.tool.ToolProvider;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * Ollama聊天请求参数类
 * 完整支持Ollama Chat API的所有参数
 * 集成LangChain4j ToolProvider支持
 */
@Data
@Builder
public class OllamaChatRequest {
    // 必需参数
    @Builder.Default
    private String model = "";
    @Builder.Default
    private List<OllamaMessage> messages = new ArrayList<>();

    // 可选参数
    private Boolean stream;  // 是否流式输出，null表示使用默认值
    private ResponseFormat format;   // 输出格式，支持JSON和Schema
    private String keepAlive; // 模型保持加载时间，如"5m", "1h", "30s"等
    private OllamaOptions options; // 模型选项参数
    private Boolean think; // (思维模型)是否启用思考模式

    // LangChain4j ToolProvider 集成
    private ToolProvider toolProvider; // 工具提供者

    // 内部使用参数
    @Builder.Default
    private String baseUrl = "http://localhost:11434"; // Ollama服务器地址
    @Builder.Default
    private String sessionId = UUID.randomUUID().toString(); // 会话ID，用于中断管理

    /**
     * 创建基础聊天请求
     */
    public static OllamaChatRequest create(String model) {
        return OllamaChatRequest.builder()
                .model(model)
                .build();
    }

    /**
     * 添加用户消息
     */
    public OllamaChatRequest addUserMessage(String content) {
        this.messages.add(OllamaMessage.user(content));
        return this;
    }

    /**
     * 添加用户消息（带图片）
     */
    public OllamaChatRequest addUserMessage(String content, List<String> images) {
        this.messages.add(OllamaMessage.builder()
                .role("user")
                .content(content)
                .images(images)
                .build());
        return this;
    }

    /**
     * 添加系统消息
     */
    public OllamaChatRequest addSystemMessage(String content) {
        this.messages.add(OllamaMessage.builder()
                .role("system")
                .content(content)
                .build());
        return this;
    }

    /**
     * 添加助手消息
     */
    public OllamaChatRequest addAssistantMessage(String content) {
        this.messages.add(OllamaMessage.assistant(content));
        return this;
    }

    /**
     * 设置流式输出
     */
    public OllamaChatRequest stream(boolean stream) {
        this.stream = stream;
        return this;
    }

    /**
     * 设置JSON格式输出
     */
    public OllamaChatRequest jsonFormat() {
        this.format = ResponseFormat.json();
        return this;
    }

    /**
     * 设置JSON Schema格式输出（使用JsonSchema对象）
     */
    public OllamaChatRequest jsonFormat(JsonSchema schema) {
        this.format = ResponseFormat.schema(schema);
        return this;
    }

    /**
     * 设置模型保持加载时间
     */
    public OllamaChatRequest keepAlive(String keepAlive) {
        this.keepAlive = keepAlive;
        return this;
    }

    /**
     * 设置工具提供者（支持MCP和Function Calling）
     */
    public OllamaChatRequest toolProvider(ToolProvider toolProvider) {
        this.toolProvider = toolProvider;
        return this;
    }

    /**
     * 设置选项参数
     */
    public OllamaChatRequest options(OllamaOptions options) {
        this.options = options;
        return this;
    }

    /**
     * 设置温度参数
     */
    public OllamaChatRequest temperature(float temperature) {
        if (this.options == null) {
            this.options = OllamaOptions.createDefault();
        }
        this.options.setTemperature(temperature);
        return this;
    }

    /**
     * 设置top_p参数
     */
    public OllamaChatRequest topP(float topP) {
        if (this.options == null) {
            this.options = OllamaOptions.createDefault();
        }
        this.options.setTopP(topP);
        return this;
    }

    /**
     * 设置最大生成令牌数
     */
    public OllamaChatRequest maxTokens(int maxTokens) {
        if (this.options == null) {
            this.options = OllamaOptions.createDefault();
        }
        this.options.setNumPredict(maxTokens);
        return this;
    }

    /**
     * 设置种子，用于可重现输出
     */
    public OllamaChatRequest seed(int seed) {
        if (this.options == null) {
            this.options = OllamaOptions.createDefault();
        }
        this.options.setSeed(seed);
        return this;
    }

    /**
     * 设置停止词
     */
    public OllamaChatRequest stopWords(String... stopWords) {
        if (this.options == null) {
            this.options = OllamaOptions.createDefault();
        }
        this.options.setStop(Arrays.asList(stopWords));
        return this;
    }

    /**
     * 设置思考模式（用于思维模型）
     */
    public OllamaChatRequest enableThinking(boolean think) {
        this.think = think;
        return this;
    }

    /**
     * 生成会话ID
     */
    public OllamaChatRequest generateSessionId() {
        this.sessionId = UUID.randomUUID().toString();
        return this;
    }
} 