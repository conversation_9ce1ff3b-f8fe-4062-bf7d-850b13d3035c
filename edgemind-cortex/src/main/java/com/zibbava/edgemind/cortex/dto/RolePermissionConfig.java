package com.zibbava.edgemind.cortex.dto;

import com.zibbava.edgemind.cortex.entity.KnowledgeSpaceRole;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 角色权限配置DTO
 * 
 * <AUTHOR>
 */
@Data
public class RolePermissionConfig {
    
    @NotNull(message = "角色ID不能为空")
    private Long roleId;
    
    @NotNull(message = "权限级别不能为空")
    private KnowledgeSpaceRole.PermissionLevel permissionLevel;
    
    private String roleName; // 角色名称（用于显示）
}
