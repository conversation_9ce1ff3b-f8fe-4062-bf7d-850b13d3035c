package com.zibbava.edgemind.cortex.browser.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zibbava.edgemind.cortex.browser.dto.BrowserToolResponse;
import com.zibbava.edgemind.cortex.browser.service.BrowserSemanticSearchService;
import com.zibbava.edgemind.cortex.browser.service.BrowserContentProcessorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.Map;

/**
 * 浏览器WebSocket处理器
 * 处理浏览器插件的WebSocket连接和消息
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class BrowserWebSocketHandler extends TextWebSocketHandler {
    
    private final WebSocketManager webSocketManager;
    private final ObjectMapper objectMapper;
    private final BrowserSemanticSearchService semanticSearchService;
    private final BrowserContentProcessorService contentProcessorService;
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = extractSessionId(session);
        String clientInfo = extractClientInfo(session);
        
        log.info("🔗 浏览器插件连接建立: sessionId={}, clientInfo={}", sessionId, clientInfo);
        
        // 注册会话到管理器
        webSocketManager.registerSession(sessionId, session);
        
        // 发送欢迎消息
        sendWelcomeMessage(session, sessionId);
        
        log.info("✅ 浏览器插件连接成功注册: sessionId={}", sessionId);
    }
    
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String sessionId = extractSessionId(session);
        String payload = message.getPayload();
        
        log.debug("📨 收到WebSocket消息: sessionId={}, payload={}", sessionId, payload);
        
        try {
            // 解析消息
            Map<String, Object> messageData = objectMapper.readValue(payload, Map.class);
            String messageType = (String) messageData.get("type");
            
            switch (messageType) {
                case "HANDSHAKE":
                    handleHandshake(session, messageData);
                    break;
                case "TOOL_RESPONSE":
                    handleToolResponse(session, messageData);
                    break;
                case "PONG":
                    handlePong(session, messageData);
                    break;
                case "ERROR":
                    handleError(session, messageData);
                    break;
                case "SEMANTIC_SEARCH_REQUEST":
                    handleSemanticSearchRequest(session, messageData);
                    break;
                case "CONTENT_PROCESS_REQUEST":
                    handleContentProcessRequest(session, messageData);
                    break;
                default:
                    log.warn("⚠️ 未知消息类型: {}", messageType);
                    sendErrorMessage(session, "未知消息类型: " + messageType);
            }
            
        } catch (Exception e) {
            log.error("❌ 处理WebSocket消息失败: sessionId={}, error={}", sessionId, e.getMessage(), e);
            sendErrorMessage(session, "消息处理失败: " + e.getMessage());
        }
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        String sessionId = extractSessionId(session);
        
        log.info("🔌 浏览器插件连接关闭: sessionId={}, status={}", sessionId, status);
        
        // 从管理器中移除会话
        webSocketManager.unregisterSession(sessionId);
        
        log.info("✅ 浏览器插件会话已清理: sessionId={}", sessionId);
    }
    
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String sessionId = extractSessionId(session);
        
        log.error("❌ WebSocket传输错误: sessionId={}, error={}", sessionId, exception.getMessage(), exception);
        
        // 尝试关闭会话
        try {
            if (session.isOpen()) {
                session.close(CloseStatus.SERVER_ERROR);
            }
        } catch (Exception e) {
            log.error("关闭会话失败: {}", e.getMessage());
        }
        
        // 清理会话
        webSocketManager.unregisterSession(sessionId);
    }
    
    /**
     * 处理握手消息
     */
    private void handleHandshake(WebSocketSession session, Map<String, Object> messageData) throws IOException {
        String clientType = (String) messageData.get("clientType");
        String version = (String) messageData.get("version");
        Object capabilities = messageData.get("capabilities");
        
        log.info("🤝 收到握手消息: clientType={}, version={}, capabilities={}", 
                clientType, version, capabilities);
        
        // 更新会话信息
        String sessionId = extractSessionId(session);
        webSocketManager.updateSessionInfo(sessionId, clientType, version, capabilities);
        
        // 发送握手响应
        Map<String, Object> response = Map.of(
            "type", "HANDSHAKE_ACK",
            "status", "success",
            "serverVersion", "1.0.0",
            "timestamp", System.currentTimeMillis()
        );
        
        session.sendMessage(new TextMessage(objectMapper.writeValueAsString(response)));
    }
    
    /**
     * 处理工具响应消息
     */
    private void handleToolResponse(WebSocketSession session, Map<String, Object> messageData) {
        try {
            // 将消息数据转换为BrowserToolResponse对象
            BrowserToolResponse response = objectMapper.convertValue(messageData, BrowserToolResponse.class);
            
            log.debug("🔧 收到工具响应: requestId={}, success={}", 
                    response.getRequestId(), response.isSuccess());
            
            // 通过管理器处理响应
            webSocketManager.handleToolResponse(response);
            
        } catch (Exception e) {
            log.error("❌ 处理工具响应失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 处理心跳响应
     */
    private void handlePong(WebSocketSession session, Map<String, Object> messageData) {
        String sessionId = extractSessionId(session);
        long timestamp = ((Number) messageData.get("timestamp")).longValue();
        
        log.debug("💓 收到心跳响应: sessionId={}, timestamp={}", sessionId, timestamp);
        
        // 更新会话最后活跃时间
        webSocketManager.updateLastActivity(sessionId);
    }
    
    /**
     * 处理错误消息
     */
    private void handleError(WebSocketSession session, Map<String, Object> messageData) {
        String error = (String) messageData.get("error");
        String errorCode = (String) messageData.get("errorCode");
        
        log.error("❌ 收到客户端错误: error={}, errorCode={}", error, errorCode);
    }
    
    /**
     * 发送欢迎消息
     */
    private void sendWelcomeMessage(WebSocketSession session, String sessionId) throws IOException {
        Map<String, Object> welcome = Map.of(
            "type", "WELCOME",
            "sessionId", sessionId,
            "serverVersion", "1.0.0",
            "timestamp", System.currentTimeMillis(),
            "message", "欢迎连接EdgeMind浏览器自动化服务"
        );
        
        session.sendMessage(new TextMessage(objectMapper.writeValueAsString(welcome)));
    }
    
    /**
     * 发送错误消息
     */
    private void sendErrorMessage(WebSocketSession session, String error) throws IOException {
        Map<String, Object> errorMsg = Map.of(
            "type", "ERROR",
            "error", error,
            "timestamp", System.currentTimeMillis()
        );
        
        session.sendMessage(new TextMessage(objectMapper.writeValueAsString(errorMsg)));
    }
    
    /**
     * 提取会话ID
     */
    private String extractSessionId(WebSocketSession session) {
        return session.getId();
    }
    
    /**
     * 处理语义搜索请求
     */
    private void handleSemanticSearchRequest(WebSocketSession session, Map<String, Object> messageData) {
        String requestId = (String) messageData.get("requestId");
        String query = (String) messageData.get("query");
        Integer maxResults = (Integer) messageData.getOrDefault("maxResults", 10);
        Double minScore = (Double) messageData.getOrDefault("minScore", 0.7);
        Boolean includeContent = (Boolean) messageData.getOrDefault("includeContent", true);

        log.info("🔍 处理语义搜索请求: requestId={}, query={}", requestId, query);

        try {
            // 处理标签页内容索引
            @SuppressWarnings("unchecked")
            java.util.List<Map<String, Object>> tabContents =
                (java.util.List<Map<String, Object>>) messageData.get("tabContents");

            if (tabContents != null) {
                for (Map<String, Object> tabContent : tabContents) {
                    Integer tabId = (Integer) tabContent.get("tabId");
                    String url = (String) tabContent.get("url");
                    String title = (String) tabContent.get("title");
                    String content = (String) tabContent.get("content");

                    // 索引标签页内容
                    semanticSearchService.indexTabContent(tabId, url, title, content);
                }
            }

            // 执行语义搜索
            long startTime = System.currentTimeMillis();
            java.util.List<BrowserSemanticSearchService.SearchResult> results =
                semanticSearchService.searchTabsContent(query, maxResults, minScore, includeContent);
            long searchTime = System.currentTimeMillis() - startTime;

            // 发送搜索结果
            Map<String, Object> response = Map.of(
                "type", "SEMANTIC_SEARCH_RESPONSE",
                "requestId", requestId,
                "success", true,
                "results", results,
                "totalResults", results.size(),
                "searchTime", searchTime,
                "query", query,
                "timestamp", System.currentTimeMillis()
            );

            session.sendMessage(new TextMessage(objectMapper.writeValueAsString(response)));

            log.info("✅ 语义搜索完成: requestId={}, 结果数={}, 耗时={}ms",
                requestId, results.size(), searchTime);

        } catch (Exception e) {
            log.error("❌ 语义搜索失败: requestId={}, error={}", requestId, e.getMessage(), e);

            try {
                Map<String, Object> errorResponse = Map.of(
                    "type", "SEMANTIC_SEARCH_RESPONSE",
                    "requestId", requestId,
                    "success", false,
                    "error", e.getMessage(),
                    "timestamp", System.currentTimeMillis()
                );

                session.sendMessage(new TextMessage(objectMapper.writeValueAsString(errorResponse)));
            } catch (IOException ioException) {
                log.error("❌ 发送语义搜索错误响应失败: {}", ioException.getMessage());
            }
        }
    }

    /**
     * 处理内容处理请求
     */
    private void handleContentProcessRequest(WebSocketSession session, Map<String, Object> messageData) {
        String requestId = (String) messageData.get("requestId");
        String htmlContent = (String) messageData.get("htmlContent");
        @SuppressWarnings("unchecked")
        Map<String, Object> metadata = (Map<String, Object>) messageData.get("metadata");

        log.info("🔍 处理内容处理请求: requestId={}, url={}", requestId, metadata.get("url"));

        try {
            // 使用内容处理服务处理HTML
            BrowserContentProcessorService.ContentProcessResult result =
                contentProcessorService.processContent(htmlContent, metadata);

            // 发送处理结果
            Map<String, Object> response = Map.of(
                "type", "CONTENT_PROCESS_RESPONSE",
                "requestId", requestId,
                "success", true,
                "content", result.getContent(),
                "contentType", result.getContentType(),
                "length", result.getLength(),
                "wordCount", result.getWordCount(),
                "metadata", result.getMetadata(),
                "timestamp", System.currentTimeMillis()
            );

            session.sendMessage(new TextMessage(objectMapper.writeValueAsString(response)));

            log.info("✅ 内容处理完成: requestId={}, 长度={}, 词数={}",
                requestId, result.getLength(), result.getWordCount());

        } catch (Exception e) {
            log.error("❌ 内容处理失败: requestId={}, error={}", requestId, e.getMessage(), e);

            try {
                Map<String, Object> errorResponse = Map.of(
                    "type", "CONTENT_PROCESS_RESPONSE",
                    "requestId", requestId,
                    "success", false,
                    "error", e.getMessage(),
                    "timestamp", System.currentTimeMillis()
                );

                session.sendMessage(new TextMessage(objectMapper.writeValueAsString(errorResponse)));
            } catch (IOException ioException) {
                log.error("❌ 发送内容处理错误响应失败: {}", ioException.getMessage());
            }
        }
    }

    /**
     * 提取客户端信息
     */
    private String extractClientInfo(WebSocketSession session) {
        return session.getRemoteAddress() != null ?
               session.getRemoteAddress().toString() : "unknown";
    }
}
