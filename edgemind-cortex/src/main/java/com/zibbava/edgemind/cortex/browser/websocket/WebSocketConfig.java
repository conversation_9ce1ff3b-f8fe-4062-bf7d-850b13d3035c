package com.zibbava.edgemind.cortex.browser.websocket;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket配置类
 * 配置浏览器自动化的WebSocket端点，支持浏览器插件连接
 */
@Configuration
@EnableWebSocket
@RequiredArgsConstructor
@Slf4j
public class WebSocketConfig implements WebSocketConfigurer {
    
    private final BrowserWebSocketHandler browserWebSocketHandler;
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        log.info("🔧 注册浏览器自动化WebSocket处理器");

        // 注册浏览器自动化WebSocket端点
        // 使用原生WebSocket，不使用SockJS
        registry.addHandler(browserWebSocketHandler, "/browser-automation")
                .setAllowedOrigins("*"); // 允许所有来源，生产环境应该限制

        log.info("✅ WebSocket端点已注册: /browser-automation (完整路径: /wkg/browser-automation)");
    }
}
