package com.zibbava.edgemind.cortex.service;

import com.zibbava.edgemind.cortex.dto.CreateSpaceRequest;
import com.zibbava.edgemind.cortex.dto.RolePermissionConfig;
import com.zibbava.edgemind.cortex.entity.KnowledgeSpace;
import com.zibbava.edgemind.cortex.entity.KnowledgeSpaceRole;

import java.util.List;

/**
 * 动态知识库空间管理服务接口
 * 
 * <AUTHOR>
 */
public interface DynamicKnowledgeSpaceService {
    
    /**
     * 创建动态知识空间
     * 
     * @param request 创建请求
     * @return 创建的知识空间
     */
    KnowledgeSpace createDynamicSpace(CreateSpaceRequest request);
    
    /**
     * 获取用户可访问的所有知识空间（基于角色权限）
     * 
     * @param userId 用户ID
     * @return 可访问的知识空间列表
     */
    List<KnowledgeSpace> getAccessibleSpaces(Long userId);

    /**
     * 获取所有知识空间列表（用于管理页面）
     *
     * @return 所有知识空间列表
     */
    List<KnowledgeSpace> getAllSpaces();

    /**
     * 根据ID获取知识空间详情
     *
     * @param spaceId 知识空间ID
     * @return 知识空间详情
     */
    KnowledgeSpace getSpaceById(String spaceId);

    /**
     * 更新知识空间信息
     *
     * @param spaceId 知识空间ID
     * @param request 更新请求
     * @return 更新后的知识空间
     */
    KnowledgeSpace updateSpace(String spaceId, CreateSpaceRequest request);

    /**
     * 检查用户对知识空间的访问权限
     * 
     * @param userId 用户ID
     * @param spaceId 知识空间ID
     * @param action 操作类型
     * @return 是否有权限
     */
    boolean hasSpaceAccess(Long userId, String spaceId, String action);
    
    /**
     * 更新知识空间的角色权限
     * 
     * @param spaceId 知识空间ID
     * @param rolePermissions 角色权限配置列表
     */
    void updateSpaceRolePermissions(String spaceId, List<RolePermissionConfig> rolePermissions);
    
    /**
     * 获取知识空间的角色权限配置
     * 
     * @param spaceId 知识空间ID
     * @return 角色权限配置列表
     */
    List<KnowledgeSpaceRole> getSpaceRolePermissions(String spaceId);
    
    /**
     * 删除知识空间
     * 
     * @param spaceId 知识空间ID
     */
    void deleteSpace(String spaceId);
}
