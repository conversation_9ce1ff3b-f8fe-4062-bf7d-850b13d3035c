package com.zibbava.edgemind.cortex.browser.controller;

import com.zibbava.edgemind.cortex.browser.service.BrowserAutomationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 浏览器自动化控制器
 * 提供浏览器自动化的管理和监控接口
 */
@RestController
@RequestMapping("/wkg/api/browser")
@RequiredArgsConstructor
@Slf4j
public class BrowserAutomationController {
    
    private final BrowserAutomationService browserAutomationService;
    
    /**
     * 获取浏览器自动化系统状态
     */
    @GetMapping("/status")
    public Map<String, Object> getStatus() {
        log.info("📊 获取浏览器自动化系统状态");
        return browserAutomationService.getHealthStatus();
    }
    
    /**
     * 获取系统统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getStats() {
        log.info("📈 获取浏览器自动化统计信息");
        return browserAutomationService.getSystemStats();
    }
    
    /**
     * 运行系统诊断
     */
    @GetMapping("/diagnostics")
    public Map<String, Object> runDiagnostics() {
        log.info("🔍 运行浏览器自动化系统诊断");
        return browserAutomationService.runDiagnostics();
    }
    
    /**
     * 重置统计信息
     */
    @PostMapping("/stats/reset")
    public Map<String, Object> resetStats() {
        log.info("🔄 重置浏览器自动化统计信息");
        browserAutomationService.resetStats();
        return Map.of(
            "success", true,
            "message", "统计信息已重置",
            "timestamp", System.currentTimeMillis()
        );
    }
    
    /**
     * 获取连接的浏览器插件信息
     */
    @GetMapping("/plugins")
    public Map<String, Object> getPluginInfo() {
        log.info("🔌 获取浏览器插件连接信息");
        return Map.of(
            "connected", browserAutomationService.isPluginConnected(),
            "count", browserAutomationService.getConnectedPluginCount(),
            "pendingRequests", browserAutomationService.getPendingRequestCount(),
            "timestamp", System.currentTimeMillis()
        );
    }
}
