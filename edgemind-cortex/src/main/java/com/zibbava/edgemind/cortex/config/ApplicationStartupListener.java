package com.zibbava.edgemind.cortex.config;

import com.zibbava.edgemind.cortex.service.AutoLicenseActivationService;
import com.zibbava.edgemind.cortex.service.SystemSettingsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 应用程序启动监听器
 * 在应用程序完全启动后执行初始化操作
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ApplicationStartupListener implements ApplicationListener<ApplicationReadyEvent> {

    @Lazy
    private final SystemSettingsService systemSettingsService;
    
    @Lazy
    private final AutoLicenseActivationService autoLicenseActivationService;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("应用程序已启动，开始执行初始化操作");

        try {
            // 初始化知识库设置
            systemSettingsService.initSystemSettings();
            log.info("知识库设置初始化完成");
        } catch (Exception e) {
            log.error("知识库设置初始化失败", e);
        }

        try {
            // 检查并自动激活免费试用许可证
            autoLicenseActivationService.checkAndActivateFreeTrial();
            log.info("自动激活检查完成");
        } catch (Exception e) {
            log.error("自动激活免费试用许可证失败", e);
        }
    }
}
