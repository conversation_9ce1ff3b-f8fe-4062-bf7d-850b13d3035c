package com.zibbava.edgemind.cortex.service.streaming;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 流式响应格式化器
 * 统一本地模型和远程模型的流式响应格式
 * 确保前端能够正确解析和渲染所有类型的响应
 */
@Slf4j
@Component
public class StreamingResponseFormatter {

    /**
     * 格式化thinking内容
     * 统一thinking内容的输出格式
     * 
     * @param thinkingContent thinking内容
     * @return 格式化后的SSE数据
     */
    public String formatThinkingContent(String thinkingContent) {
        if (thinkingContent == null || thinkingContent.isEmpty()) {
            return "";
        }
        
        // 使用前缀格式，确保与handelSSE方法兼容
        return "THINKING:" + thinkingContent;
    }

    /**
     * 格式化主要内容
     * 统一主要内容的输出格式
     * 
     * @param content 主要内容
     * @return 格式化后的SSE数据
     */
    public String formatMainContent(String content) {
        if (content == null || content.isEmpty()) {
            return "";
        }
        
        // 使用前缀格式，确保与handelSSE方法兼容
        return "CONTENT:" + content;
    }

    /**
     * 格式化完成标识
     * 
     * @return 格式化后的完成标识
     */
    public String formatDoneSignal() {
        return "DONE:";
    }

    /**
     * 格式化thinking开始标识
     * 
     * @return 格式化后的thinking开始标识
     */
    public String formatThinkingStart() {
        return "THINKING_START:";
    }

    /**
     * 格式化thinking结束标识
     * 
     * @return 格式化后的thinking结束标识
     */
    public String formatThinkingEnd() {
        return "THINKING_END:";
    }

    /**
     * 格式化工具调用结果
     * 
     * @param toolResult 工具调用结果
     * @return 格式化后的工具调用结果
     */
    public String formatToolResult(String toolResult) {
        if (toolResult == null || toolResult.isEmpty()) {
            return "";
        }
        
        return "TOOL_RESULT:" + toolResult;
    }

    /**
     * 格式化错误信息
     * 
     * @param errorMessage 错误信息
     * @return 格式化后的错误信息
     */
    public String formatError(String errorMessage) {
        if (errorMessage == null || errorMessage.isEmpty()) {
            return "";
        }
        
        return "ERROR:" + errorMessage;
    }

    /**
     * 从JSON格式转换为前缀格式
     * 用于处理langchain4j组件返回的JSON格式响应
     * 
     * @param jsonResponse JSON格式的响应
     * @return 前缀格式的响应
     */
    public String convertJsonToPrefix(String jsonResponse) {
        if (jsonResponse == null || jsonResponse.isEmpty()) {
            return "";
        }

        try {
            // 简单的JSON解析，避免引入额外依赖
            if (jsonResponse.contains("\"type\":\"thinking\"")) {
                // 提取thinking内容
                String content = extractJsonContent(jsonResponse);
                return formatThinkingContent(content);
                
            } else if (jsonResponse.contains("\"type\":\"content\"")) {
                // 提取主要内容
                String content = extractJsonContent(jsonResponse);
                return formatMainContent(content);
                
            } else if (jsonResponse.contains("\"type\":\"thinking_start\"")) {
                return formatThinkingStart();
                
            } else if (jsonResponse.contains("\"type\":\"thinking_end\"")) {
                return formatThinkingEnd();
                
            } else if (jsonResponse.contains("\"type\":\"done\"")) {
                return formatDoneSignal();
                
            } else if (jsonResponse.contains("\"type\":\"error\"")) {
                String content = extractJsonContent(jsonResponse);
                return formatError(content);
            }
            
        } catch (Exception e) {
            log.warn("⚠️ JSON转换失败，返回原始内容: {}", e.getMessage());
        }
        
        // 如果无法解析，返回原始内容
        return jsonResponse;
    }

    /**
     * 从JSON字符串中提取content字段的值
     * 简单的字符串解析，避免引入JSON库依赖
     * 
     * @param jsonString JSON字符串
     * @return 提取的内容
     */
    private String extractJsonContent(String jsonString) {
        try {
            // 查找 "content":"..." 模式
            int contentStart = jsonString.indexOf("\"content\":\"");
            if (contentStart == -1) {
                return "";
            }
            
            contentStart += 11; // 跳过 "content":"
            int contentEnd = jsonString.lastIndexOf("\"");
            
            if (contentEnd <= contentStart) {
                return "";
            }
            
            String content = jsonString.substring(contentStart, contentEnd);
            
            // 反转义JSON字符串
            return unescapeJsonString(content);
            
        } catch (Exception e) {
            log.warn("⚠️ 提取JSON内容失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 反转义JSON字符串
     * 
     * @param escapedString 转义后的字符串
     * @return 反转义后的字符串
     */
    private String unescapeJsonString(String escapedString) {
        if (escapedString == null) {
            return "";
        }
        
        return escapedString
                .replace("\\\"", "\"")
                .replace("\\\\", "\\")
                .replace("\\n", "\n")
                .replace("\\r", "\r")
                .replace("\\t", "\t");
    }

    /**
     * 检查响应是否为JSON格式
     * 
     * @param response 响应内容
     * @return 是否为JSON格式
     */
    public boolean isJsonFormat(String response) {
        if (response == null || response.isEmpty()) {
            return false;
        }
        
        String trimmed = response.trim();
        return trimmed.startsWith("{") && trimmed.endsWith("}") && trimmed.contains("\"type\":");
    }

    /**
     * 检查响应是否为前缀格式
     * 
     * @param response 响应内容
     * @return 是否为前缀格式
     */
    public boolean isPrefixFormat(String response) {
        if (response == null || response.isEmpty()) {
            return false;
        }
        
        return response.startsWith("THINKING:") || 
               response.startsWith("CONTENT:") || 
               response.startsWith("DONE:") ||
               response.startsWith("THINKING_START:") ||
               response.startsWith("THINKING_END:") ||
               response.startsWith("TOOL_RESULT:") ||
               response.startsWith("ERROR:");
    }

    /**
     * 统一格式化响应
     * 将任何格式的响应转换为标准前缀格式
     * 
     * @param response 原始响应
     * @return 标准格式的响应
     */
    public String unifyResponseFormat(String response) {
        if (response == null || response.isEmpty()) {
            return "";
        }
        
        // 如果已经是前缀格式，直接返回
        if (isPrefixFormat(response)) {
            return response;
        }
        
        // 如果是JSON格式，转换为前缀格式
        if (isJsonFormat(response)) {
            return convertJsonToPrefix(response);
        }
        
        // 其他情况，作为主要内容处理
        return formatMainContent(response);
    }
}
