package com.zibbava.edgemind.cortex.service;

import com.zibbava.edgemind.cortex.entity.RemoteModelConfig;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.StreamingChatModel;

import java.util.List;
import java.util.Map;

/**
 * 远程模型集成服务
 * 负责将配置好的远程模型集成到LangChain4j中
 */
public interface RemoteModelIntegrationService {

    /**
     * 获取所有可用的远程聊天模型
     * @return 模型ID到ChatModel的映射
     */
    Map<String, ChatModel> getAvailableRemoteChatModels();

    /**
     * 根据模型ID获取聊天模型
     * @param modelId 模型ID
     * @return ChatModel实例，如果不存在则返回null
     */
    ChatModel getChatModel(String modelId);

    /**
     * 根据模型ID获取流式聊天模型
     * @param modelId 模型ID
     * @return StreamingChatModel实例，如果不存在则返回null
     */
    StreamingChatModel getStreamingChatModel(String modelId);

    /**
     * 刷新远程模型配置
     * 重新加载所有启用的远程模型配置
     */
    void refreshRemoteModels();

    /**
     * 测试远程模型
     * @param modelId 模型ID
     * @param testMessage 测试消息
     * @return 测试结果
     */
    Map<String, Object> testRemoteModel(String modelId, String testMessage);

    /**
     * 检查模型是否可用
     * @param modelId 模型ID
     * @return 是否可用
     */
    boolean isModelAvailable(String modelId);

    /**
     * 获取所有可用的模型ID列表
     * @return 模型ID列表
     */
    List<String> getAvailableModelIds();

    /**
     * 获取所有可用的模型信息（包含名称、描述等）
     * @return 模型信息列表
     */
    List<Map<String, Object>> getAvailableModelInfos();
}
