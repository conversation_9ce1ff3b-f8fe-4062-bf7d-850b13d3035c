package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 远程模型配置实体
 * 用于存储全局的远程模型配置信息（如API密钥等）
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("remote_model_config")
public class RemoteModelConfig {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模型ID（对应RemoteModelInfo中的modelId）
     */
    @TableField("model_id")
    private String modelId;

    /**
     * API密钥（加密存储）
     */
    @TableField("api_key")
    private String apiKey;

    /**
     * 是否启用
     */
    @TableField("enabled")
    private Boolean enabled;

    /**
     * 配置参数（JSON格式）
     * 可存储temperature、max_tokens等参数
     */
    @TableField("config_params")
    private String configParams;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 最后使用时间
     */
    @TableField("last_used_time")
    private LocalDateTime lastUsedTime;

    /**
     * 使用次数统计
     */
    @TableField("usage_count")
    private Long usageCount;

    /**
     * 是否删除（逻辑删除）
     */
    @TableLogic
    @TableField("deleted")
    private Boolean deleted;
}
