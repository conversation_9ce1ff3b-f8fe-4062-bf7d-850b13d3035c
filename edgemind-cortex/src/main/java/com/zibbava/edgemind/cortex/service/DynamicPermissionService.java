package com.zibbava.edgemind.cortex.service;

import com.zibbava.edgemind.cortex.entity.Permission;
import com.zibbava.edgemind.cortex.entity.PermissionTemplate;

import java.util.List;

/**
 * 动态权限服务接口
 * 
 * <AUTHOR>
 */
public interface DynamicPermissionService {
    
    /**
     * 为知识库空间自动生成权限
     * 
     * @param spaceId 知识空间ID
     * @param spaceName 知识空间名称
     * @return 生成的权限列表
     */
    List<Permission> generateKnowledgeSpacePermissions(String spaceId, String spaceName);
    
    /**
     * 删除知识库空间相关权限
     * 
     * @param spaceId 知识空间ID
     */
    void removeKnowledgeSpacePermissions(String spaceId);
    
    /**
     * 获取或创建知识库管理父权限
     * 
     * @return 知识库管理父权限
     */
    Permission getOrCreateKnowledgeParentPermission();
    
    /**
     * 根据资源类型获取权限模板
     * 
     * @param resourceType 资源类型
     * @return 权限模板列表
     */
    List<PermissionTemplate> getPermissionTemplates(String resourceType);
    
    /**
     * 确保所有知识库空间的动态权限已生成
     */
    void ensureDynamicPermissionsGenerated();
}
