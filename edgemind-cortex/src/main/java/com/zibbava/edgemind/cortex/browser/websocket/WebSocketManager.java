package com.zibbava.edgemind.cortex.browser.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zibbava.edgemind.cortex.browser.dto.BrowserToolRequest;
import com.zibbava.edgemind.cortex.browser.dto.BrowserToolResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * WebSocket会话管理器
 * 负责管理浏览器插件的WebSocket连接和工具调用
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class WebSocketManager {
    
    private final ObjectMapper objectMapper;
    
    // 活跃的WebSocket会话
    private final Map<String, WebSocketSession> activeSessions = new ConcurrentHashMap<>();
    
    // 会话信息
    private final Map<String, SessionInfo> sessionInfos = new ConcurrentHashMap<>();
    
    // 待处理的工具请求
    private final Map<String, CompletableFuture<BrowserToolResponse>> pendingRequests = new ConcurrentHashMap<>();
    
    // 请求超时时间（毫秒）
    private static final long DEFAULT_TIMEOUT_MS = 30000; // 30秒
    
    /**
     * 注册WebSocket会话
     */
    public void registerSession(String sessionId, WebSocketSession session) {
        activeSessions.put(sessionId, session);
        sessionInfos.put(sessionId, new SessionInfo(sessionId, System.currentTimeMillis()));
        
        log.info("✅ WebSocket会话已注册: sessionId={}, totalSessions={}", 
                sessionId, activeSessions.size());
    }
    
    /**
     * 注销WebSocket会话
     */
    public void unregisterSession(String sessionId) {
        activeSessions.remove(sessionId);
        sessionInfos.remove(sessionId);
        
        // 清理该会话相关的待处理请求
        cleanupPendingRequests(sessionId);
        
        log.info("🗑️ WebSocket会话已注销: sessionId={}, remainingSessions={}", 
                sessionId, activeSessions.size());
    }
    
    /**
     * 更新会话信息
     */
    public void updateSessionInfo(String sessionId, String clientType, String version, Object capabilities) {
        SessionInfo info = sessionInfos.get(sessionId);
        if (info != null) {
            info.setClientType(clientType);
            info.setVersion(version);
            info.setCapabilities(capabilities);
            info.setLastActivity(System.currentTimeMillis());
            
            log.info("📝 会话信息已更新: sessionId={}, clientType={}, version={}", 
                    sessionId, clientType, version);
        }
    }
    
    /**
     * 更新会话最后活跃时间
     */
    public void updateLastActivity(String sessionId) {
        SessionInfo info = sessionInfos.get(sessionId);
        if (info != null) {
            info.setLastActivity(System.currentTimeMillis());
        }
    }
    
    /**
     * 发送工具请求到浏览器插件
     */
    public Mono<BrowserToolResponse> sendToolRequest(BrowserToolRequest request) {
        return Mono.fromFuture(() -> {
            CompletableFuture<BrowserToolResponse> future = new CompletableFuture<>();
            
            // 检查是否有可用的会话
            if (activeSessions.isEmpty()) {
                future.completeExceptionally(new RuntimeException("没有可用的浏览器插件连接"));
                return future;
            }
            
            // 注册待处理请求
            pendingRequests.put(request.getRequestId(), future);
            
            // 设置超时处理
            CompletableFuture.delayedExecutor(request.getTimeoutMs(), TimeUnit.MILLISECONDS)
                    .execute(() -> {
                        CompletableFuture<BrowserToolResponse> timeoutFuture = 
                                pendingRequests.remove(request.getRequestId());
                        if (timeoutFuture != null && !timeoutFuture.isDone()) {
                            timeoutFuture.complete(BrowserToolResponse.timeout(request.getRequestId()));
                        }
                    });
            
            // 发送请求到所有活跃会话（广播模式）
            broadcastToolRequest(request);
            
            return future;
        });
    }
    
    /**
     * 处理工具响应
     */
    public void handleToolResponse(BrowserToolResponse response) {
        CompletableFuture<BrowserToolResponse> future = pendingRequests.remove(response.getRequestId());
        
        if (future != null) {
            future.complete(response);
            log.debug("✅ 工具响应已处理: requestId={}, success={}", 
                    response.getRequestId(), response.isSuccess());
        } else {
            log.warn("⚠️ 收到未知请求的响应: requestId={}", response.getRequestId());
        }
    }
    
    /**
     * 广播工具请求到所有活跃会话
     */
    private void broadcastToolRequest(BrowserToolRequest request) {
        Map<String, Object> message = Map.of(
            "type", "TOOL_CALL",
            "requestId", request.getRequestId(),
            "toolName", request.getToolName(),
            "arguments", request.getArguments(),
            "timestamp", request.getTimestamp(),
            "waitForNavigation", request.isWaitForNavigation(),
            "targetTabId", request.getTargetTabId() != null ? request.getTargetTabId() : -1
        );
        
        try {
            String messageJson = objectMapper.writeValueAsString(message);
            TextMessage textMessage = new TextMessage(messageJson);
            
            // 发送到所有活跃会话
            activeSessions.values().forEach(session -> {
                try {
                    if (session.isOpen()) {
                        session.sendMessage(textMessage);
                        log.debug("📤 工具请求已发送: sessionId={}, toolName={}", 
                                session.getId(), request.getToolName());
                    }
                } catch (Exception e) {
                    log.error("❌ 发送工具请求失败: sessionId={}, error={}", 
                            session.getId(), e.getMessage());
                }
            });
            
        } catch (Exception e) {
            log.error("❌ 序列化工具请求失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 发送心跳消息
     */
    @Scheduled(fixedRate = 30000) // 每30秒发送一次心跳
    public void sendHeartbeat() {
        if (activeSessions.isEmpty()) {
            return;
        }
        
        Map<String, Object> ping = Map.of(
            "type", "PING",
            "timestamp", System.currentTimeMillis()
        );
        
        try {
            String pingJson = objectMapper.writeValueAsString(ping);
            TextMessage pingMessage = new TextMessage(pingJson);
            
            activeSessions.values().forEach(session -> {
                try {
                    if (session.isOpen()) {
                        session.sendMessage(pingMessage);
                    }
                } catch (Exception e) {
                    log.warn("发送心跳失败: sessionId={}", session.getId());
                }
            });
            
            log.debug("💓 心跳消息已发送到 {} 个会话", activeSessions.size());
            
        } catch (Exception e) {
            log.error("❌ 序列化心跳消息失败: {}", e.getMessage());
        }
    }
    
    /**
     * 清理超时的待处理请求
     */
    @Scheduled(fixedRate = 60000) // 每分钟清理一次
    public void cleanupExpiredRequests() {
        long now = System.currentTimeMillis();
        int cleanedCount = 0;
        
        pendingRequests.entrySet().removeIf(entry -> {
            CompletableFuture<BrowserToolResponse> future = entry.getValue();
            if (future.isDone()) {
                return true;
            }
            
            // 检查是否超时（这里简单处理，实际应该根据请求创建时间判断）
            // 由于我们在sendToolRequest中已经设置了超时处理，这里主要是兜底清理
            return false;
        });
        
        if (cleanedCount > 0) {
            log.info("🧹 清理了 {} 个过期请求", cleanedCount);
        }
    }
    
    /**
     * 清理指定会话的待处理请求
     */
    private void cleanupPendingRequests(String sessionId) {
        // 这里可以根据需要实现更精细的清理逻辑
        // 目前的实现是广播模式，所以不需要按会话清理
    }
    
    /**
     * 获取活跃会话数量
     */
    public int getActiveSessionCount() {
        return activeSessions.size();
    }
    
    /**
     * 获取待处理请求数量
     */
    public int getPendingRequestCount() {
        return pendingRequests.size();
    }
    
    /**
     * 会话信息内部类
     */
    private static class SessionInfo {
        private final String sessionId;
        private final long createdAt;
        private long lastActivity;
        private String clientType;
        private String version;
        private Object capabilities;
        
        public SessionInfo(String sessionId, long createdAt) {
            this.sessionId = sessionId;
            this.createdAt = createdAt;
            this.lastActivity = createdAt;
        }
        
        // Getters and setters
        public String getSessionId() { return sessionId; }
        public long getCreatedAt() { return createdAt; }
        public long getLastActivity() { return lastActivity; }
        public void setLastActivity(long lastActivity) { this.lastActivity = lastActivity; }
        public String getClientType() { return clientType; }
        public void setClientType(String clientType) { this.clientType = clientType; }
        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }
        public Object getCapabilities() { return capabilities; }
        public void setCapabilities(Object capabilities) { this.capabilities = capabilities; }
    }
}
