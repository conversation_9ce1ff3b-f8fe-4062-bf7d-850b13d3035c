package com.zibbava.edgemind.cortex.service.impl;

import com.zibbava.edgemind.cortex.entity.RemoteModelConfig;
import com.zibbava.edgemind.cortex.enums.RemoteModelInfo;
import com.zibbava.edgemind.cortex.service.RemoteModelIntegrationService;
import com.zibbava.edgemind.cortex.service.RemoteModelService;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.openai.OpenAiStreamingChatModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 远程模型集成服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RemoteModelIntegrationServiceImpl implements RemoteModelIntegrationService {

    private final RemoteModelService remoteModelService;
    
    // 缓存已创建的模型实例
    private final Map<String, ChatModel> modelCache = new ConcurrentHashMap<>();
    private final Map<String, StreamingChatModel> streamingModelCache = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void init() {
        log.info("初始化远程模型集成服务");
        try {
            refreshRemoteModels();
        } catch (Exception e) {
            log.warn("远程模型集成服务初始化失败，可能是数据库表未创建: {}", e.getMessage());
            log.info("远程模型集成服务将在表创建后可用");
        }
    }

    @Override
    public Map<String, ChatModel> getAvailableRemoteChatModels() {
        return new HashMap<>(modelCache);
    }

    @Override
    public ChatModel getChatModel(String modelId) {
        return modelCache.get(modelId);
    }

    @Override
    public StreamingChatModel getStreamingChatModel(String modelId) {
        StreamingChatModel model = streamingModelCache.get(modelId);
        log.debug("🔍 查找远程流式模型: modelId={}, 找到={}", modelId, model != null);
        if (model == null) {
            log.debug("🔍 当前可用的远程流式模型: {}", streamingModelCache.keySet());
        }
        return model;
    }

    @Override
    public void refreshRemoteModels() {
        log.info("刷新远程模型配置");
        
        // 清空现有缓存
        modelCache.clear();
        streamingModelCache.clear();
        
        // 获取所有启用的远程模型配置
        List<RemoteModelConfig> enabledConfigs = remoteModelService.getEnabledRemoteModelConfigs();
        
        for (RemoteModelConfig config : enabledConfigs) {
            try {
                ChatModel model = createChatModel(config);
                StreamingChatModel streamingModel = createStreamingChatModel(config);
                if (model != null) {
                    modelCache.put(config.getModelId(), model);
                    log.info("成功加载远程聊天模型: {}", config.getModelId());
                }
                if (streamingModel != null) {
                    streamingModelCache.put(config.getModelId(), streamingModel);
                    log.info("成功加载远程流式模型: {}", config.getModelId());
                }
            } catch (Exception e) {
                log.error("加载远程模型失败: {}", config.getModelId(), e);
            }
        }
        
        log.info("远程模型刷新完成，共加载{}个聊天模型，{}个流式模型", modelCache.size(), streamingModelCache.size());
    }

    @Override
    public Map<String, Object> testRemoteModel(String modelId, String testMessage) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            ChatModel model = getChatModel(modelId);
            if (model == null) {
                result.put("success", false);
                result.put("message", "模型不存在或未启用: " + modelId);
                return result;
            }

            // 发送测试消息
            String response = model.chat(testMessage != null ? testMessage : "Hello");
            
            result.put("success", true);
            result.put("message", "模型测试成功");
            result.put("modelId", modelId);
            result.put("testMessage", testMessage);
            result.put("response", response);
            
        } catch (Exception e) {
            log.error("测试远程模型失败: {}", modelId, e);
            result.put("success", false);
            result.put("message", "模型测试失败: " + e.getMessage());
        }
        
        return result;
    }

    @Override
    public boolean isModelAvailable(String modelId) {
        return modelCache.containsKey(modelId) || streamingModelCache.containsKey(modelId);
    }

    @Override
    public List<String> getAvailableModelIds() {
        return new ArrayList<>(modelCache.keySet());
    }

    @Override
    public List<Map<String, Object>> getAvailableModelInfos() {
        List<Map<String, Object>> modelInfos = new ArrayList<>();
        
        for (String modelId : modelCache.keySet()) {
            RemoteModelInfo modelInfo = RemoteModelInfo.findByModelId(modelId);
            if (modelInfo != null) {
                Map<String, Object> info = new HashMap<>();
                info.put("id", modelId);
                info.put("name", modelInfo.getName());
                info.put("description", modelInfo.getDescription());
                info.put("provider", modelInfo.getProvider());
                info.put("type", "remote");
                info.put("available", true);
                modelInfos.add(info);
            }
        }
        
        return modelInfos;
    }

    /**
     * 根据配置创建StreamingChatModel实例
     */
    private StreamingChatModel createStreamingChatModel(RemoteModelConfig config) {
        RemoteModelInfo modelInfo = RemoteModelInfo.findByModelId(config.getModelId());
        if (modelInfo == null) {
            log.warn("未找到模型信息: {}", config.getModelId());
            return null;
        }

        String provider = modelInfo.getProvider().toLowerCase();

        switch (provider) {
            case "deepseek":
                return createDeepSeekStreamingModel(config, modelInfo);
            default:
                log.warn("不支持的提供商: {}", provider);
                return null;
        }
    }

    /**
     * 根据配置创建ChatModel实例
     */
    private ChatModel createChatModel(RemoteModelConfig config) {
        RemoteModelInfo modelInfo = RemoteModelInfo.findByModelId(config.getModelId());
        if (modelInfo == null) {
            log.warn("未找到模型信息: {}", config.getModelId());
            return null;
        }
        
        String provider = modelInfo.getProvider().toLowerCase();
        
        switch (provider) {
            case "deepseek":
                return createDeepSeekModel(config, modelInfo);
            default:
                log.warn("不支持的提供商: {}", provider);
                return null;
        }
    }

    /**
     * 创建DeepSeek流式模型实例
     */
    private StreamingChatModel createDeepSeekStreamingModel(RemoteModelConfig config, RemoteModelInfo modelInfo) {
        try {
            return OpenAiStreamingChatModel.builder()
                    .baseUrl("https://api.deepseek.com/v1")
                    .apiKey(config.getApiKey())
                    .modelName(config.getModelId())
                    .temperature(0.7)
                    .timeout(Duration.ofSeconds(60))
                    .logRequests(false)
                    .logResponses(false)
                    .build();
        } catch (Exception e) {
            log.error("创建DeepSeek流式模型失败: {}", config.getModelId(), e);
            return null;
        }
    }

    /**
     * 创建DeepSeek模型实例
     */
    private ChatModel createDeepSeekModel(RemoteModelConfig config, RemoteModelInfo modelInfo) {
        try {
            return OpenAiChatModel.builder()
                    .baseUrl("https://api.deepseek.com/v1")
                    .apiKey(config.getApiKey())
                    .modelName(config.getModelId())
                    .temperature(0.7)
                    .timeout(Duration.ofSeconds(60))
                    .maxRetries(3)
                    .logRequests(false)
                    .logResponses(false)
                    .build();
        } catch (Exception e) {
            log.error("创建DeepSeek模型失败: {}", config.getModelId(), e);
            return null;
        }
    }
}
