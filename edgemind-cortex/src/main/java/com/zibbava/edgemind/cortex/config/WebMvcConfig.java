package com.zibbava.edgemind.cortex.config;

import com.zibbava.edgemind.cortex.interceptor.LicenseCheckInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@RequiredArgsConstructor
public class WebMvcConfig implements WebMvcConfigurer {

    private final LicenseCheckInterceptor licenseCheckInterceptor;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 映射 /wkg/css/** 到 classpath:/static/css/
        registry.addResourceHandler("/wkg/css/**")
                .addResourceLocations("classpath:/static/css/");

        // 映射 /wkg/fonts/** 到 classpath:/static/fonts/
        registry.addResourceHandler("/wkg/fonts/**")
                .addResourceLocations("classpath:/static/fonts/");

        // 映射 /wkg/js/** 到 classpath:/static/js/
        registry.addResourceHandler("/wkg/js/**")
                .addResourceLocations("classpath:/static/js/");

        // 映射 /wkg/images/** 到 classpath:/static/images/ (如果需要)
        registry.addResourceHandler("/wkg/images/**")
                .addResourceLocations("classpath:/static/images/");

        // 保留 /static/** 映射（可选，但可能有用）
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册授权检查拦截器
        registry.addInterceptor(licenseCheckInterceptor)
                .addPathPatterns("/**") // 拦截所有路径
                .excludePathPatterns(
                        "/error/**", // 排除错误页面
                        "/auth/**", // 排除认证相关路径
                        "/login", // 排除登录页面
                        "/css/**", "/js/**", "/images/**", "/fonts/**", // 排除静态资源
                        "/static/**" // 排除静态资源
                )
                .order(2); // 设置拦截器顺序，在Sa-Token拦截器之后执行
    }
}