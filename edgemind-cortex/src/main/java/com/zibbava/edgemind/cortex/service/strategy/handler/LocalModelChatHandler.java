package com.zibbava.edgemind.cortex.service.strategy.handler;

import com.zibbava.edgemind.cortex.browser.tools.BrowserToolProvider;
import com.zibbava.edgemind.cortex.dto.ChatContext;
import com.zibbava.edgemind.cortex.dto.ollama.OllamaChatRequest;
import com.zibbava.edgemind.cortex.dto.ollama.OllamaMessage;
import com.zibbava.edgemind.cortex.enums.ModelInfo;
import com.zibbava.edgemind.cortex.service.ChatService;
import com.zibbava.edgemind.cortex.util.OllamaUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.SynchronousSink;

import java.util.ArrayList;
import java.util.List;

/**
 * 本地模型聊天处理器
 * 负责处理本地Ollama模型的聊天请求，支持工具调用功能
 * 支持切换使用原有OllamaUtils或新的LangChain4j实现
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class LocalModelChatHandler {

    private final ChatService chatService;
    private final BrowserToolProvider browserToolProvider;
    private final LangChain4jChatHandler langChain4jChatHandler;

    @Value("${chat.thinking.enabled:true}")
    private boolean enableThinking;

    @Value("${chat.use-langchain4j:true}")
    private boolean useLangChain4j;

    /**
     * 执行本地模型聊天
     * @param context 聊天上下文
     * @return 聊天结果流
     */
    public Flux<String> executeChat(ChatContext context) {
        try {
            log.info("🏠 开始执行本地模型对话: model={}, toolCalling={}, useLangChain4j={}",
                    context.getModelName(), context.getEnableToolCalling(), useLangChain4j);

            // 根据配置选择使用LangChain4j或原有OllamaUtils实现
            if (useLangChain4j) {
                log.info("🔗 使用LangChain4j实现处理本地模型对话");
                return langChain4jChatHandler.executeChat(context);
            } else {
                log.info("🔧 使用原有OllamaUtils实现处理本地模型对话");
                return executeWithOllamaUtils(context);
            }
        } catch (Exception e) {
            BrowserToolProvider.clearToolCallingState(); // 清理ThreadLocal状态
            log.error("❌ 执行本地模型对话出错: {}", e.getMessage(), e);
            return Flux.error(e);
        }
    }

    /**
     * 使用原有OllamaUtils实现执行聊天
     * 保留原有功能，作为备用方案
     */
    private Flux<String> executeWithOllamaUtils(ChatContext context) {
        try {
            // 设置工具调用状态到ThreadLocal
            BrowserToolProvider.setToolCallingEnabled(
                context.getEnableToolCalling() != null && context.getEnableToolCalling()
            );

            // 构建消息历史列表
            List<OllamaMessage> messages = buildMessageHistory(context);

            // 创建OllamaChatRequest
            OllamaChatRequest chatRequest = OllamaChatRequest.builder()
                    .model(context.getModelName())
                    .messages(messages)
                    .stream(true)
                    .sessionId("chat_" + context.getUserId() + "_" + context.getConversationId())
                    .build();

            // 如果启用工具调用，添加工具提供者
            if (context.getEnableToolCalling() != null && context.getEnableToolCalling()) {
                log.info("🔧 本地模型启用工具调用");
                chatRequest.setToolProvider(browserToolProvider);
            }

            // 设置深度思考功能
            boolean shouldEnableThinking = shouldEnableThinking(context);
            chatRequest.enableThinking(shouldEnableThinking);

            // 使用OllamaUtils执行流式聊天
            return OllamaUtils.streamChat(chatRequest)
                    .handle((String rawToken, SynchronousSink<String> sink) -> chatService.handelSSE(
                            context.getMainContentBuilder(),
                            context.getThinkingContentBuilder(),
                            rawToken,
                            sink,
                            context.getIsInsideThinkingBlock()
                    ))
                    .doOnComplete(() -> {
                        updateConversationWithQuality(context);
                        BrowserToolProvider.clearToolCallingState(); // 清理ThreadLocal状态
                        log.info("✅ 本地模型对话完成: conversationId={}", context.getConversationId());
                    })
                    .doOnError(e -> {
                        BrowserToolProvider.clearToolCallingState(); // 清理ThreadLocal状态
                        log.error("❌ 本地模型对话出错: {}", e.getMessage(), e);
                    });
        } catch (Exception e) {
            BrowserToolProvider.clearToolCallingState(); // 清理ThreadLocal状态
            log.error("❌ 执行OllamaUtils本地模型对话出错: {}", e.getMessage(), e);
            return Flux.error(e);
        }
    }

    /**
     * 构建消息历史
     */
    private List<OllamaMessage> buildMessageHistory(ChatContext context) {
        List<OllamaMessage> messages = new ArrayList<>();

        try {
            // 添加系统消息（如果需要的话，可以从配置或其他地方获取）
            // 暂时不添加系统消息，直接使用历史消息和当前消息

            // 添加历史消息（从数据库加载）
            if (context.getConversationId() != null) {
                List<com.zibbava.edgemind.cortex.entity.ChatMessage> historyMessages =
                    chatService.getRecentConversationMessages(context.getConversationId(), 10);

                if (!historyMessages.isEmpty()) {
                    log.info("📚 加载历史消息: conversationId={}, 消息数量={}",
                            context.getConversationId(), historyMessages.size());

                    // 转换历史消息为OllamaMessage格式
                    for (com.zibbava.edgemind.cortex.entity.ChatMessage msg : historyMessages) {
                        if ("user".equals(msg.getSender())) {
                            messages.add(OllamaMessage.user(msg.getContent()));
                        } else if ("assistant".equals(msg.getSender()) || "ai".equals(msg.getSender())) {
                            messages.add(OllamaMessage.assistant(msg.getContent()));
                        }
                    }
                }
            }

            // 添加当前用户消息
            messages.add(OllamaMessage.user(context.getPrompt()));

            log.info("💬 构建本地模型消息历史完成: 总消息数={}, 历史消息数={}",
                    messages.size(), messages.size() - 1);

        } catch (Exception e) {
            log.warn("⚠️ 加载历史消息失败，使用当前消息: {}", e.getMessage());
            // 如果加载历史失败，只使用当前消息
            messages.clear();
            messages.add(OllamaMessage.user(context.getPrompt()));
        }

        return messages;
    }

    /**
     * 判断是否应该启用深度思考
     */
    private boolean shouldEnableThinking(ChatContext context) {
        if (!ModelInfo.supportsThinking(context.getModelName())) {
            log.info("🧠 深度思考功能: 关闭 (模型 {} 不支持think功能)", context.getModelName());
            return false;
        }

        boolean shouldEnable;
        if (context.getEnableThinking() != null) {
            shouldEnable = context.getEnableThinking();
            log.info("🧠 深度思考功能: {} (用户设置, 模型: {})", shouldEnable ? "开启" : "关闭", context.getModelName());
        } else {
            shouldEnable = enableThinking;
            log.info("🧠 深度思考功能: {} (使用默认配置, 模型: {})", shouldEnable ? "开启" : "关闭", context.getModelName());
        }
        
        return shouldEnable;
    }

    /**
     * 更新会话并进行质量评估
     */
    private void updateConversationWithQuality(ChatContext context) {
        try {
            // 基本的会话更新
            updateConversation(context);
        } catch (Exception e) {
            log.warn("⚠️ 会话质量评估失败: {}", e.getMessage());
        }
    }

    /**
     * 更新会话
     */
    private void updateConversation(ChatContext context) {
        // 如果需要，可以在这里添加更新会话的逻辑
        // 例如更新最后活动时间、会话质量评分等
        log.debug("🔄 会话 {} 状态更新完成", context.getConversationId());
    }
}
