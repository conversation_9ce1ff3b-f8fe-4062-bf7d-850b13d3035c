package com.zibbava.edgemind.cortex.store;

import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.filter.Filter;
import dev.langchain4j.store.embedding.filter.comparison.IsEqualTo;
import dev.langchain4j.store.embedding.filter.comparison.IsIn;
import dev.langchain4j.store.embedding.filter.comparison.IsNotEqualTo;
import dev.langchain4j.store.embedding.filter.comparison.ContainsString;
import dev.langchain4j.store.embedding.filter.logical.And;
import dev.langchain4j.store.embedding.filter.logical.Not;
import dev.langchain4j.store.embedding.filter.logical.Or;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.cn.smart.SmartChineseAnalyzer;
import org.apache.lucene.document.*;
import org.apache.lucene.index.*;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.lucene.queryparser.classic.ParseException;
import org.apache.lucene.search.*;
import org.apache.lucene.search.BooleanClause;

import java.util.Collection;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.FSDirectory;
import org.apache.lucene.util.BytesRef;
import org.apache.lucene.index.VectorSimilarityFunction;
import org.springframework.beans.factory.DisposableBean;

import jakarta.annotation.PreDestroy;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 企业级 Apache Lucene 9.x 向量存储实现
 * <p>
 * 🚀 核心特性：
 * - 基于Apache Lucene 9.x，完全嵌入式部署
 * - 支持向量检索和混合检索（向量+关键词）
 * - 优化的中文分词和搜索支持
 * - 高性能的HNSW向量索引
 * - 完善的条件过滤查询
 * - 数据持久化存储
 * - Spring Boot 生命周期管理
 * <p>
 * 🎯 RAG 优化：
 * - 针对文档片段的快速检索和精确过滤
 * - 支持复杂元数据查询和混合搜索场景
 * - 使用SmartChineseAnalyzer优化中文搜索质量
 * - 内存友好的大规模数据处理
 * <p>
 * 🔧 生命周期管理：
 * - 实现 DisposableBean 接口，确保 Spring 容器关闭时正确释放资源
 * - 使用 @PreDestroy 注解，提供双重保障的资源清理机制
 * - 线程安全的关闭状态管理，避免重复关闭操作
 */
@Slf4j
public class LuceneEmbeddingStore implements EmbeddingStore<TextSegment>, DisposableBean {

    // --- Lucene 核心组件 ---
    private final Directory directory;
    private final Analyzer analyzer;
    private IndexWriter indexWriter;
    private SearcherManager searcherManager;

    // --- 配置参数 ---
    private final String indexPath;
    private final Integer dimension;
    private final Integer batchSize;
    private final boolean enableHybridSearch;
    private final float hybridAlpha;

    // --- 状态管理 ---
    private final AtomicBoolean isInitialized = new AtomicBoolean(false);
    private final AtomicBoolean isClosed = new AtomicBoolean(false);

    // --- 字段名称常量 ---
    private static final String ID_FIELD = "id";
    private static final String VECTOR_FIELD = "vector";
    private static final String TEXT_FIELD = "text";
    private static final String NODE_ID_FIELD = "node_id";
    private static final String SPACE_ID_FIELD = "space_id";
    private static final String FILE_NAME_FIELD = "file_name";
    private static final String USER_ID_FIELD = "user_id";
    private static final String MIME_TYPE_FIELD = "mime_type";
    private static final String TITLE_FIELD = "title";
    private static final String NODE_PATH_FIELD = "node_path";
    private static final String CREATED_TIME_FIELD = "created_time";
    private static final String UPDATED_TIME_FIELD = "updated_time";
    private static final String CHUNK_INDEX_FIELD = "chunk_index";
    private static final String CHUNK_SIZE_FIELD = "chunk_size";
    private static final String DOCUMENT_ID_FIELD = "document_id";

    // --- 默认配置 ---
    private static final int DEFAULT_BATCH_SIZE = 100;
    private static final int MAX_BATCH_SIZE = 1000;

    /**
     * 构造函数
     */
    public LuceneEmbeddingStore(String indexPath, Integer dimension, Integer batchSize,
                               boolean enableHybridSearch, float hybridAlpha) {
        this.indexPath = indexPath != null ? indexPath : "./data/lucene-index";
        this.dimension = dimension != null ? dimension : 1024;
        this.batchSize = batchSize != null ? Math.min(batchSize, MAX_BATCH_SIZE) : DEFAULT_BATCH_SIZE;
        this.enableHybridSearch = enableHybridSearch;
        this.hybridAlpha = hybridAlpha;

        try {
            // 初始化Lucene组件
            Path indexDir = Paths.get(this.indexPath);
            this.directory = FSDirectory.open(indexDir);
            this.analyzer = new SmartChineseAnalyzer();

            // 初始化索引
            initializeIndex();
            
            log.info("✅ Lucene向量存储初始化成功");
            log.info("📁 索引路径: {}", this.indexPath);
            log.info("📊 向量维度: {}", this.dimension);
            log.info("📦 批处理大小: {}", this.batchSize);
            log.info("🔍 混合搜索: {}", this.enableHybridSearch ? "启用" : "禁用");
            log.info("⚖️ 混合搜索权重: {}", this.hybridAlpha);

        } catch (Exception e) {
            log.error("❌ 初始化Lucene向量存储失败", e);
            throw new RuntimeException("初始化Lucene向量存储失败", e);
        }
    }

    /**
     * 初始化Lucene索引
     */
    private void initializeIndex() throws IOException {
        // 配置索引写入器
        IndexWriterConfig config = new IndexWriterConfig(analyzer);
        config.setOpenMode(IndexWriterConfig.OpenMode.CREATE_OR_APPEND);
        config.setRAMBufferSizeMB(256.0); // 设置RAM缓冲区大小
        config.setMaxBufferedDocs(1000); // 设置最大缓冲文档数

        this.indexWriter = new IndexWriter(directory, config);
        
        // 初始化搜索管理器
        this.searcherManager = new SearcherManager(indexWriter, null);
        
        isInitialized.set(true);
    }

    /**
     * 确保存储可用
     */
    private void ensureStoreReady() {
        if (isClosed.get()) {
            throw new IllegalStateException("Lucene向量存储已关闭");
        }
        if (!isInitialized.get()) {
            throw new IllegalStateException("Lucene向量存储未初始化");
        }
    }

    @Override
    public String add(Embedding embedding) {
        return add(embedding, null);
    }

    @Override
    public void add(String id, Embedding embedding) {
        add(id, embedding, null);
    }

    @Override
    public String add(Embedding embedding, TextSegment textSegment) {
        String id = UUID.randomUUID().toString();
        add(id, embedding, textSegment);
        return id;
    }

    public void add(String id, Embedding embedding, TextSegment textSegment) {
        ensureStoreReady();

        try {
            // 创建Lucene文档
            Document doc = createLuceneDocument(id, embedding, textSegment);

            // 添加到索引
            indexWriter.addDocument(doc);

            // 提交索引并刷新搜索管理器
            indexWriter.commit();
            searcherManager.maybeRefresh();

            log.debug("✅ 文档添加成功: {}", id);

        } catch (Exception e) {
            log.error("❌ 添加文档失败: {}", e.getMessage(), e);
            throw new RuntimeException("添加文档失败", e);
        }
    }

    /**
     * 创建Lucene文档
     */
    private Document createLuceneDocument(String id, Embedding embedding, TextSegment textSegment) {
        Document doc = new Document();
        
        // 添加ID字段
        doc.add(new StringField(ID_FIELD, id, Field.Store.YES));
        
        // 添加向量字段
        List<Float> vectorList = embedding.vectorAsList();
        float[] vectorArray = new float[vectorList.size()];
        for (int i = 0; i < vectorList.size(); i++) {
            vectorArray[i] = vectorList.get(i);
        }
        
        // 使用KnnFloatVectorField存储向量（Lucene 9.x支持）
        doc.add(new KnnFloatVectorField(VECTOR_FIELD, vectorArray, VectorSimilarityFunction.COSINE));
        
        // 添加文本内容
        String text = textSegment != null ? textSegment.text() : "";
        doc.add(new TextField(TEXT_FIELD, text, Field.Store.YES));
        
        // 添加元数据字段
        if (textSegment != null && textSegment.metadata() != null) {
            addMetadataFields(doc, textSegment.metadata());
        }
        
        return doc;
    }

    /**
     * 添加元数据字段到Lucene文档
     */
    private void addMetadataFields(Document doc, dev.langchain4j.data.document.Metadata metadata) {
        // 添加各种元数据字段
        addStringField(doc, NODE_ID_FIELD, metadata.getString(NODE_ID_FIELD));
        addStringField(doc, SPACE_ID_FIELD, metadata.getString(SPACE_ID_FIELD));
        addStringField(doc, FILE_NAME_FIELD, metadata.getString(FILE_NAME_FIELD));
        addStringField(doc, USER_ID_FIELD, metadata.getString(USER_ID_FIELD));
        addStringField(doc, MIME_TYPE_FIELD, metadata.getString(MIME_TYPE_FIELD));
        addStringField(doc, TITLE_FIELD, metadata.getString(TITLE_FIELD));
        addStringField(doc, NODE_PATH_FIELD, metadata.getString(NODE_PATH_FIELD));
        addStringField(doc, CREATED_TIME_FIELD, metadata.getString(CREATED_TIME_FIELD));
        addStringField(doc, UPDATED_TIME_FIELD, metadata.getString(UPDATED_TIME_FIELD));
        addStringField(doc, CHUNK_INDEX_FIELD, metadata.getString(CHUNK_INDEX_FIELD));
        addStringField(doc, CHUNK_SIZE_FIELD, metadata.getString(CHUNK_SIZE_FIELD));
        addStringField(doc, DOCUMENT_ID_FIELD, metadata.getString(DOCUMENT_ID_FIELD));
    }

    /**
     * 添加字符串字段到文档
     */
    private void addStringField(Document doc, String fieldName, String value) {
        if (value != null && !value.isEmpty()) {
            doc.add(new StringField(fieldName, value, Field.Store.YES));
            // 同时添加为可搜索的文本字段
            doc.add(new TextField(fieldName + "_text", value, Field.Store.NO));
        }
    }

    @Override
    public List<String> addAll(List<Embedding> embeddings) {
        return addAll(embeddings, null);
    }

    @Override
    public List<String> addAll(List<Embedding> embeddings, List<TextSegment> textSegments) {
        ensureStoreReady();

        try {
            log.info("📦 批量添加文档到Lucene，数量: {}", embeddings.size());

            List<String> ids = new ArrayList<>();

            // 分批处理，避免内存溢出
            for (int i = 0; i < embeddings.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, embeddings.size());
                List<Embedding> batchEmbeddings = embeddings.subList(i, endIndex);
                List<TextSegment> batchSegments = textSegments != null ?
                    textSegments.subList(i, Math.min(endIndex, textSegments.size())) : null;

                List<String> batchIds = processBatch(batchEmbeddings, batchSegments);
                ids.addAll(batchIds);
            }

            // 提交更改
            indexWriter.commit();
            searcherManager.maybeRefresh();

            log.info("✅ 批量添加文档成功，数量: {}", ids.size());
            return ids;

        } catch (Exception e) {
            log.error("❌ 批量添加文档失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量添加文档失败", e);
        }
    }

    /**
     * 处理单个批次
     */
    private List<String> processBatch(List<Embedding> embeddings, List<TextSegment> textSegments) throws IOException {
        List<String> batchIds = new ArrayList<>();

        for (int i = 0; i < embeddings.size(); i++) {
            String id = UUID.randomUUID().toString();
            batchIds.add(id);

            TextSegment segment = textSegments != null && i < textSegments.size() ?
                textSegments.get(i) : null;

            // 创建并添加文档
            Document doc = createLuceneDocument(id, embeddings.get(i), segment);
            indexWriter.addDocument(doc);
        }

        return batchIds;
    }

    @Override
    public EmbeddingSearchResult<TextSegment> search(EmbeddingSearchRequest request) {
        ensureStoreReady();

        if (enableHybridSearch) {
            // 如果启用混合搜索但没有查询文本，回退到向量搜索
            return performVectorSearch(request);
        } else {
            return performVectorSearch(request);
        }
    }

    /**
     * 执行向量搜索
     */
    private EmbeddingSearchResult<TextSegment> performVectorSearch(EmbeddingSearchRequest request) {
        try {
            log.debug("🔍 执行Lucene向量搜索，maxResults: {}, minScore: {}",
                request.maxResults(), request.minScore());

            // 获取搜索器
            IndexSearcher searcher = searcherManager.acquire();
            
            try {
                // 准备查询向量
                List<Float> queryVectorList = request.queryEmbedding().vectorAsList();
                float[] queryVector = new float[queryVectorList.size()];
                for (int i = 0; i < queryVectorList.size(); i++) {
                    queryVector[i] = queryVectorList.get(i);
                }

                // 创建KNN查询
                KnnFloatVectorQuery vectorQuery = new KnnFloatVectorQuery(VECTOR_FIELD, queryVector, request.maxResults());

                // 应用过滤器
                TopDocs topDocs;
                if (request.filter() != null) {
                    Query filterQuery = convertFilterToQuery(request.filter());
                    BooleanQuery.Builder builder = new BooleanQuery.Builder();
                    builder.add(vectorQuery, BooleanClause.Occur.MUST);
                    builder.add(filterQuery, BooleanClause.Occur.FILTER);
                    topDocs = searcher.search(builder.build(), request.maxResults());
                } else {
                    topDocs = searcher.search(vectorQuery, request.maxResults());
                }

                log.debug("🔍 Lucene向量搜索原始结果: {} 个文档, 最高分数: {}",
                    topDocs.scoreDocs.length,
                    topDocs.scoreDocs.length > 0 ? topDocs.scoreDocs[0].score : "N/A");

                // 转换结果
                List<EmbeddingMatch<TextSegment>> matches = convertToEmbeddingMatches(searcher, topDocs, request.minScore());

                log.debug("✅ Lucene向量搜索完成，返回 {} 个结果", matches.size());
                return new EmbeddingSearchResult<>(matches);

            } finally {
                searcherManager.release(searcher);
            }

        } catch (Exception e) {
            log.error("❌ Lucene向量搜索失败: {}", e.getMessage(), e);
            return new EmbeddingSearchResult<>(Collections.emptyList());
        }
    }

    /**
     * 混合搜索方法 - 结合向量搜索和关键词搜索
     */
    public EmbeddingSearchResult<TextSegment> hybridSearch(String queryText, Embedding queryEmbedding,
                                                           int maxResults, double minScore, Filter filter) {
        ensureStoreReady();

        try {
            log.debug("🔀 执行Lucene混合搜索: queryText={}, maxResults={}, minScore={}",
                queryText, maxResults, minScore);

            if (!enableHybridSearch) {
                log.debug("混合搜索未启用，回退到向量搜索");
                EmbeddingSearchRequest request = EmbeddingSearchRequest.builder()
                    .queryEmbedding(queryEmbedding)
                    .maxResults(maxResults)
                    .minScore(minScore)
                    .filter(filter)
                    .build();
                return performVectorSearch(request);
            }

            // 获取搜索器
            IndexSearcher searcher = searcherManager.acquire();

            try {
                // 1. 执行向量搜索
                List<Float> queryVectorList = queryEmbedding.vectorAsList();
                float[] queryVector = new float[queryVectorList.size()];
                for (int i = 0; i < queryVectorList.size(); i++) {
                    queryVector[i] = queryVectorList.get(i);
                }

                // 创建基础查询
                KnnFloatVectorQuery vectorQuery = new KnnFloatVectorQuery(VECTOR_FIELD, queryVector, maxResults * 2);
                Query textQuery = createTextQuery(queryText);

                // 应用过滤器
                Query filterQuery = null;
                if (filter != null) {
                    filterQuery = convertFilterToQuery(filter);
                }

                // 执行向量搜索
                TopDocs vectorResults;
                if (filterQuery != null) {
                    BooleanQuery.Builder vectorBuilder = new BooleanQuery.Builder();
                    vectorBuilder.add(vectorQuery, BooleanClause.Occur.MUST);
                    vectorBuilder.add(filterQuery, BooleanClause.Occur.FILTER);
                    vectorResults = searcher.search(vectorBuilder.build(), maxResults * 2);
                } else {
                    vectorResults = searcher.search(vectorQuery, maxResults * 2);
                }

                // 执行文本搜索
                TopDocs textResults;
                if (filterQuery != null) {
                    BooleanQuery.Builder textBuilder = new BooleanQuery.Builder();
                    textBuilder.add(textQuery, BooleanClause.Occur.MUST);
                    textBuilder.add(filterQuery, BooleanClause.Occur.FILTER);
                    textResults = searcher.search(textBuilder.build(), maxResults * 2);
                } else {
                    textResults = searcher.search(textQuery, maxResults * 2);
                }

                // 3. 合并和重排序结果 - 使用改进的分数计算
                Map<String, ScoredDocument> combinedResults = combineSearchResults(
                    searcher, vectorResults, textResults, hybridAlpha);

                // 4. 智能分数过滤逻辑
                boolean hasVectorResults = vectorResults.scoreDocs.length > 0;
                boolean hasTextResults = textResults.scoreDocs.length > 0;

                log.debug("🔍 搜索结果统计: 向量结果={}, 文本结果={}", hasVectorResults, hasTextResults);

                List<EmbeddingMatch<TextSegment>> finalResults;

                if (!hasVectorResults && hasTextResults) {
                    // 如果向量检索没有数据但精准匹配有数据，直接取精准匹配的数据，不需要判断分数
                    log.debug("📝 向量检索无结果，直接使用精准匹配结果，跳过分数过滤");
                    finalResults = combinedResults.values().stream()
                        .sorted((a, b) -> Double.compare(b.score, a.score))
                        .limit(maxResults)
                        .map(scoredDoc -> convertDocumentToEmbeddingMatch(scoredDoc.document, scoredDoc.score))
                        .collect(Collectors.toList());
                } else {
                    // 如果向量检索有数据，或者两种检索都有数据，则使用分数过滤
                    log.debug("🔢 向量检索有结果，使用分数过滤: minScore={}", minScore);
                    finalResults = combinedResults.values().stream()
                        .filter(scoredDoc -> scoredDoc.score >= minScore)
                        .sorted((a, b) -> Double.compare(b.score, a.score))
                        .limit(maxResults)
                        .map(scoredDoc -> convertDocumentToEmbeddingMatch(scoredDoc.document, scoredDoc.score))
                        .collect(Collectors.toList());
                }

                log.debug("✅ Lucene混合搜索完成，返回 {} 个结果", finalResults.size());
                return new EmbeddingSearchResult<>(finalResults);

            } finally {
                searcherManager.release(searcher);
            }

        } catch (Exception e) {
            log.error("❌ Lucene混合搜索失败: {}", e.getMessage(), e);
            return new EmbeddingSearchResult<>(Collections.emptyList());
        }
    }

    /**
     * 创建文本查询
     */
    private Query createTextQuery(String queryText) throws Exception {
        if (queryText == null || queryText.trim().isEmpty()) {
            return new MatchAllDocsQuery();
        }

        // 使用SmartChineseAnalyzer解析查询，支持中文分词
        QueryParser parser = new QueryParser(TEXT_FIELD, analyzer);
        return parser.parse(QueryParser.escape(queryText));
    }

    /**
     * 转换TopDocs为EmbeddingMatch列表
     */
    private List<EmbeddingMatch<TextSegment>> convertToEmbeddingMatches(IndexSearcher searcher, TopDocs topDocs, double minScore) throws IOException {
        List<EmbeddingMatch<TextSegment>> matches = new ArrayList<>();

        for (ScoreDoc scoreDoc : topDocs.scoreDocs) {
            if (scoreDoc.score >= minScore) {
                Document doc = searcher.doc(scoreDoc.doc);
                EmbeddingMatch<TextSegment> match = convertDocumentToEmbeddingMatch(doc, scoreDoc.score);
                matches.add(match);
            }
        }

        return matches;
    }

    /**
     * 转换Lucene文档为EmbeddingMatch
     */
    private EmbeddingMatch<TextSegment> convertDocumentToEmbeddingMatch(Document doc, double score) {
        String id = doc.get(ID_FIELD);
        String text = doc.get(TEXT_FIELD);

        // 重建元数据（确保所有字段都不为null）
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(NODE_ID_FIELD, getFieldValue(doc, NODE_ID_FIELD, ""));
        metadata.put(SPACE_ID_FIELD, getFieldValue(doc, SPACE_ID_FIELD, ""));
        metadata.put(FILE_NAME_FIELD, getFieldValue(doc, FILE_NAME_FIELD, ""));
        metadata.put(USER_ID_FIELD, getFieldValue(doc, USER_ID_FIELD, ""));
        metadata.put(MIME_TYPE_FIELD, getFieldValue(doc, MIME_TYPE_FIELD, ""));
        metadata.put(TITLE_FIELD, getFieldValue(doc, TITLE_FIELD, ""));
        metadata.put(NODE_PATH_FIELD, getFieldValue(doc, NODE_PATH_FIELD, ""));
        metadata.put(CREATED_TIME_FIELD, getFieldValue(doc, CREATED_TIME_FIELD, ""));
        metadata.put(UPDATED_TIME_FIELD, getFieldValue(doc, UPDATED_TIME_FIELD, ""));
        metadata.put(CHUNK_INDEX_FIELD, getFieldValue(doc, CHUNK_INDEX_FIELD, "0"));
        metadata.put(CHUNK_SIZE_FIELD, getFieldValue(doc, CHUNK_SIZE_FIELD, "0"));
        metadata.put(DOCUMENT_ID_FIELD, getFieldValue(doc, DOCUMENT_ID_FIELD, ""));

        // 创建TextSegment
        TextSegment segment = TextSegment.from(text, dev.langchain4j.data.document.Metadata.from(metadata));

        // 重建Embedding（这里使用空的embedding，实际应用中可能需要重新计算）
        Embedding embedding = Embedding.from(Collections.nCopies(dimension, 0.0f));

        return new EmbeddingMatch<>(score, id, embedding, segment);
    }

    /**
     * 安全获取文档字段值，如果为null则返回默认值
     */
    private String getFieldValue(Document doc, String fieldName, String defaultValue) {
        String value = doc.get(fieldName);
        return value != null ? value : defaultValue;
    }

    /**
     * 删除指定ID的文档
     */
    public void remove(String id) {
        ensureStoreReady();

        try {
            Term term = new Term(ID_FIELD, id);
            indexWriter.deleteDocuments(term);
            indexWriter.commit();
            searcherManager.maybeRefresh();

            log.debug("✅ 文档删除成功: {}", id);

        } catch (Exception e) {
            log.error("❌ 删除文档失败: {}", e.getMessage(), e);
            throw new RuntimeException("删除文档失败", e);
        }
    }

    /**
     * 根据条件删除文档
     */
    public void removeByMetadata(String field, String value) {
        ensureStoreReady();

        try {
            Term term = new Term(field, value);
            indexWriter.deleteDocuments(term);
            indexWriter.commit();
            searcherManager.maybeRefresh();

            log.debug("✅ 根据条件删除文档成功: {}={}", field, value);

        } catch (Exception e) {
            log.error("❌ 根据条件删除文档失败: {}", e.getMessage(), e);
            throw new RuntimeException("根据条件删除文档失败", e);
        }
    }

    /**
     * 清空所有文档
     */
    public void removeAll() {
        ensureStoreReady();

        try {
            indexWriter.deleteAll();
            indexWriter.commit();
            searcherManager.maybeRefresh();

            log.info("✅ 清空所有文档成功");

        } catch (Exception e) {
            log.error("❌ 清空所有文档失败: {}", e.getMessage(), e);
            throw new RuntimeException("清空所有文档失败", e);
        }
    }

    /**
     * 合并向量搜索和文本搜索结果，使用改进的分数计算方法
     */
    private Map<String, ScoredDocument> combineSearchResults(IndexSearcher searcher,
                                                           TopDocs vectorResults,
                                                           TopDocs textResults,
                                                           float hybridAlpha) throws IOException {
        Map<String, ScoredDocument> combinedResults = new HashMap<>();

        // 计算分数归一化参数
        double maxVectorScore = getMaxScore(vectorResults);
        double maxTextScore = getMaxScore(textResults);

        log.debug("🔢 分数归一化参数: maxVectorScore={:.4f}, maxTextScore={:.4f}",
            maxVectorScore, maxTextScore);

        // 添加向量搜索结果（归一化后加权）
        for (ScoreDoc scoreDoc : vectorResults.scoreDocs) {
            Document doc = searcher.doc(scoreDoc.doc);
            String id = doc.get(ID_FIELD);

            // 归一化向量分数到[0,1]范围
            double normalizedVectorScore = maxVectorScore > 0 ? scoreDoc.score / maxVectorScore : 0.0;
            double weightedVectorScore = normalizedVectorScore * hybridAlpha;

            combinedResults.put(id, new ScoredDocument(scoreDoc.doc, weightedVectorScore, doc,
                normalizedVectorScore, 0.0));
        }

        // 添加文本搜索结果（归一化后加权）
        for (ScoreDoc scoreDoc : textResults.scoreDocs) {
            Document doc = searcher.doc(scoreDoc.doc);
            String id = doc.get(ID_FIELD);

            // 归一化文本分数到[0,1]范围
            double normalizedTextScore = maxTextScore > 0 ? scoreDoc.score / maxTextScore : 0.0;
            double weightedTextScore = normalizedTextScore * (1.0 - hybridAlpha);

            if (combinedResults.containsKey(id)) {
                // 合并分数 - 使用加权平均
                ScoredDocument existing = combinedResults.get(id);
                double combinedScore = existing.score + weightedTextScore;

                // 使用RRF (Reciprocal Rank Fusion) 进一步优化分数
                double rrfScore = calculateRRFScore(existing.vectorScore, normalizedTextScore);
                double finalScore = 0.8 * combinedScore + 0.2 * rrfScore; // 混合RRF和加权分数

                combinedResults.put(id, new ScoredDocument(existing.docId, finalScore, existing.document,
                    existing.vectorScore, normalizedTextScore));
            } else {
                combinedResults.put(id, new ScoredDocument(scoreDoc.doc, weightedTextScore, doc,
                    0.0, normalizedTextScore));
            }
        }

        return combinedResults;
    }

    /**
     * 获取TopDocs中的最高分数
     */
    private double getMaxScore(TopDocs topDocs) {
        if (topDocs.scoreDocs.length == 0) {
            return 0.0;
        }
        return topDocs.scoreDocs[0].score;
    }

    /**
     * 计算RRF (Reciprocal Rank Fusion) 分数
     * RRF是一种有效的多源搜索结果融合方法
     */
    private double calculateRRFScore(double vectorScore, double textScore) {
        // RRF公式: 1/(k + rank)，这里我们使用分数的倒数作为rank的近似
        double k = 60.0; // RRF常数，通常设为60

        double vectorRRF = vectorScore > 0 ? 1.0 / (k + (1.0 / vectorScore)) : 0.0;
        double textRRF = textScore > 0 ? 1.0 / (k + (1.0 / textScore)) : 0.0;

        return vectorRRF + textRRF;
    }

    /**
     * 评分文档内部类 - 增强版，包含详细的分数信息
     */
    private static class ScoredDocument {
        final int docId;
        final double score;
        final Document document;
        final double vectorScore;  // 归一化后的向量分数
        final double textScore;    // 归一化后的文本分数

        ScoredDocument(int docId, double score, Document document) {
            this(docId, score, document, 0.0, 0.0);
        }

        ScoredDocument(int docId, double score, Document document, double vectorScore, double textScore) {
            this.docId = docId;
            this.score = score;
            this.document = document;
            this.vectorScore = vectorScore;
            this.textScore = textScore;
        }
    }

    /**
     * 资源清理
     */
    @PreDestroy
    @Override
    public void destroy() throws Exception {
        if (isClosed.compareAndSet(false, true)) {
            log.info("🔄 开始关闭Lucene向量存储...");

            try {
                if (searcherManager != null) {
                    searcherManager.close();
                }
                if (indexWriter != null) {
                    indexWriter.close();
                }
                if (directory != null) {
                    directory.close();
                }
                if (analyzer != null) {
                    analyzer.close();
                }

                log.info("✅ Lucene向量存储关闭完成");

            } catch (Exception e) {
                log.error("❌ 关闭Lucene向量存储时出错", e);
                throw e;
            }
        }
    }

    /**
     * 将langchain4j的Filter转换为Lucene Query
     */
    private Query convertFilterToQuery(Filter filter) {
        if (filter instanceof IsEqualTo) {
            IsEqualTo equalTo = (IsEqualTo) filter;
            String fieldName = equalTo.key();
            Object value = equalTo.comparisonValue();

            // 根据字段类型创建相应的查询
            if (value instanceof String) {
                return new TermQuery(new Term(fieldName, (String) value));
            } else {
                return new TermQuery(new Term(fieldName, value.toString()));
            }
        } else if (filter instanceof ContainsString) {
            ContainsString containsString = (ContainsString) filter;
            String fieldName = containsString.key();
            String value = containsString.comparisonValue();

            // 使用WildcardQuery实现类似like操作的功能
            // 在值的前后添加通配符，实现包含匹配
            String wildcardValue = "*" + value + "*";
            return new WildcardQuery(new Term(fieldName, wildcardValue));

        } else if (filter instanceof IsIn) {
            IsIn isIn = (IsIn) filter;
            String fieldName = isIn.key();
            Collection<?> values = isIn.comparisonValues();

            // 创建BooleanQuery，使用OR逻辑组合多个TermQuery
            BooleanQuery.Builder builder = new BooleanQuery.Builder();
            for (Object value : values) {
                String stringValue = value instanceof String ? (String) value : value.toString();
                builder.add(new TermQuery(new Term(fieldName, stringValue)), BooleanClause.Occur.SHOULD);
            }
            return builder.build();

        } else if (filter instanceof IsNotEqualTo) {
            IsNotEqualTo notEqualTo = (IsNotEqualTo) filter;
            String fieldName = notEqualTo.key();
            Object value = notEqualTo.comparisonValue();

            // 使用BooleanQuery的MUST_NOT实现不等于
            String stringValue = value instanceof String ? (String) value : value.toString();
            BooleanQuery.Builder builder = new BooleanQuery.Builder();
            builder.add(new MatchAllDocsQuery(), BooleanClause.Occur.MUST);
            builder.add(new TermQuery(new Term(fieldName, stringValue)), BooleanClause.Occur.MUST_NOT);
            return builder.build();

        } else if (filter instanceof And) {
            And andFilter = (And) filter;
            BooleanQuery.Builder builder = new BooleanQuery.Builder();
            builder.add(convertFilterToQuery(andFilter.left()), BooleanClause.Occur.MUST);
            builder.add(convertFilterToQuery(andFilter.right()), BooleanClause.Occur.MUST);
            return builder.build();

        } else if (filter instanceof Or) {
            Or orFilter = (Or) filter;
            BooleanQuery.Builder builder = new BooleanQuery.Builder();
            builder.add(convertFilterToQuery(orFilter.left()), BooleanClause.Occur.SHOULD);
            builder.add(convertFilterToQuery(orFilter.right()), BooleanClause.Occur.SHOULD);
            return builder.build();

        } else if (filter instanceof Not) {
            Not notFilter = (Not) filter;
            BooleanQuery.Builder builder = new BooleanQuery.Builder();
            builder.add(new MatchAllDocsQuery(), BooleanClause.Occur.MUST);
            builder.add(convertFilterToQuery(notFilter.expression()), BooleanClause.Occur.MUST_NOT);
            return builder.build();
        }

        // 如果是其他类型的过滤器，记录警告并返回匹配所有文档的查询
        log.warn("不支持的过滤器类型: {}", filter.getClass().getSimpleName());
        return new MatchAllDocsQuery(); // 返回匹配所有文档的查询作为后备
    }

    // --- 管理和调试方法 ---

    /**
     * 获取Directory对象，用于管理界面访问索引
     * 注意：这个方法仅用于管理和调试目的
     */
    public Directory getDirectory() {
        return directory;
    }

    /**
     * 获取IndexWriter对象，用于管理界面访问索引
     * 注意：这个方法仅用于管理和调试目的
     */
    public IndexWriter getIndexWriter() {
        return indexWriter;
    }

    /**
     * 获取SearcherManager对象，用于管理界面访问索引
     * 注意：这个方法仅用于管理和调试目的
     */
    public SearcherManager getSearcherManager() {
        return searcherManager;
    }
}
