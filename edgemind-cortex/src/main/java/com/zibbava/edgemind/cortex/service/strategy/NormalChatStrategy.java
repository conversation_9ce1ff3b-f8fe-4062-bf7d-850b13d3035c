package com.zibbava.edgemind.cortex.service.strategy;

import com.zibbava.edgemind.cortex.dto.ChatContext;

import com.zibbava.edgemind.cortex.entity.ChatConversation;
import com.zibbava.edgemind.cortex.entity.ChatMessage;
import com.zibbava.edgemind.cortex.service.ChatService;
import com.zibbava.edgemind.cortex.service.RemoteModelIntegrationService;
import com.zibbava.edgemind.cortex.service.strategy.ChatStrategy;
import com.zibbava.edgemind.cortex.service.strategy.PromptTemplateManager;
import com.zibbava.edgemind.cortex.service.strategy.handler.LocalModelChatHandler;
import com.zibbava.edgemind.cortex.service.strategy.handler.RemoteModelChatHandler;
import dev.langchain4j.model.chat.StreamingChatModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import java.util.UUID;

/**
 * 企业级普通对话策略实现
 * <p>
 * 🚀 核心特性：
 * - 智能提示词工程：根据对话类型和上下文动态构建提示词
 * - 历史对话感知：充分利用对话历史提供连贯的交互体验
 * - 多模态支持：支持文本、图片等多种输入形式
 * - 角色一致性：维持AI助手的专业形象和对话风格
 * - 上下文理解：智能分析用户意图和对话场景
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class NormalChatStrategy implements ChatStrategy {

    private final ChatService chatService;
    private final PromptTemplateManager promptTemplateManager;
    private final RemoteModelIntegrationService remoteModelIntegrationService;
    private final LocalModelChatHandler localModelChatHandler;
    private final RemoteModelChatHandler remoteModelChatHandler;

    // 企业级配置参数
    @Value("${chat.assistant.name:智能助手}")
    private String assistantName;

    @Value("${chat.assistant.role:专业的AI助手}")
    private String assistantRole;

    @Value("${chat.style.professional:true}")
    private boolean professionalStyle;

    @Value("${chat.context.analysis:true}")
    private boolean enableContextAnalysis;

    @Value("${chat.multimodal.enabled:true}")
    private boolean multimodalEnabled;


    @Override
    public boolean checkPermission(ChatContext context) {
        // 如果有会话ID，检查会话归属
        if (context.getConversationId() != null) {
            ChatConversation conversation = chatService.getConversation(context.getConversationId());
            if (conversation == null) {
                log.warn("会话不存在: {}", context.getConversationId());
                return false;
            }
            if (!conversation.getUserId().equals(context.getUserId())) {
                log.warn("用户 {} 尝试访问不属于他的会话 {}", context.getUserId(), context.getConversationId());
                return false;
            }
        }
        return true;
    }

    @Override
    public ChatContext prepareContext(ChatContext context) {
        // 处理会话（创建或获取）
        if (context.getConversationId() == null) {
            // 智能生成会话标题
            String intelligentTitle = generateIntelligentTitle(context.getPrompt());
            ChatConversation conversation = chatService.createConversation(
                    context.getUserId(),
                    intelligentTitle
            );
            context.setConversationId(conversation.getId());
            context.setConversation(conversation);
            log.info("为用户 {} 创建新会话: {} (标题: {})", context.getUserId(), conversation.getId(), intelligentTitle);
        } else {
            context.setConversation(chatService.getConversation(context.getConversationId()));
            log.info("使用已有会话: {}", context.getConversationId());
        }

        // 处理图片上传（如果有）
        if (context.getImageFile() != null && !context.getImageFile().isEmpty()) {
            try {
                String imagePath = saveUploadedImage(context.getImageFile());
                log.info("保存上传图片: {}", imagePath);
                // 这里可以设置图片路径到上下文中，如果需要的话
            } catch (IOException e) {
                log.error("保存图片失败", e);
            }
        }

        return context;
    }

    @Override
    public String buildPrompt(ChatContext context) {
        // 使用企业级提示词模板管理器构建提示词
        return buildEnterprisePromptWithTemplate(context);
    }

    /**
     * 使用模板管理器构建企业级提示词
     */
    private String buildEnterprisePromptWithTemplate(ChatContext context) {
        try {
            String finalPrompt;

            // 根据输入类型选择合适的提示词模板
            if (multimodalEnabled && context.getImageFile() != null && !context.getImageFile().isEmpty()) {
                // 多模态对话（包含图片）
                String imagePath = "已上传图片"; // 简化处理
                finalPrompt = promptTemplateManager.buildMultimodalChatPrompt(
                        context.getPrompt(),
                        imagePath
                );
                log.info("🖼️ 构建优化多模态对话提示词");
            } else {
                // 普通文本对话
                finalPrompt = promptTemplateManager.buildNormalChatPrompt(
                        context.getPrompt()
                );
            }

            return finalPrompt;

        } catch (Exception e) {
            log.error("❌ 构建企业级提示词失败: {}", e.getMessage(), e);
        }
        return context.getPrompt();
    }

    /**
     * 检查是否有对话历史
     */
    private boolean hasConversationHistory(ChatContext context) {
        if (context.getConversationId() == null) {
            return false;
        }

        try {
            // 只检查消息数量，不加载所有消息
            int messageCount = chatService.getConversationMessagesCount(context.getConversationId());
            return messageCount > 0;

        } catch (Exception e) {
            log.debug("检查对话历史失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 智能生成会话标题
     */
    private String generateIntelligentTitle(String userPrompt) {
        if (userPrompt == null || userPrompt.trim().isEmpty()) {
            return "新对话";
        }

        // 简单的标题生成逻辑，可以后续用AI模型优化
        String title = userPrompt.trim();
        if (title.length() > 20) {
            title = title.substring(0, 17) + "...";
        }

        // 移除换行符和多余空格
        title = title.replaceAll("\\s+", " ");

        return title;
    }

    @Override
    public Flux<String> executeChat(ChatContext context) {
        try {
            log.info("🚀 开始执行企业级普通对话: conversationId={}, model={}, toolCalling={}",
                    context.getConversationId(), context.getModelName(), context.getEnableToolCalling());

            // 检查是否为远程模型
            StreamingChatModel remoteStreamingModel = remoteModelIntegrationService.getStreamingChatModel(context.getModelName());
            if (remoteStreamingModel != null) {
                log.info("🌐 使用远程流式模型: {}", context.getModelName());
                return remoteModelChatHandler.executeChat(context, remoteStreamingModel);
            } else {
                log.info("🏠 使用本地模型: {}", context.getModelName());
                return localModelChatHandler.executeChat(context);
            }
        } catch (Exception e) {
            log.error("❌ 执行企业级对话出错: {}", e.getMessage(), e);
            return Flux.error(e);
        }
    }

    @Override
    public ChatMessage saveChatHistory(ChatContext context, String userContent, String aiContent, String thinkingContent) {
        // 保存用户消息
        String imagePath = null;
        if (context.getImageFile() != null && !context.getImageFile().isEmpty()) {
            try {
                imagePath = saveUploadedImage(context.getImageFile());
            } catch (Exception e) {
                log.warn("⚠️ 保存图片失败: {}", e.getMessage());
            }
        }

        chatService.saveUserMessage(
                context.getConversationId(),
                userContent,
                imagePath
        );

        // 保存AI消息
        return chatService.saveAiMessage(
                context.getConversationId(),
                aiContent,
                thinkingContent,
                calculateTokenCount(aiContent, thinkingContent),
                context.getModelName()
        );
    }

    /**
     * 简单的Token数量估算
     */
    private Integer calculateTokenCount(String aiContent, String thinkingContent) {
        if (aiContent == null && thinkingContent == null) {
            return 0;
        }

        // 简单估算：平均4个字符 = 1个token
        int aiTokens = aiContent != null ? aiContent.length() / 4 : 0;
        int thinkingTokens = thinkingContent != null ? thinkingContent.length() / 4 : 0;

        return aiTokens + thinkingTokens;
    }

    @Override
    public boolean isApplicable(ChatContext context) {
        // 对于普通对话，只要不是知识库对话就适用
        return !context.isKnowledgeChat();
    }

    // 辅助方法：保存上传图片
    private String saveUploadedImage(MultipartFile imageFile) throws IOException {
        // 确保目录存在
        String uploadDir = "uploads/images";
        Path uploadPath = Paths.get(uploadDir);
        if (!Files.exists(uploadPath)) {
            Files.createDirectories(uploadPath);
        }

        // 生成唯一文件名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String originalFilename = imageFile.getOriginalFilename();
        String fileExtension = originalFilename != null && originalFilename.contains(".")
                ? originalFilename.substring(originalFilename.lastIndexOf("."))
                : ".jpg";
        String filename = "img_" + timestamp + "_" + UUID.randomUUID().toString().substring(0, 8) + fileExtension;

        // 保存文件
        Path filePath = uploadPath.resolve(filename);
        Files.copy(imageFile.getInputStream(), filePath);

        log.info("📸 图片保存成功: {}", filePath.toString());
        return filePath.toString();
    }


}
