package com.zibbava.edgemind.cortex.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * Asynchronous Task Executor Configuration
 */
@Configuration
@EnableAsync // Enables Spring's asynchronous method execution capability
public class AsyncConfig {

    /**
     * Defines a custom thread pool executor for async tasks, e.g., document indexing.
     *
     * @return TaskExecutor bean.
     */
    @Bean("asyncTaskExecutor") // Bean name used in @Async annotation
    public TaskExecutor asyncTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // Configure core pool size, max pool size, queue capacity based on expected load
        executor.setCorePoolSize(4); // Start with 4 threads
        executor.setMaxPoolSize(10); // Allow up to 10 threads
        executor.setQueueCapacity(1000); // Queue size before rejecting tasks
        executor.setThreadNamePrefix("DocIndexAsync-");
        // Rejection policy: Caller runs the task if pool is full
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
} 