package com.zibbava.edgemind.cortex.config; // 或者放在 service 包下也可以

import cn.dev33.satoken.stp.StpInterface;
import com.zibbava.edgemind.cortex.entity.Permission;
import com.zibbava.edgemind.cortex.entity.Role;
import com.zibbava.edgemind.cortex.service.PermissionService;
import com.zibbava.edgemind.cortex.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 自定义权限验证接口扩展，Sa-Token 调用此类来获取用户对应的 角色列表 和 权限列表
 */
@Component // 保证此类被 SpringBoot 扫描，并注入 Sa-Token
public class StpInterfaceImpl implements StpInterface {

    // 推荐注入 Service
    @Autowired
    private RoleService roleService;
    @Autowired
    private PermissionService permissionService;

    /**
     * 返回指定账号 id 所拥有的权限码集合
     * @param loginId   账号id (通常是用户ID)
     * @param loginType 账号类型
     * @return 该账号 id 具有的权限码集合
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        Long userId;
        try {
            userId = Long.parseLong(loginId.toString());
        } catch (NumberFormatException e) {
            System.err.println("StpInterfaceImpl - Invalid loginId format: " + loginId);
            return new ArrayList<>();
        }

        // 1. 根据用户ID查询角色
        List<Role> roles = roleService.findRolesByUserId(userId);
        if (CollectionUtils.isEmpty(roles)) {
            return new ArrayList<>();
        }

        // 2. 提取角色ID列表
        List<Long> roleIds = roles.stream().map(Role::getId).collect(Collectors.toList());

        // 3. 根据角色ID列表查询权限
        List<Permission> permissions = permissionService.findPermissionsByRoleIds(roleIds);
        if (CollectionUtils.isEmpty(permissions)) {
            return new ArrayList<>();
        }

        // 4. 提取权限编码并去重
        return permissions.stream()
                .map(Permission::getPermissionCode)
                .filter(code -> code != null && !code.isBlank()) // 确保权限码不为空
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 返回指定账号 id 所拥有的角色标识集合
     * @param loginId   账号id (通常是用户ID)
     * @param loginType 账号类型
     * @return 该账号 id 具有的角色标识集合
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        Long userId;
        try {
            userId = Long.parseLong(loginId.toString());
        } catch (NumberFormatException e) {
            System.err.println("StpInterfaceImpl - Invalid loginId format: " + loginId);
            return new ArrayList<>();
        }

        // 1. 根据用户ID查询角色
        List<Role> roles = roleService.findRolesByUserId(userId);
        if (CollectionUtils.isEmpty(roles)) {
            return new ArrayList<>();
        }

        // 2. 提取角色编码并去重
        return roles.stream()
                .map(Role::getRoleCode)
                .filter(code -> code != null && !code.isBlank()) // 确保角色码不为空
                .distinct()
                .collect(Collectors.toList());
    }
} 