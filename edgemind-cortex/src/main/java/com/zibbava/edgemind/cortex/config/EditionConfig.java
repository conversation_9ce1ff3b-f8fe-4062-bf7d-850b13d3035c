package com.zibbava.edgemind.cortex.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 版本配置类
 * 用于区分个人版和企业版功能
 */
@Component
@ConfigurationProperties(prefix = "edgemind.edition")
public class EditionConfig {

    /**
     * 版本类型：personal(个人版) 或 enterprise(企业版)
     */
    private String type = "personal";

    // Getters and Setters

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    /**
     * 判断是否为企业版
     */
    public boolean isEnterprise() {
        return "enterprise".equalsIgnoreCase(type);
    }

    /**
     * 判断是否为个人版
     */
    public boolean isPersonal() {
        return "personal".equalsIgnoreCase(type);
    }
}
