package com.zibbava.edgemind.cortex.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 许可证检查结果
 * 包含授权状态和详细信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LicenseCheckResult {
    
    /**
     * 是否授权
     */
    private boolean licensed;
    
    /**
     * 授权状态详细信息
     */
    private LicenseStatusDTO statusInfo;
    
    /**
     * 失败原因
     * 可能的值：null(成功)、"expired"(已过期)、"fingerprint"(硬件指纹不匹配)、"not_activated"(未激活)、"error"(系统错误)
     */
    private String failReason;
    
    /**
     * 失败消息
     */
    private String failMessage;
}
