package com.zibbava.edgemind.cortex.dto.knowledgebase;

import com.zibbava.edgemind.cortex.common.enums.NodeType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 知识库节点数据传输对象 (DTO)，用于API响应。
 * 通常包含节点的层级关系（通过 children 字段）。
 */
@Data
// @ApiModel(description = "知识库节点信息，包含子节点用于树状展示")
public class KnowledgeNodeDto {

    /**
     * 节点唯一ID
     */
    // @ApiModelProperty(value = "节点唯一ID")
    private String nodeId;

    /**
     * 所属知识空间ID
     */
    // @ApiModelProperty(value = "所属知识空间ID")
    private String spaceId;

    /**
     * 父节点ID，根节点时为null
     */
    // @ApiModelProperty(value = "父节点ID，根节点时为null")
    private String parentNodeId;

    /**
     * 节点名称
     */
    // @ApiModelProperty(value = "节点名称")
    private String name;

    /**
     * 节点类型 (FOLDER 或 FILE)
     */
    // @ApiModelProperty(value = "节点类型 (FOLDER 或 FILE)")
    private NodeType type;

    /**
     * 创建者用户ID (响应时可考虑转换为用户名或昵称)
     */
    // @ApiModelProperty(value = "创建者用户ID")
    private Long createBy;

    /**
     * 创建时间
     */
    // @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 最后更新者用户ID (响应时可考虑转换为用户名或昵称)
     */
    // @ApiModelProperty(value = "最后更新者用户ID")
    private Long updateBy;

    /**
     * 最后更新时间
     */
    // @ApiModelProperty(value = "最后更新时间")
    private LocalDateTime updateTime;

    /**
     * 子节点列表，用于构建树状结构
     */
    // @ApiModelProperty(value = "子节点列表，用于构建树状结构")
    private List<KnowledgeNodeDto> children;
} 