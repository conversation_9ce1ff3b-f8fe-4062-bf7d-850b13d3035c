package com.zibbava.edgemind.cortex.service;

import com.zibbava.edgemind.cortex.dto.ollama.OllamaChatRequest;
import com.zibbava.edgemind.cortex.dto.ollama.OllamaMessage;
import com.zibbava.edgemind.cortex.model.ollama.OllamaModel;
import com.zibbava.edgemind.cortex.service.streaming.StreamingResponseFormatter;
import com.zibbava.edgemind.cortex.util.ollama.TriConsumer;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.StreamingResponseHandler;
import dev.langchain4j.model.chat.StreamingChatModel;

import dev.langchain4j.model.ollama.OllamaStreamingChatModel;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.model.chat.response.StreamingChatResponseHandler;
import dev.langchain4j.model.chat.response.ChatResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * LangChain4j Ollama服务
 * 使用langchain4j-ollama组件替代原有的OllamaUtils实现
 * 支持动态模型加载、think模式和流式对话
 */
@Slf4j
@Service
public class LangChain4jOllamaService {

    private final StreamingResponseFormatter responseFormatter;

    @Value("${ollama.base-url:http://localhost:11434}")
    private String baseUrl;

    @Value("${ollama.timeout:60}")
    private Integer timeout;

    // 缓存已创建的流式模型实例，支持动态加载
    private final ConcurrentHashMap<String, OllamaStreamingChatModel> streamingModelCache = new ConcurrentHashMap<>();

    public LangChain4jOllamaService(StreamingResponseFormatter responseFormatter) {
        this.responseFormatter = responseFormatter;
    }

    /**
     * 获取或创建流式聊天模型
     * 支持动态模型加载，按需创建模型实例
     *
     * @param modelName 模型名称
     * @param enableThinking 是否启用think模式
     * @return 流式聊天模型实例
     */
    public OllamaStreamingChatModel getOrCreateStreamingModel(String modelName, boolean enableThinking) {
        String cacheKey = modelName + "_thinking_" + enableThinking;
        
        return streamingModelCache.computeIfAbsent(cacheKey, key -> {
            var builder = OllamaStreamingChatModel.builder()
                    .baseUrl(baseUrl)
                    .modelName(modelName)
                    .timeout(java.time.Duration.ofSeconds(timeout));

            // 配置think模式
            if (enableThinking) {
                builder.think(true)
                       .returnThinking(true);
            } else {
                builder.think(false)
                       .returnThinking(false);
            }

            return builder.build();
        });
    }



    /**
     * 执行流式聊天
     * 使用langchain4j-ollama组件实现流式对话
     * 
     * @param request Ollama聊天请求
     * @return 响应流
     */
    public Flux<String> streamChat(OllamaChatRequest request) {
        return Flux.create(sink -> {
            try {
                String modelName = request.getModel();
                boolean enableThinking = request.getThink() != null && request.getThink();

                // 获取流式模型实例
                OllamaStreamingChatModel streamingModel = getOrCreateStreamingModel(modelName, enableThinking);
                
                // 转换消息格式
                List<ChatMessage> messages = convertMessages(request.getMessages());
                
                // 创建流式响应处理器
                StreamingChatResponseHandler handler = createStreamingHandler(sink, enableThinking);

                // 执行流式聊天
                streamingModel.chat(messages, handler);
                
            } catch (Exception e) {
                log.error("❌ langchain4j流式聊天出错: {}", e.getMessage(), e);
                sink.error(e);
            }
        });
    }

    /**
     * 创建流式响应处理器
     * 处理模型的流式响应，包括thinking内容
     */
    private StreamingChatResponseHandler createStreamingHandler(FluxSink<String> sink, boolean enableThinking) {
        return new StreamingChatResponseHandler() {
            private final StringBuilder contentBuilder = new StringBuilder();
            private final StringBuilder thinkingBuilder = new StringBuilder();
            private final AtomicBoolean isInsideThinkingBlock = new AtomicBoolean(false);

            @Override
            public void onPartialResponse(String partialResponse) {
                try {
                    // 处理thinking模式的特殊标记
                        if (partialResponse.contains("<think>")) {
                            isInsideThinkingBlock.set(true);
                            // 发送thinking开始标记
                            String formattedStart = responseFormatter.formatThinkingStart();
                            sink.next(formattedStart);
                            return;
                        } else if (partialResponse.contains("</think>")) {
                            isInsideThinkingBlock.set(false);
                            // 发送thinking结束标记
                            String formattedEnd = responseFormatter.formatThinkingEnd();
                            sink.next(formattedEnd);
                            return;
                        }

                        if (isInsideThinkingBlock.get()) {
                            thinkingBuilder.append(partialResponse);
                            // 发送thinking内容，使用统一格式
                            String formattedThinking = responseFormatter.formatThinkingContent(partialResponse);
                            sink.next(formattedThinking);
                            return;
                        }

                    // 处理正常内容，使用统一格式
                    contentBuilder.append(partialResponse);
                    String formattedContent = responseFormatter.formatMainContent(partialResponse);
                    sink.next(formattedContent);

                } catch (Exception e) {
                    log.error("❌ 处理流式token出错: {}", e.getMessage(), e);
                }
            }

            @Override
            public void onCompleteResponse(ChatResponse response) {
                try {
                    AiMessage aiMessage = response.aiMessage();



                    sink.complete();
                } catch (Exception e) {
                    log.error("❌ 完成流式聊天时出错: {}", e.getMessage(), e);
                    sink.error(e);
                }
            }

            @Override
            public void onPartialThinking(dev.langchain4j.model.chat.response.PartialThinking partialThinking) {
                if (enableThinking && partialThinking != null) {
                    String thinkingText = partialThinking.text();
                    thinkingBuilder.append(thinkingText);
                    // 使用统一格式发送thinking内容
                    String formattedThinking = responseFormatter.formatThinkingContent(thinkingText);
                    sink.next(formattedThinking);
                }
            }

            @Override
            public void onPartialToolCall(dev.langchain4j.model.chat.response.PartialToolCall partialToolCall) {
                // 暂时不处理工具调用
            }

            @Override
            public void onCompleteToolCall(dev.langchain4j.model.chat.response.CompleteToolCall completeToolCall) {
                // 暂时不处理工具调用
            }

            @Override
            public void onError(Throwable error) {
                log.error("❌ langchain4j流式聊天出错: {}", error.getMessage(), error);
                sink.error(error);
            }
        };
    }

    /**
     * 转换消息格式
     * 将OllamaMessage转换为LangChain4j的ChatMessage格式
     */
    private List<ChatMessage> convertMessages(List<OllamaMessage> ollamaMessages) {
        List<ChatMessage> messages = new ArrayList<>();
        
        for (OllamaMessage msg : ollamaMessages) {
            switch (msg.getRole().toLowerCase()) {
                case "system":
                    messages.add(SystemMessage.from(msg.getContent()));
                    break;
                case "user":
                    messages.add(UserMessage.from(msg.getContent()));
                    break;
                case "assistant":
                    AiMessage.Builder aiBuilder = AiMessage.builder()
                            .text(msg.getContent());
                    
                    // 添加thinking内容
                    if (msg.getThinking() != null && !msg.getThinking().isEmpty()) {
                        aiBuilder.thinking(msg.getThinking());
                    }
                    
                    messages.add(aiBuilder.build());
                    break;
                default:
                    log.warn("⚠️ 未知的消息角色: {}", msg.getRole());
                    break;
            }
        }
        
        return messages;
    }



    /**
     * 清理模型缓存
     * 用于释放不再使用的模型实例
     */
    public void clearModelCache() {
        streamingModelCache.clear();
    }

    /**
     * 获取缓存的模型数量
     */
    public int getCachedModelCount() {
        return streamingModelCache.size();
    }

    /**
     * 检查模型是否已缓存
     */
    public boolean isModelCached(String modelName, boolean enableThinking) {
        String cacheKey = modelName + "_thinking_" + enableThinking;
        return streamingModelCache.containsKey(cacheKey);
    }
}
