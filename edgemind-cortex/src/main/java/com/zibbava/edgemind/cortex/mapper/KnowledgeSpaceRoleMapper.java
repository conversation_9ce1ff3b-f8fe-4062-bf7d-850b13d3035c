package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.KnowledgeSpaceRole;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 知识空间角色关联Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface KnowledgeSpaceRoleMapper extends BaseMapper<KnowledgeSpaceRole> {
    
    /**
     * 检查角色是否有访问空间的权限（使用XML配置）
     */
    boolean hasRoleAccess(@Param("spaceId") String spaceId,
                         @Param("roleIds") List<Long> roleIds,
                         @Param("action") String action);
    
    /**
     * 删除空间的所有角色权限
     */
    @Delete("DELETE FROM kb_space_roles WHERE space_id = #{spaceId}")
    void deleteBySpaceId(@Param("spaceId") String spaceId);
    
    /**
     * 查询知识空间的角色权限配置
     */
    @Select("""
        SELECT sr.*, r.role_name, r.role_code 
        FROM kb_space_roles sr 
        LEFT JOIN sys_role r ON sr.role_id = r.id 
        WHERE sr.space_id = #{spaceId}
        ORDER BY sr.create_time
    """)
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "spaceId", column = "space_id"),
        @Result(property = "roleId", column = "role_id"),
        @Result(property = "permissionLevel", column = "permission_level"),
        @Result(property = "createBy", column = "create_by"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time"),
        @Result(property = "role.id", column = "role_id"),
        @Result(property = "role.roleName", column = "role_name"),
        @Result(property = "role.roleCode", column = "role_code")
    })
    List<KnowledgeSpaceRole> findSpaceRolePermissions(@Param("spaceId") String spaceId);
}
