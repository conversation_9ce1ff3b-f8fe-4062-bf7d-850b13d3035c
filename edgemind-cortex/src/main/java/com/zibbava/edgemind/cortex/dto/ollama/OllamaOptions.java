package com.zibbava.edgemind.cortex.dto.ollama;

import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Ollama模型选项参数类
 * 替代Map使用，提供类型安全的参数配置
 */
@Data
@Builder
public class OllamaOptions {
    private Float temperature;    // 温度参数 (0.0-1.0)
    private Float topP;          // top_p参数 (0.0-1.0) 
    private Float topK;          // top_k参数
    private Integer numPredict;   // 最大生成token数
    private Integer seed;         // 随机种子
    private List<String> stop;    // 停止词列表
    private Float repeatPenalty;  // 重复惩罚
    private Integer repeatLastN;  // 重复检查的最后N个token
    private Float presencePenalty; // 存在惩罚
    private Float frequencyPenalty; // 频率惩罚
    private Integer numCtx;       // 上下文长度
    private Integer numBatch;     // 批处理大小
    private Integer numGqa;       // GQA组数
    private Integer numGpu;       // GPU层数
    private String mainGpu;       // 主GPU
    private Boolean lowVram;      // 低显存模式
    private Boolean f16Kv;        // F16键值缓存
    private Boolean logitsAll;    // 返回所有logits
    private Boolean vocabOnly;    // 仅词汇表模式
    private Boolean useMmap;      // 使用内存映射
    private Boolean useMlock;     // 使用内存锁定
    private Boolean numa;         // NUMA支持

    /**
     * 创建默认选项
     */
    public static OllamaOptions createDefault() {
        return OllamaOptions.builder().build();
    }

    /**
     * 创建聊天优化选项
     */
    public static OllamaOptions createForChat(float temperature, float topP, int maxTokens) {
        return OllamaOptions.builder()
                .temperature(temperature)
                .topP(topP)
                .numPredict(maxTokens)
                .build();
    }

    /**
     * 创建确定性输出选项
     */
    public static OllamaOptions createDeterministic(int seed) {
        return OllamaOptions.builder()
                .temperature(0.0f)
                .seed(seed)
                .build();
    }

    /**
     * 转换为Map格式（内部使用）
     */
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (temperature != null) map.put("temperature", temperature);
        if (topP != null) map.put("top_p", topP);
        if (topK != null) map.put("top_k", topK);
        if (numPredict != null) map.put("num_predict", numPredict);
        if (seed != null) map.put("seed", seed);
        if (stop != null && !stop.isEmpty()) map.put("stop", stop);
        if (repeatPenalty != null) map.put("repeat_penalty", repeatPenalty);
        if (repeatLastN != null) map.put("repeat_last_n", repeatLastN);
        if (presencePenalty != null) map.put("presence_penalty", presencePenalty);
        if (frequencyPenalty != null) map.put("frequency_penalty", frequencyPenalty);
        if (numCtx != null) map.put("num_ctx", numCtx);
        if (numBatch != null) map.put("num_batch", numBatch);
        if (numGqa != null) map.put("num_gqa", numGqa);
        if (numGpu != null) map.put("num_gpu", numGpu);
        if (mainGpu != null) map.put("main_gpu", mainGpu);
        if (lowVram != null) map.put("low_vram", lowVram);
        if (f16Kv != null) map.put("f16_kv", f16Kv);
        if (logitsAll != null) map.put("logits_all", logitsAll);
        if (vocabOnly != null) map.put("vocab_only", vocabOnly);
        if (useMmap != null) map.put("use_mmap", useMmap);
        if (useMlock != null) map.put("use_mlock", useMlock);
        if (numa != null) map.put("numa", numa);
        return map;
    }
} 