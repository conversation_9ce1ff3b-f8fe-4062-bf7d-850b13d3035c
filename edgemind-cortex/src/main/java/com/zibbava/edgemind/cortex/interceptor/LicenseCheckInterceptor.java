package com.zibbava.edgemind.cortex.interceptor;

import com.zibbava.edgemind.cortex.dto.LicenseCheckResult;
import com.zibbava.edgemind.cortex.service.LicenseService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.util.Arrays;
import java.util.List;

/**
 * 许可证检查拦截器
 * 用于在请求处理前检查系统是否已授权
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class LicenseCheckInterceptor implements HandlerInterceptor {

    @Value("${run.env}")
    private String runEnv;


    private final LicenseService licenseService;

    // 允许未授权访问的路径
    private final List<String> allowedPaths = Arrays.asList(
            "/wkg/license", // 授权页面
            "/wkg/license/content", // 授权页面内容
            "/wkg/api/license", // 授权相关API
            "/wkg/auth", // 认证相关API
            "/wkg/login", // 登录页面
            "/wkg/static", // 静态资源
            "/wkg/css", // 静态资源
            "/wkg/js", // 静态资源
            "/wkg/fonts", // 静态资源
            "/wkg/images", // 图片资源
            "/error", // 错误页面
            "/favicon.ico" // 网站图标
    );

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (runEnv.equals("demo")) {
            return true;
        }

        String requestPath = request.getRequestURI();

        // 检查是否是允许未授权访问的路径
        for (String allowedPath : allowedPaths) {
            if (requestPath.startsWith(allowedPath)) {
                return true;
            }
        }

        // 特殊处理：如果是访问根路径且带有page=license参数，允许访问
        if ("/wkg/".equals(requestPath) || "/wkg".equals(requestPath)) {
            String pageParam = request.getParameter("page");
            if ("license".equals(pageParam)) {
                return true;
            }
        }

        try {
            // 使用新的检查方法，避免重复查询数据库
            LicenseCheckResult checkResult = licenseService.checkLicenseStatus();

            if (!checkResult.isLicensed()) {
                // 检查是否是授权页面本身的请求
                if (isLicensePageRequest(requestPath, request)) {
                    // 对于授权页面，正常重定向
                    String failReason = checkResult.getFailReason();
                    if ("expired".equals(failReason)) {
                        log.warn("授权验证失败: 许可证已过期");
                        response.sendRedirect("/wkg/?page=license&error=expired");
                    } else if ("fingerprint".equals(failReason)) {
                        log.warn("授权验证失败: 硬件指纹不匹配");
                        response.sendRedirect("/wkg/?page=license&error=fingerprint");
                    } else if ("error".equals(failReason)) {
                        log.warn("授权验证失败: 系统错误");
                        response.sendRedirect("/wkg/?page=license&error=system");
                    } else {
                        log.warn("系统未授权，重定向到授权页面");
                        response.sendRedirect("/wkg/?page=license");
                    }
                } else {
                    // 默认所有其他请求都当作iframe请求，返回JavaScript重定向父窗口
                    response.setContentType("text/html;charset=UTF-8");
                    response.getWriter().write(
                        "<script>window.top.location.href='/wkg/?page=license';</script>"
                    );
                }
                return false;
            }

            return true;
        } catch (Exception e) {
            // 如果检查授权状态时出错，记录错误但不允许访问
            log.error("检查授权状态时出错: {}", e.getMessage(), e);
            response.sendRedirect("/wkg/?page=license&error=system");
            return false;
        }
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 请求处理后的操作（如果需要）
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 请求完成后的操作（如果需要）
    }

    /**
     * 判断是否是授权页面本身的请求
     * 只有 /wkg/?page=license 是特殊的，其他所有请求都当作iframe处理
     */
    private boolean isLicensePageRequest(String requestPath, HttpServletRequest request) {
        return ("/wkg/".equals(requestPath) || "/wkg".equals(requestPath)) &&
               "license".equals(request.getParameter("page"));
    }
}
