package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.KnowledgeSpace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 知识空间表（kb_knowledge_spaces）的MyBatis Mapper接口。
 * 提供了对知识空间数据的基本CRUD操作，并包含自定义查询。
 */
@Mapper
public interface KnowledgeSpaceMapper extends BaseMapper<KnowledgeSpace> {

    /**
      * 查询指定用户可访问的所有知识空间列表。
      * 包括所有公开的团队空间以及该用户拥有的私人空间。
      *
      * @param userId 当前登录用户的ID (对应 sys_user.id)。
      * @return 用户可访问的知识空间实体列表，按创建时间降序排列。
      */
    @Select("SELECT * FROM kb_knowledge_spaces WHERE access_type = 'PUBLIC' OR create_by = #{userId} ORDER BY create_time DESC")
    List<KnowledgeSpace> findAccessibleSpaces(@Param("userId") Long userId);

    /**
     * 根据用户角色查询可访问的知识空间（使用XML配置）
     */
    List<KnowledgeSpace> findAccessibleSpacesByRoles(@Param("userId") Long userId,
                                                     @Param("roleIds") List<Long> roleIds);
} 