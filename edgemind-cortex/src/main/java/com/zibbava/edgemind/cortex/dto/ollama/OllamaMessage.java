package com.zibbava.edgemind.cortex.dto.ollama;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Ollama聊天消息
 */
@Data
@Builder
public class OllamaMessage {
    private String role;    // "user", "assistant", "system", "tool"
    private String content;
    private String thinking; // (思维模型)模型的思考过程
    private List<String> images;
    private List<LangchainToolCall> toolCalls; // 使用LangChain4j格式的工具调用
    private String toolCallId;

    public static OllamaMessage user(String content) {
        return OllamaMessage.builder()
                .role("user")
                .content(content)
                .build();
    }

    public static OllamaMessage assistant(String content) {
        return OllamaMessage.builder()
                .role("assistant")
                .content(content)
                .build();
    }

    public static OllamaMessage system(String content) {
        return OllamaMessage.builder()
                .role("system")
                .content(content)
                .build();
    }

    public static OllamaMessage tool(String content, String toolCallId) {
        return OllamaMessage.builder()
                .role("tool")
                .content(content)
                .toolCallId(toolCallId)
                .build();
    }
} 