package com.zibbava.edgemind.cortex.service.strategy.handler;

import com.zibbava.edgemind.cortex.dto.ChatContext;
import com.zibbava.edgemind.cortex.dto.ollama.OllamaChatRequest;
import com.zibbava.edgemind.cortex.dto.ollama.OllamaMessage;
import com.zibbava.edgemind.cortex.entity.ChatConversation;
import com.zibbava.edgemind.cortex.entity.ChatMessage;
import com.zibbava.edgemind.cortex.enums.ModelInfo;
import com.zibbava.edgemind.cortex.service.ChatService;
import com.zibbava.edgemind.cortex.service.LangChain4jOllamaService;
import com.zibbava.edgemind.cortex.browser.tools.BrowserToolProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.SynchronousSink;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * LangChain4j聊天处理器
 * 使用langchain4j-ollama组件处理本地模型聊天
 * 支持动态模型加载和think模式
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LangChain4jChatHandler {

    private final ChatService chatService;
    private final LangChain4jOllamaService langChain4jOllamaService;

    @Value("${chat.enable-thinking:true}")
    private boolean enableThinking;

    @Value("${chat.max-history-messages:20}")
    private int maxHistoryMessages;

    /**
     * 执行LangChain4j本地模型聊天
     * @param context 聊天上下文
     * @return 聊天结果流
     */
    public Flux<String> executeChat(ChatContext context) {
        try {
            log.info("🚀 开始执行LangChain4j本地模型对话: model={}, toolCalling={}", 
                    context.getModelName(), context.getEnableToolCalling());

            // 设置工具调用状态到ThreadLocal
            BrowserToolProvider.setToolCallingEnabled(
                context.getEnableToolCalling() != null && context.getEnableToolCalling()
            );

            // 构建消息历史列表
            List<OllamaMessage> messages = buildMessageHistory(context);
            
            // 判断是否启用深度思考
            boolean shouldEnableThinking = shouldEnableThinking(context);
            
            // 创建OllamaChatRequest
            OllamaChatRequest chatRequest = OllamaChatRequest.builder()
                    .model(context.getModelName())
                    .messages(messages)
                    .stream(true)
                    .think(shouldEnableThinking)
                    .sessionId("langchain4j_chat_" + context.getUserId() + "_" + context.getConversationId())
                    .build();

            log.info("🧠 LangChain4j聊天请求: model={}, thinking={}, messages={}", 
                    context.getModelName(), shouldEnableThinking, messages.size());

            // 使用LangChain4jOllamaService执行流式聊天
            return langChain4jOllamaService.streamChat(chatRequest)
                    .handle((String rawToken, SynchronousSink<String> sink) -> chatService.handelSSE(
                            context.getMainContentBuilder(),
                            context.getThinkingContentBuilder(),
                            rawToken,
                            sink,
                            context.getIsInsideThinkingBlock()
                    ))
                    .doOnComplete(() -> {
                        updateConversationWithQuality(context);
                        BrowserToolProvider.clearToolCallingState(); // 清理ThreadLocal状态
                        log.info("✅ LangChain4j本地模型对话完成: conversationId={}", context.getConversationId());
                    })
                    .doOnError(e -> {
                        BrowserToolProvider.clearToolCallingState(); // 清理ThreadLocal状态
                        log.error("❌ LangChain4j本地模型对话出错: {}", e.getMessage(), e);
                    });

        } catch (Exception e) {
            log.error("❌ 执行LangChain4j本地模型对话异常: {}", e.getMessage(), e);
            return Flux.error(e);
        }
    }

    /**
     * 构建消息历史
     * 从数据库获取历史消息并转换为Ollama格式
     */
    private List<OllamaMessage> buildMessageHistory(ChatContext context) {
        List<OllamaMessage> messages = new ArrayList<>();
        
        try {
            if (context.getConversationId() != null) {
                // 获取历史消息
                List<ChatMessage> historyMessages = chatService.getMessagesByConversationId(context.getConversationId());
                
                // 限制历史消息数量，避免上下文过长
                if (historyMessages.size() > maxHistoryMessages) {
                    historyMessages = historyMessages.subList(
                        historyMessages.size() - maxHistoryMessages, 
                        historyMessages.size()
                    );
                    log.info("📝 限制历史消息数量为: {}", maxHistoryMessages);
                }
                
                // 转换历史消息格式
                for (ChatMessage msg : historyMessages) {
                    if ("user".equals(msg.getSender())) {
                        messages.add(OllamaMessage.user(msg.getContent()));
                    } else if ("ai".equals(msg.getSender()) || "assistant".equals(msg.getSender())) {
                        OllamaMessage.OllamaMessageBuilder builder = OllamaMessage.builder()
                                .role("assistant")
                                .content(msg.getContent());

                        // 添加thinking内容
                        if (msg.getThinkingContent() != null && !msg.getThinkingContent().isEmpty()) {
                            builder.thinking(msg.getThinkingContent());
                        }

                        messages.add(builder.build());
                    }
                }
                
                log.info("📚 加载历史消息: {} 条", historyMessages.size());
            }
            
            // 添加当前用户消息
            messages.add(OllamaMessage.user(context.getPrompt()));
            
        } catch (Exception e) {
            log.error("❌ 构建消息历史出错: {}", e.getMessage(), e);
            // 如果获取历史消息失败，至少包含当前消息
            messages.clear();
            messages.add(OllamaMessage.user(context.getPrompt()));
        }
        
        return messages;
    }

    /**
     * 判断是否应该启用深度思考
     */
    private boolean shouldEnableThinking(ChatContext context) {
        if (!ModelInfo.supportsThinking(context.getModelName())) {
            log.info("🧠 深度思考功能: 关闭 (模型 {} 不支持think功能)", context.getModelName());
            return false;
        }

        boolean shouldEnable;
        if (context.getEnableThinking() != null) {
            shouldEnable = context.getEnableThinking();
            log.info("🧠 深度思考功能: {} (用户设置, 模型: {})", shouldEnable ? "开启" : "关闭", context.getModelName());
        } else {
            shouldEnable = enableThinking;
            log.info("🧠 深度思考功能: {} (使用默认配置, 模型: {})", shouldEnable ? "开启" : "关闭", context.getModelName());
        }
        
        return shouldEnable;
    }

    /**
     * 更新会话质量评分
     * 根据对话内容和thinking质量进行评分
     */
    private void updateConversationWithQuality(ChatContext context) {
        try {
            if (context.getConversation() == null) {
                return;
            }

            ChatConversation conversation = context.getConversation();
            String mainContent = context.getMainContentBuilder().toString();
            String thinkingContent = context.getThinkingContentBuilder().toString();

            // 计算质量评分
            double qualityScore = calculateQualityScore(mainContent, thinkingContent);
            
            // 保存AI消息到数据库
            ChatMessage aiMessage = chatService.saveAiMessage(
                    conversation.getId(),
                    mainContent,
                    thinkingContent,
                    estimateTokenCount(mainContent + thinkingContent),
                    context.getModelName()
            );

            log.info("💾 保存AI消息: id={}, contentLength={}, thinkingLength={}, quality={}", 
                    aiMessage.getId(), mainContent.length(), thinkingContent.length(), qualityScore);

        } catch (Exception e) {
            log.error("❌ 更新会话质量评分出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 计算对话质量评分
     * 基于内容长度、thinking质量等因素
     */
    private double calculateQualityScore(String mainContent, String thinkingContent) {
        double score = 0.0;
        
        // 基础分数：根据内容长度
        if (mainContent != null && mainContent.length() > 0) {
            score += Math.min(mainContent.length() / 100.0, 5.0); // 最多5分
        }
        
        // thinking质量分数
        if (thinkingContent != null && thinkingContent.length() > 0) {
            score += Math.min(thinkingContent.length() / 200.0, 3.0); // 最多3分
        }
        
        // 内容质量分数（简单的启发式评估）
        if (mainContent != null) {
            // 检查是否包含代码块
            if (mainContent.contains("```")) {
                score += 1.0;
            }
            
            // 检查是否包含列表或结构化内容
            if (mainContent.contains("1.") || mainContent.contains("- ") || mainContent.contains("* ")) {
                score += 0.5;
            }
        }
        
        return Math.min(score, 10.0); // 最高10分
    }

    /**
     * 估算token数量
     * 简单的token数量估算，实际应该使用tokenizer
     */
    private Integer estimateTokenCount(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }
        
        // 简单估算：中文字符按1个token，英文单词按0.75个token计算
        int chineseChars = 0;
        int englishWords = 0;
        
        for (char c : text.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fff) {
                chineseChars++;
            }
        }
        
        String[] words = text.replaceAll("[\\u4e00-\\u9fff]", "").split("\\s+");
        englishWords = words.length;
        
        return (int) (chineseChars + englishWords * 0.75);
    }
}
