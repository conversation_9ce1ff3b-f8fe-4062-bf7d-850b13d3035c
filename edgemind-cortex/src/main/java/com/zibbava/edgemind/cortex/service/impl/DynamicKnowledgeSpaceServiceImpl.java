package com.zibbava.edgemind.cortex.service.impl;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zibbava.edgemind.cortex.dto.CreateSpaceRequest;
import com.zibbava.edgemind.cortex.dto.RolePermissionConfig;
import com.zibbava.edgemind.cortex.entity.KnowledgeSpace;
import com.zibbava.edgemind.cortex.entity.KnowledgeSpaceRole;
import com.zibbava.edgemind.cortex.entity.Permission;
import com.zibbava.edgemind.cortex.entity.Role;
import com.zibbava.edgemind.cortex.mapper.KnowledgeSpaceMapper;
import com.zibbava.edgemind.cortex.mapper.KnowledgeSpaceRoleMapper;
import com.zibbava.edgemind.cortex.service.DynamicKnowledgeSpaceService;
import com.zibbava.edgemind.cortex.service.DynamicPermissionService;
import com.zibbava.edgemind.cortex.service.RoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

// 移除AccessDeniedException导入，使用RuntimeException代替
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 动态知识库空间管理服务实现类
 * 
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DynamicKnowledgeSpaceServiceImpl implements DynamicKnowledgeSpaceService {
    
    private final KnowledgeSpaceMapper spaceMapper;
    private final KnowledgeSpaceRoleMapper spaceRoleMapper;
    private final RoleService roleService;
    private final DynamicPermissionService dynamicPermissionService;
    
    @Override
    @Transactional
    @SaCheckPermission("knowledge:space:create")
    public KnowledgeSpace createDynamicSpace(CreateSpaceRequest request) {
        Long userId = StpUtil.getLoginIdAsLong();
        
        // 创建知识空间
        KnowledgeSpace space = new KnowledgeSpace();
        space.setSpaceId(UUID.randomUUID().toString());
        space.setName(request.getName());
        space.setDescription(request.getDescription());

        // 知识库空间不允许创建私有类型，强制为团队空间
        String accessType = request.getAccessType();
        if ("PRIVATE".equals(accessType)) {
            accessType = "ROLE_BASED"; // 私有类型转换为基于角色
        }
        space.setAccessType(accessType);
        space.setCreateBy(userId);
        space.setUpdateBy(userId);
        
        spaceMapper.insert(space);
        
        // 自动生成权限
        List<Permission> generatedPermissions = dynamicPermissionService
            .generateKnowledgeSpacePermissions(space.getSpaceId(), space.getName());
        
        // 配置角色权限
        if (request.getRolePermissions() != null) {
            for (RolePermissionConfig config : request.getRolePermissions()) {
                // 验证角色是否存在
                Role role = roleService.getById(config.getRoleId());
                if (role == null) {
                    throw new RuntimeException("角色不存在: " + config.getRoleId());
                }

                KnowledgeSpaceRole spaceRole = new KnowledgeSpaceRole();
                spaceRole.setSpaceId(space.getSpaceId());
                spaceRole.setRoleId(config.getRoleId());
                spaceRole.setPermissionLevel(config.getPermissionLevel());
                spaceRole.setCreateBy(userId);

                spaceRoleMapper.insert(spaceRole);
            }
        }
        
        // 如果指定了初始角色权限，自动分配
        if (request.getInitialRoleIds() != null && !request.getInitialRoleIds().isEmpty()) {
            assignInitialRolePermissions(generatedPermissions, request.getInitialRoleIds());
        }
        
        log.info("用户 {} 创建了动态知识空间: {} ({})", userId, space.getName(), space.getSpaceId());
        return space;
    }
    
    @Override
    public List<KnowledgeSpace> getAccessibleSpaces(Long userId) {
        // 获取用户角色
        List<Role> userRoles = roleService.findRolesByUserId(userId);
        List<Long> roleIds = userRoles.stream().map(Role::getId).collect(Collectors.toList());

        // 如果用户没有角色，只能访问公开空间和自己创建的私有空间
        if (roleIds.isEmpty()) {
            roleIds.add(-1L); // 添加一个不存在的角色ID，避免SQL语法错误
        }

        // 查询可访问的空间，并过滤掉个人空间
        List<KnowledgeSpace> spaces = spaceMapper.findAccessibleSpacesByRoles(userId, roleIds);
        return spaces.stream()
            .filter(space -> space.getOwnerUserId() == null) // 过滤掉个人空间
            .collect(Collectors.toList());
    }

    @Override
    public List<KnowledgeSpace> getAllSpaces() {
        // 只返回知识库空间（非个人空间），按创建时间降序排列
        return spaceMapper.selectList(
            new LambdaQueryWrapper<KnowledgeSpace>()
                .isNull(KnowledgeSpace::getOwnerUserId) // 只显示团队知识库空间
                .orderByDesc(KnowledgeSpace::getCreateTime)
        );
    }

    @Override
    public KnowledgeSpace getSpaceById(String spaceId) {
        KnowledgeSpace space = spaceMapper.selectById(spaceId);
        if (space == null) {
            throw new RuntimeException("知识空间不存在: " + spaceId);
        }
        return space;
    }

    @Override
    @Transactional
    @SaCheckPermission("knowledge:space:edit")
    public KnowledgeSpace updateSpace(String spaceId, CreateSpaceRequest request) {
        KnowledgeSpace space = getSpaceById(spaceId);

        // 更新基本信息
        space.setName(request.getName());
        space.setDescription(request.getDescription());
        space.setAccessType(request.getAccessType());
        space.setUpdateBy(StpUtil.getLoginIdAsLong());

        spaceMapper.updateById(space);

        log.info("更新知识空间: {} ({})", space.getName(), space.getSpaceId());
        return space;
    }
    
    @Override
    public boolean hasSpaceAccess(Long userId, String spaceId, String action) {
        KnowledgeSpace space = spaceMapper.selectById(spaceId);
        if (space == null) {
            return false;
        }

        // 公开空间所有人都可以访问
        if ("PUBLIC".equals(space.getAccessType())) {
            return true;
        }

        // 私有空间只有创建者可以访问
        if ("PRIVATE".equals(space.getAccessType())) {
            return userId.equals(space.getCreateBy());
        }

        // 基于角色的权限检查 - 简化版本，只要有角色就可以访问
        if ("ROLE_BASED".equals(space.getAccessType())) {
            List<Role> userRoles = roleService.findRolesByUserId(userId);
            List<Long> roleIds = userRoles.stream().map(Role::getId).collect(Collectors.toList());

            // 如果用户没有角色，直接返回false
            if (roleIds.isEmpty()) {
                return false;
            }

            // 简化权限检查：只要用户有任何角色且该角色被分配给空间，就允许访问
            return spaceRoleMapper.selectCount(
                new LambdaQueryWrapper<KnowledgeSpaceRole>()
                    .eq(KnowledgeSpaceRole::getSpaceId, spaceId)
                    .in(KnowledgeSpaceRole::getRoleId, roleIds)
            ) > 0;
        }

        return false;
    }
    
    @Override
    @Transactional
    @SaCheckPermission("knowledge:space:edit")
    public void updateSpaceRolePermissions(String spaceId, List<RolePermissionConfig> rolePermissions) {
        Long userId = StpUtil.getLoginIdAsLong();
        
        // 检查用户是否有管理该空间的权限
        if (!hasSpaceAccess(userId, spaceId, "admin")) {
            throw new RuntimeException("无权限管理该知识空间");
        }
        
        // 删除现有权限配置
        spaceRoleMapper.deleteBySpaceId(spaceId);
        
        // 添加新的权限配置
        for (RolePermissionConfig config : rolePermissions) {
            // 验证角色是否存在
            Role role = roleService.getById(config.getRoleId());
            if (role == null) {
                log.warn("角色不存在，跳过权限配置: {}", config.getRoleId());
                continue;
            }

            KnowledgeSpaceRole spaceRole = new KnowledgeSpaceRole();
            spaceRole.setSpaceId(spaceId);
            spaceRole.setRoleId(config.getRoleId());
            spaceRole.setPermissionLevel(config.getPermissionLevel());
            spaceRole.setCreateBy(userId);

            spaceRoleMapper.insert(spaceRole);
        }
        
        log.info("用户 {} 更新了知识空间 {} 的角色权限", userId, spaceId);
    }
    
    @Override
    public List<KnowledgeSpaceRole> getSpaceRolePermissions(String spaceId) {
        return spaceRoleMapper.findSpaceRolePermissions(spaceId);
    }
    
    @Override
    @Transactional
    @SaCheckPermission("knowledge:space:delete")
    public void deleteSpace(String spaceId) {
        // 删除空间相关权限
        dynamicPermissionService.removeKnowledgeSpacePermissions(spaceId);
        
        // 删除空间角色关联
        spaceRoleMapper.deleteBySpaceId(spaceId);
        
        // 删除空间本身
        spaceMapper.deleteById(spaceId);
        
        log.info("删除知识空间: {}", spaceId);
    }
    
    /**
     * 为初始角色分配权限
     */
    private void assignInitialRolePermissions(List<Permission> permissions, List<Long> roleIds) {
        for (Long roleId : roleIds) {
            // 验证角色是否存在
            Role role = roleService.getById(roleId);
            if (role == null) {
                log.warn("角色不存在，跳过权限分配: {}", roleId);
                continue;
            }

            List<Long> permissionIds = permissions.stream()
                .map(Permission::getId)
                .collect(Collectors.toList());

            roleService.assignPermissionsToRole(roleId, permissionIds);
        }
    }
}
