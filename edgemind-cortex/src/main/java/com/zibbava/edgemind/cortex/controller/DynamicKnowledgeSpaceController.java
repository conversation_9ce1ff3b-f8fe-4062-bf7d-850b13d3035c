package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import cn.dev33.satoken.stp.StpUtil;
import com.zibbava.edgemind.cortex.dto.ApiResponse;
import com.zibbava.edgemind.cortex.dto.CreateSpaceRequest;
import com.zibbava.edgemind.cortex.dto.RolePermissionConfig;
import com.zibbava.edgemind.cortex.entity.KnowledgeSpace;
import com.zibbava.edgemind.cortex.entity.KnowledgeSpaceRole;
import com.zibbava.edgemind.cortex.entity.Role;
import com.zibbava.edgemind.cortex.service.DynamicKnowledgeSpaceService;
import com.zibbava.edgemind.cortex.service.RoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 动态知识库空间管理控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/knowledge/spaces")
@RequiredArgsConstructor
@Validated
public class DynamicKnowledgeSpaceController {
    
    private final DynamicKnowledgeSpaceService spaceService;
    private final RoleService roleService;
    
    /**
     * 获取用户可访问的知识空间列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<KnowledgeSpace>>> getAccessibleSpaces() {
        Long userId = StpUtil.getLoginIdAsLong();
        List<KnowledgeSpace> spaces = spaceService.getAccessibleSpaces(userId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", spaces));
    }

    /**
     * 获取所有知识空间列表（用于管理页面）
     */
    @GetMapping("/all")
    @SaCheckPermission(value = {"knowledge:space:create", "knowledge:space:edit", "knowledge:space:delete"}, mode = SaMode.OR)
    public ResponseEntity<ApiResponse<List<KnowledgeSpace>>> getAllSpaces() {
        List<KnowledgeSpace> spaces = spaceService.getAllSpaces();
        return ResponseEntity.ok(ApiResponse.success("查询成功", spaces));
    }

    /**
     * 获取指定知识空间详情
     */
    @GetMapping("/{spaceId}")
    @SaCheckPermission("knowledge:space:edit")
    public ResponseEntity<ApiResponse<KnowledgeSpace>> getSpaceById(@PathVariable String spaceId) {
        KnowledgeSpace space = spaceService.getSpaceById(spaceId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", space));
    }

    /**
     * 更新知识空间信息
     */
    @PutMapping("/{spaceId}")
    @SaCheckPermission("knowledge:space:edit")
    public ResponseEntity<ApiResponse<KnowledgeSpace>> updateSpace(
            @PathVariable String spaceId,
            @Valid @RequestBody CreateSpaceRequest request) {
        KnowledgeSpace space = spaceService.updateSpace(spaceId, request);
        return ResponseEntity.ok(ApiResponse.success("知识空间更新成功", space));
    }
    
    /**
     * 创建动态知识空间
     */
    @PostMapping
    @SaCheckPermission("knowledge:space:create")
    public ResponseEntity<ApiResponse<KnowledgeSpace>> createSpace(@Valid @RequestBody CreateSpaceRequest request) {
        KnowledgeSpace space = spaceService.createDynamicSpace(request);
        return ResponseEntity.ok(ApiResponse.success("知识空间创建成功", space));
    }
    
    /**
     * 获取所有可用角色（用于权限配置）
     */
    @GetMapping("/available-roles")
    @SaCheckPermission("knowledge:space:edit")
    public ResponseEntity<ApiResponse<List<Role>>> getAvailableRoles() {
        List<Role> roles = roleService.list();
        return ResponseEntity.ok(ApiResponse.success("查询成功", roles));
    }
    
    /**
     * 获取知识空间的角色权限配置
     */
    @GetMapping("/{spaceId}/role-permissions")
    @SaCheckPermission("knowledge:space:edit")
    public ResponseEntity<ApiResponse<List<KnowledgeSpaceRole>>> getSpaceRolePermissions(@PathVariable String spaceId) {
        List<KnowledgeSpaceRole> permissions = spaceService.getSpaceRolePermissions(spaceId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", permissions));
    }
    
    /**
     * 更新知识空间的角色权限
     */
    @PutMapping("/{spaceId}/role-permissions")
    @SaCheckPermission("knowledge:space:edit")
    public ResponseEntity<ApiResponse<Void>> updateSpaceRolePermissions(
            @PathVariable String spaceId,
            @Valid @RequestBody List<RolePermissionConfig> rolePermissions) {
        spaceService.updateSpaceRolePermissions(spaceId, rolePermissions);
        return ResponseEntity.ok(ApiResponse.success("权限配置更新成功"));
    }
    
    /**
     * 删除知识空间
     */
    @DeleteMapping("/{spaceId}")
    @SaCheckPermission("knowledge:space:delete")
    public ResponseEntity<ApiResponse<Void>> deleteSpace(@PathVariable String spaceId) {
        spaceService.deleteSpace(spaceId);
        return ResponseEntity.ok(ApiResponse.success("知识空间删除成功"));
    }
}
