package com.zibbava.edgemind.cortex.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 创建知识空间请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class CreateSpaceRequest {
    
    @NotBlank(message = "空间名称不能为空")
    @Size(max = 255, message = "空间名称长度不能超过255个字符")
    private String name;
    
    @Size(max = 1000, message = "空间描述长度不能超过1000个字符")
    private String description;

    private String accessType = "ROLE_BASED"; // 访问类型：PUBLIC, PRIVATE, ROLE_BASED

    private List<RolePermissionConfig> rolePermissions; // 角色权限配置

    private List<Long> initialRoleIds; // 初始角色ID列表（用于自动分配权限）
}
