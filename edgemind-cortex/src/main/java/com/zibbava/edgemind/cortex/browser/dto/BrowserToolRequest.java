package com.zibbava.edgemind.cortex.browser.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 浏览器工具请求数据传输对象
 * 用于封装从Java后端发送到浏览器插件的工具调用请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrowserToolRequest {
    
    /**
     * 请求唯一标识符，用于匹配请求和响应
     */
    private String requestId;
    
    /**
     * 工具名称，对应浏览器插件中的工具执行器
     * 例如：browser_navigate, browser_click, browser_fill等
     */
    private String toolName;
    
    /**
     * 工具参数，JSON格式字符串
     * 包含工具执行所需的所有参数
     */
    private String arguments;
    
    /**
     * 请求创建时间戳
     */
    private long timestamp;
    
    /**
     * 请求超时时间（毫秒）
     * 默认30秒
     */
    @Builder.Default
    private long timeoutMs = 30000;
    
    /**
     * 用户ID，用于权限验证和日志记录
     */
    private Long userId;
    
    /**
     * 会话ID，用于关联对话上下文
     */
    private Long conversationId;
    
    /**
     * 请求优先级
     * 1-高优先级，2-普通优先级，3-低优先级
     */
    @Builder.Default
    private int priority = 2;
    
    /**
     * 是否需要等待页面导航完成
     */
    @Builder.Default
    private boolean waitForNavigation = false;
    
    /**
     * 目标标签页ID（可选）
     * 如果不指定，则使用当前活跃标签页
     */
    private Integer targetTabId;
    
    /**
     * 创建请求的便捷方法
     */
    public static BrowserToolRequest create(String toolName, String arguments) {
        return BrowserToolRequest.builder()
                .requestId(java.util.UUID.randomUUID().toString())
                .toolName(toolName)
                .arguments(arguments)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建带用户信息的请求
     */
    public static BrowserToolRequest create(String toolName, String arguments, Long userId, Long conversationId) {
        return BrowserToolRequest.builder()
                .requestId(java.util.UUID.randomUUID().toString())
                .toolName(toolName)
                .arguments(arguments)
                .timestamp(System.currentTimeMillis())
                .userId(userId)
                .conversationId(conversationId)
                .build();
    }
    
    /**
     * 检查请求是否已超时
     */
    public boolean isExpired() {
        return System.currentTimeMillis() - timestamp > timeoutMs;
    }
    
    /**
     * 获取请求年龄（毫秒）
     */
    public long getAge() {
        return System.currentTimeMillis() - timestamp;
    }
}
