package com.zibbava.edgemind.cortex.controller;

import com.zibbava.edgemind.cortex.store.LuceneEmbeddingStore;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.document.Document;
import org.apache.lucene.index.DirectoryReader;
import org.apache.lucene.index.IndexReader;
import org.apache.lucene.index.LeafReaderContext;
import org.apache.lucene.index.Term;
import org.apache.lucene.search.*;
import org.apache.lucene.store.Directory;
import org.apache.lucene.util.Bits;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;

/**
 * Lucene数据管理控制器
 * 提供查看和管理Lucene索引数据的功能
 * 注意：为了测试方便，暂时移除了授权检查
 */
@Slf4j
@Controller
@RequestMapping("/admin/lucene")
@RequiredArgsConstructor
public class LuceneDataController {

    private final LuceneEmbeddingStore luceneEmbeddingStore;
    private final EmbeddingModel embeddingModel;

    /**
     * 显示Lucene数据管理页面
     */
    @GetMapping
    public String luceneDataPage(Model model) {
        try {
            // 获取索引统计信息
            Map<String, Object> stats = getIndexStats();
            model.addAttribute("stats", stats);
            
            // 获取最近的文档
            List<Map<String, Object>> recentDocs = getRecentDocuments(10);
            model.addAttribute("recentDocs", recentDocs);
            
        } catch (Exception e) {
            log.error("获取Lucene数据失败", e);
            model.addAttribute("error", "获取Lucene数据失败: " + e.getMessage());
        }
        
        return "admin/lucene-data";
    }

    /**
     * 搜索文档
     */
    @GetMapping("/search")
    @ResponseBody
    public Map<String, Object> searchDocuments(
            @RequestParam(defaultValue = "") String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            Directory directory = luceneEmbeddingStore.getDirectory();
            try (IndexReader reader = DirectoryReader.open(directory)) {
                IndexSearcher searcher = new IndexSearcher(reader);

                Query luceneQuery;
                if (query.trim().isEmpty()) {
                    // 如果没有查询条件，返回所有文档
                    luceneQuery = new MatchAllDocsQuery();
                } else {
                    // 在内容字段中搜索，使用通配符查询以支持部分匹配
                    try {
                        // 尝试使用通配符查询
                        luceneQuery = new WildcardQuery(new Term("text", "*" + query.toLowerCase() + "*"));
                    } catch (Exception e) {
                        // 如果通配符查询失败，使用精确匹配
                        luceneQuery = new TermQuery(new Term("text", query));
                    }
                }

                // 执行搜索，获取更多结果以支持分页
                int totalNeeded = (page + 1) * size;
                TopDocs topDocs = searcher.search(luceneQuery, Math.max(totalNeeded, 100));

                List<Map<String, Object>> documents = new ArrayList<>();
                int start = page * size;
                int end = Math.min(start + size, topDocs.scoreDocs.length);

                for (int i = start; i < end; i++) {
                    ScoreDoc scoreDoc = topDocs.scoreDocs[i];
                    Document doc = searcher.doc(scoreDoc.doc);

                    Map<String, Object> docMap = new HashMap<>();
                    docMap.put("id", doc.get("id"));
                    docMap.put("text", truncateText(doc.get("text"), 200));
                    docMap.put("metadata", doc.get("metadata"));
                    docMap.put("score", scoreDoc.score);
                    docMap.put("docId", scoreDoc.doc);

                    // 检查删除状态
                    boolean isDeleted = isDocumentDeleted(reader, scoreDoc.doc);
                    docMap.put("deleted", isDeleted);

                    // 添加更多字段信息
                    Map<String, String> allFields = new HashMap<>();
                    doc.getFields().forEach(field -> {
                        String name = field.name();
                        String value = field.stringValue();
                        if (!"vector".equals(name) && value != null) {
                            allFields.put(name, truncateText(value, 100));
                        }
                    });
                    docMap.put("allFields", allFields);

                    documents.add(docMap);
                }
                
                result.put("documents", documents);
                result.put("totalHits", topDocs.totalHits.value);
                result.put("page", page);
                result.put("size", size);
                result.put("totalPages", (int) Math.ceil((double) topDocs.totalHits.value / size));
                
            }
        } catch (Exception e) {
            log.error("搜索文档失败", e);
            result.put("error", "搜索失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取文档详情
     */
    @GetMapping("/document/{docId}")
    @ResponseBody
    public Map<String, Object> getDocumentDetail(@PathVariable int docId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Directory directory = luceneEmbeddingStore.getDirectory();
            try (IndexReader reader = DirectoryReader.open(directory)) {
                if (docId >= 0 && docId < reader.maxDoc()) {
                    Document doc = reader.document(docId);
                    
                    Map<String, Object> docMap = new HashMap<>();
                    
                    // 获取所有字段
                    doc.getFields().forEach(field -> {
                        String name = field.name();
                        String value = field.stringValue();
                        
                        if ("vector".equals(name)) {
                            // 向量字段特殊处理
                            docMap.put(name, "[向量数据 - " + (value != null ? value.length() : 0) + " 字节]");
                        } else {
                            docMap.put(name, value);
                        }
                    });
                    
                    docMap.put("docId", docId);

                    // 检查文档是否被删除
                    boolean isDeleted = false;
                    try {
                        // 使用LeafReader检查删除状态
                        for (LeafReaderContext context : reader.leaves()) {
                            if (docId >= context.docBase && docId < context.docBase + context.reader().maxDoc()) {
                                int leafDocId = docId - context.docBase;
                                Bits liveDocs = context.reader().getLiveDocs();
                                if (liveDocs != null) {
                                    isDeleted = !liveDocs.get(leafDocId);
                                }
                                break;
                            }
                        }
                    } catch (Exception e) {
                        log.warn("无法检查文档删除状态: {}", e.getMessage());
                        isDeleted = false;
                    }
                    docMap.put("deleted", isDeleted);
                    
                    result.put("document", docMap);
                } else {
                    result.put("error", "文档ID不存在");
                }
            }
        } catch (Exception e) {
            log.error("获取文档详情失败", e);
            result.put("error", "获取文档详情失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 删除文档
     */
    @DeleteMapping("/document/{id}")
    @ResponseBody
    public Map<String, Object> deleteDocument(@PathVariable String id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 这里需要调用LuceneEmbeddingStore的删除方法
            // 注意：需要根据实际的删除API进行调整
            result.put("success", true);
            result.put("message", "文档删除成功");
        } catch (Exception e) {
            log.error("删除文档失败", e);
            result.put("success", false);
            result.put("error", "删除文档失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取索引统计信息
     */
    private Map<String, Object> getIndexStats() throws IOException {
        Map<String, Object> stats = new HashMap<>();

        Directory directory = luceneEmbeddingStore.getDirectory();
        try (IndexReader reader = DirectoryReader.open(directory)) {
            // 基本统计
            stats.put("totalDocs", reader.numDocs());
            stats.put("maxDoc", reader.maxDoc());
            stats.put("deletedDocs", reader.numDeletedDocs());
            stats.put("hasDeletions", reader.hasDeletions());
            stats.put("indexPath", directory.toString());
            stats.put("numSegments", reader.leaves().size());

            // 获取字段信息和统计
            Set<String> fieldNames = new HashSet<>();
            Map<String, Integer> fieldCounts = new HashMap<>();
            Map<String, Set<String>> fieldTypes = new HashMap<>();

            int maxDocsToCheck = Math.min(reader.maxDoc(), 200); // 增加检查的文档数量
            int checkedDocs = 0;

            for (int i = 0; i < reader.maxDoc() && checkedDocs < maxDocsToCheck; i++) {
                try {
                    // 检查文档是否被删除
                    if (isDocumentDeleted(reader, i)) {
                        continue;
                    }

                    Document doc = reader.document(i);
                    doc.getFields().forEach(field -> {
                        String name = field.name();
                        fieldNames.add(name);
                        fieldCounts.put(name, fieldCounts.getOrDefault(name, 0) + 1);

                        // 记录字段类型
                        String type = field.getClass().getSimpleName();
                        fieldTypes.computeIfAbsent(name, k -> new HashSet<>()).add(type);
                    });
                    checkedDocs++;

                    if (fieldNames.size() > 100) break; // 限制字段数量
                } catch (Exception e) {
                    // 如果无法读取文档，跳过
                    continue;
                }
            }

            stats.put("fields", new ArrayList<>(fieldNames));
            stats.put("fieldCounts", fieldCounts);
            stats.put("fieldTypes", fieldTypes);
            stats.put("checkedDocs", checkedDocs);

            // 计算索引健康度
            double healthScore = calculateIndexHealth(reader);
            stats.put("healthScore", healthScore);
        }

        return stats;
    }

    /**
     * 计算索引健康度
     */
    private double calculateIndexHealth(IndexReader reader) {
        try {
            int totalDocs = reader.maxDoc();
            int liveDocs = reader.numDocs();
            int deletedDocs = reader.numDeletedDocs();

            if (totalDocs == 0) return 100.0;

            // 健康度 = (活跃文档数 / 总文档数) * 100
            double health = ((double) liveDocs / totalDocs) * 100;
            return Math.round(health * 100.0) / 100.0; // 保留两位小数
        } catch (Exception e) {
            return 0.0;
        }
    }

    /**
     * 获取最近的文档
     */
    private List<Map<String, Object>> getRecentDocuments(int limit) throws IOException {
        List<Map<String, Object>> documents = new ArrayList<>();
        
        Directory directory = luceneEmbeddingStore.getDirectory();
        try (IndexReader reader = DirectoryReader.open(directory)) {
            IndexSearcher searcher = new IndexSearcher(reader);
            
            // 获取所有文档
            Query query = new MatchAllDocsQuery();
            TopDocs topDocs = searcher.search(query, limit);
            
            for (ScoreDoc scoreDoc : topDocs.scoreDocs) {
                Document doc = searcher.doc(scoreDoc.doc);
                
                Map<String, Object> docMap = new HashMap<>();
                docMap.put("id", doc.get("id"));
                docMap.put("text", truncateText(doc.get("text"), 100));
                docMap.put("metadata", doc.get("metadata"));
                docMap.put("docId", scoreDoc.doc);
                
                documents.add(docMap);
            }
        }
        
        return documents;
    }

    /**
     * 检查文档是否被删除
     */
    private boolean isDocumentDeleted(IndexReader reader, int docId) {
        try {
            for (LeafReaderContext context : reader.leaves()) {
                if (docId >= context.docBase && docId < context.docBase + context.reader().maxDoc()) {
                    int leafDocId = docId - context.docBase;
                    Bits liveDocs = context.reader().getLiveDocs();
                    if (liveDocs != null) {
                        return !liveDocs.get(leafDocId);
                    }
                    return false; // 如果没有删除信息，认为文档未删除
                }
            }
        } catch (Exception e) {
            log.warn("检查文档删除状态失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 知识库搜索 - 与对话功能一致的向量搜索
     */
    @PostMapping("/knowledge-search")
    @ResponseBody
    public Map<String, Object> knowledgeSearch(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();

        try {
            String query = (String) request.get("query");
            Integer maxResults = (Integer) request.getOrDefault("maxResults", 10);
            Double minScore = (Double) request.getOrDefault("minScore", 0.0);

            if (query == null || query.trim().isEmpty()) {
                result.put("success", false);
                result.put("error", "查询内容不能为空");
                return result;
            }

            log.info("🔍 执行知识库搜索: query='{}', maxResults={}, minScore={}", query, maxResults, minScore);

            // 1. 生成查询向量
            Embedding queryEmbedding = embeddingModel.embed(query).content();
            log.debug("📊 查询向量维度: {}", queryEmbedding.dimension());

            // 2. 执行向量搜索
            EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
                    .queryEmbedding(queryEmbedding)
                    .maxResults(maxResults)
                    .minScore(minScore)
                    .build();

            EmbeddingSearchResult<TextSegment> searchResult = luceneEmbeddingStore.search(searchRequest);

            // 3. 处理搜索结果
            List<Map<String, Object>> matches = new ArrayList<>();
            for (EmbeddingMatch<TextSegment> match : searchResult.matches()) {
                Map<String, Object> matchMap = new HashMap<>();

                // 基本信息
                matchMap.put("score", Math.round(match.score() * 10000.0) / 10000.0); // 保留4位小数
                matchMap.put("embeddingId", match.embeddingId());

                // 文本内容
                TextSegment segment = match.embedded();
                if (segment != null) {
                    matchMap.put("text", segment.text());
                    matchMap.put("textLength", segment.text().length());
                    matchMap.put("textPreview", truncateText(segment.text(), 200));

                    // 元数据
                    if (segment.metadata() != null) {
                        Map<String, Object> metadata = new HashMap<>();
                        try {
                            // 尝试获取常用字段
                            String source = segment.metadata().getString("source");
                            String title = segment.metadata().getString("title");
                            String type = segment.metadata().getString("type");

                            if (source != null) metadata.put("source", source);
                            if (title != null) metadata.put("title", title);
                            if (type != null) metadata.put("type", type);

                            matchMap.put("metadata", metadata);

                            // 提取常用字段
                            matchMap.put("source", source);
                            matchMap.put("title", title);
                            matchMap.put("type", type);
                        } catch (Exception e) {
                            log.debug("无法解析元数据: {}", e.getMessage());
                        }
                    }
                }

                matches.add(matchMap);
            }

            // 4. 构建响应
            result.put("success", true);
            result.put("query", query);
            result.put("totalMatches", matches.size());
            result.put("maxResults", maxResults);
            result.put("minScore", minScore);
            result.put("matches", matches);

            // 搜索统计
            Map<String, Object> stats = new HashMap<>();
            stats.put("avgScore", matches.stream()
                    .mapToDouble(m -> (Double) m.get("score"))
                    .average().orElse(0.0));
            stats.put("maxScore", matches.stream()
                    .mapToDouble(m -> (Double) m.get("score"))
                    .max().orElse(0.0));
            stats.put("minScore", matches.stream()
                    .mapToDouble(m -> (Double) m.get("score"))
                    .min().orElse(0.0));
            result.put("stats", stats);

            log.info("✅ 知识库搜索完成: 找到{}个匹配结果", matches.size());

        } catch (Exception e) {
            log.error("❌ 知识库搜索失败", e);
            result.put("success", false);
            result.put("error", "知识库搜索失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 截断文本
     */
    private String truncateText(String text, int maxLength) {
        if (text == null) return null;
        if (text.length() <= maxLength) return text;
        return text.substring(0, maxLength) + "...";
    }
}
