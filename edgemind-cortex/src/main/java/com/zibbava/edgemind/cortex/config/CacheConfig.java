package com.zibbava.edgemind.cortex.config;

import cn.dev33.satoken.dao.SaTokenDao;
import cn.dev33.satoken.dao.SaTokenDaoDefaultImpl;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 缓存配置 - 使用内存缓存
 * 替代Redis缓存，提供轻量级的内存缓存解决方案
 *
 * <AUTHOR>
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * 内存缓存管理器
     */
    @Bean
    @Primary
    public CacheManager cacheManager() {
        return new ConcurrentMapCacheManager(
            "userCache",
            "roleCache", 
            "permissionCache",
            "systemSettingsCache",
            "knowledgeSpaceCache"
        );
    }

    /**
     * Sa-Token内存存储
     */
    @Bean
    @Primary
    public SaTokenDao saTokenDao() {
        return new SaTokenDaoDefaultImpl();
    }
}
