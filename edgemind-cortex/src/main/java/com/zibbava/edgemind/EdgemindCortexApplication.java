package com.zibbava.edgemind;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.awt.*;
import java.net.URI;


/**
 * 端智 AI 助手主启动类
 * 轻量级启动，无需Docker和Redis依赖
 */
@SpringBootApplication
@MapperScan("com.zibbava.**.mapper")
@EnableScheduling
public class EdgemindCortexApplication {



	public static void main(String[] args) {
		System.out.println("EdgeMind-Cortex 启动中...");
		System.out.println("使用轻量级模式：H2数据库 + 内存缓存");

		// 强制启用GUI环境，确保Desktop API可用（适用于IDE和Maven运行）
		System.setProperty("java.awt.headless", "false");
		System.out.println("已强制启用GUI环境：java.awt.headless=false");

		// 启动Spring Boot应用
		ConfigurableApplicationContext context = SpringApplication.run(EdgemindCortexApplication.class, args);

		// 在应用启动后打开浏览器
		openBrowserOnStartup(context);
	}

	/**
	 * 在应用启动后打开浏览器
	 * @param context 应用上下文
	 */
	private static void openBrowserOnStartup(ConfigurableApplicationContext context) {
		// 获取端口和上下文路径
		String port = context.getEnvironment().getProperty("server.port", "8080");
		String contextPath = context.getEnvironment().getProperty("server.servlet.context-path", "/wkg");
		String url = String.format("http://localhost:%s%s/", port, contextPath);

		System.out.println("应用启动完成，正在打开浏览器访问: " + url);

		if (Desktop.isDesktopSupported()) {
			Desktop desktop = Desktop.getDesktop();
			System.out.println("Desktop.isSupported(BROWSE) = " + desktop.isSupported(Desktop.Action.BROWSE));
		}
		System.out.println("========================");

		// 使用新线程延迟打开浏览器，确保Web服务器完全启动
		new Thread(() -> {
			try {
				// 等待2秒确保服务器完全启动
				Thread.sleep(2000);
				// 直接使用Desktop API打开浏览器
				Desktop.getDesktop().browse(new URI(url));
				System.out.println("✅ 浏览器打开成功！");

			} catch (Exception e) {
			}
		}, "BrowserOpener").start();
	}
}
