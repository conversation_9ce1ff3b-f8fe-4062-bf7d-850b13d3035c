<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zibbava.edgemind.cortex.mapper.KnowledgeSpaceMapper">

    <!-- 根据用户角色查询可访问的知识空间 -->
    <select id="findAccessibleSpacesByRoles" resultType="com.zibbava.edgemind.cortex.entity.KnowledgeSpace">
        SELECT DISTINCT s.* FROM kb_knowledge_spaces s
        LEFT JOIN kb_space_roles sr ON s.space_id = sr.space_id
        WHERE s.access_type = 'PUBLIC'
           OR (s.access_type = 'PRIVATE' AND s.create_by = #{userId})
           OR (s.access_type = 'ROLE_BASED' AND sr.role_id IN
               <foreach collection="roleIds" item="roleId" open="(" close=")" separator=",">
                   #{roleId}
               </foreach>)
        ORDER BY s.create_time DESC
    </select>

</mapper>
