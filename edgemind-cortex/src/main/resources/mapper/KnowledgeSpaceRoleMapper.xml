<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zibbava.edgemind.cortex.mapper.KnowledgeSpaceRoleMapper">

    <!-- 检查角色是否有访问空间的权限 -->
    <select id="hasRoleAccess" resultType="boolean">
        SELECT COUNT(*) > 0 FROM kb_space_roles
        WHERE space_id = #{spaceId}
          AND role_id IN
          <foreach collection="roleIds" item="roleId" open="(" close=")" separator=",">
              #{roleId}
          </foreach>
          AND (
              (#{action} = 'read' AND permission_level IN ('read', 'write', 'admin'))
              OR (#{action} = 'write' AND permission_level IN ('write', 'admin'))
              OR (#{action} = 'admin' AND permission_level = 'admin')
          )
    </select>

</mapper>
