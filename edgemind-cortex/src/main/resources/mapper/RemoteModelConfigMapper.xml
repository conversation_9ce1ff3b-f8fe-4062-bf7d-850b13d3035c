<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zibbava.edgemind.cortex.mapper.RemoteModelConfigMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.zibbava.edgemind.cortex.entity.RemoteModelConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="model_id" property="modelId" jdbcType="VARCHAR"/>
        <result column="api_key" property="apiKey" jdbcType="VARCHAR"/>
        <result column="enabled" property="enabled" jdbcType="BOOLEAN"/>
        <result column="config_params" property="configParams" jdbcType="LONGVARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="last_used_time" property="lastUsedTime" jdbcType="TIMESTAMP"/>
        <result column="usage_count" property="usageCount" jdbcType="BIGINT"/>
        <result column="deleted" property="deleted" jdbcType="BOOLEAN"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, model_id, api_key, enabled, config_params, remark,
        create_time, update_time, last_used_time, usage_count, deleted
    </sql>

    <!-- 获取所有启用的远程模型配置 -->
    <select id="selectAllEnabled" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM remote_model_config
        WHERE enabled = 1
          AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据模型ID获取配置 -->
    <select id="selectByModelId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM remote_model_config
        WHERE model_id = #{modelId}
          AND deleted = 0
        LIMIT 1
    </select>

    <!-- 更新最后使用时间和使用次数 -->
    <update id="updateLastUsedTime">
        UPDATE remote_model_config
        SET last_used_time = NOW(),
            usage_count = COALESCE(usage_count, 0) + 1,
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
