<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 标题可以动态设置，或使用通用标题 -->
    <title>AI 工作台</title> 
    <!-- Common CSS -->
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css">
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css">
    <link rel="stylesheet" href="/wkg/css/base/main.css">
    <link rel="stylesheet" href="/wkg/css/components/sidebar.css">
    <!-- Page-specific CSS (Consider moving chat CSS to chat.html only) -->
    <link rel="stylesheet" href="/wkg/css/components/chat-area.css">
    <link rel="stylesheet" href="/wkg/css/components/chat-input-area.css">
    <link rel="stylesheet" href="/wkg/css/components/auth-modal.css">
    <link rel="stylesheet" href="/wkg/css/vendor/github.min.css">
</head>
<body>
    <script>
        // 在页面渲染前立即检查并应用侧边栏状态
        (function() {
             try {
                 const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
                 if (isCollapsed && window.innerWidth > 768) {
                     document.documentElement.style.setProperty('--initial-sidebar-width', 'var(--sidebar-width-collapsed)');
                     document.documentElement.style.setProperty('--initial-main-margin', 'var(--sidebar-width-collapsed)');
                     // 立即添加折叠类
                     document.documentElement.classList.add('sidebar-initially-collapsed');
                 } else {
                     document.documentElement.style.setProperty('--initial-sidebar-width', 'var(--sidebar-width-expanded)');
                     document.documentElement.style.setProperty('--initial-main-margin', 'var(--sidebar-width-expanded)');
                 }
             } catch(e) {
                 // 如果localStorage不可用，使用默认值
                 document.documentElement.style.setProperty('--initial-sidebar-width', 'var(--sidebar-width-expanded)');
                 document.documentElement.style.setProperty('--initial-main-margin', 'var(--sidebar-width-expanded)');
             }
         })();
    </script>
    <div class="app-container d-flex">
        <!-- 侧边栏组件 (始终显示) -->
        <div th:replace="~{fragments/_sidebar :: sidebar}"></div>

        <!-- 主内容区域 (使用 iframe) -->
        <div class="main-content flex-grow-1 d-flex flex-column" id="main-content-area">
             <!-- 认证状态也应放在 shell 中 -->
             <div th:replace="~{fragments/_authStatus :: authStatus}" class="p-2 border-bottom bg-light flex-shrink-0"></div>
             
             <!-- Iframe 加载内容页面 -->
             <iframe name="contentFrame"
                     id="contentFrame"
                     src="about:blank"
                     frameborder="0"
                     class="flex-grow-1 w-100 border-0">
                 您的浏览器不支持 iframe。
             </iframe>
        </div>
    </div>

    <!-- 全局 Modals 和 Toasts -->
    <div th:replace="~{fragments/_toastContainer :: toastContainer}"></div>
    <div th:replace="~{fragments/_authModal :: authModal}"></div>
    <div th:replace="~{fragments/_changePasswordModal :: changePasswordModal}"></div>

    <!-- Common JS (全局需要) -->
    <script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
    <script type="module" src="/wkg/js/shared/components/modal-component.js"></script>
    <script type="module" src="/wkg/js/features/auth/auth-modal.js"></script>
    <script type="module" src="/wkg/js/core/responsive-layout.js"></script>
    <script type="module" src="/wkg/js/core/page-loader.js"></script>
    
    <!-- 初始化和父子通信脚本 -->
    <script type="module">
        import { initialize as initializeResponsiveLayout } from '/wkg/js/core/responsive-layout.js';
        import { initialize as initializeAuthModal } from '/wkg/js/features/auth/auth-modal.js';
        import { initialize as initializePageLoader } from '/wkg/js/core/page-loader.js';
        import { ModalComponent, showToast } from '/wkg/js/shared/components/modal-component.js';
        import { checkLoginStatus } from '/wkg/js/shared/services/auth-service.js';

        // 全局状态变量
        let isGuestMode = false;
        let currentConversationId = null;

        // 处理来自iframe的消息
        function handleIframeMessage(event) {
            // 安全检查 (生产环境应当限制 origin)
            // if (event.origin !== window.origin) return;
            
            const data = event.data;
            if (!data || !data.type) return;
            
            const iframe = document.getElementById('contentFrame');
            if (!iframe || !iframe.contentWindow) {
                console.error("[Layout] iframe元素不存在");
                return;
            }
            
            switch (data.type) {
                case 'GET_CONVERSATION_STATE':
                    iframe.contentWindow.postMessage({
                        type: 'CONVERSATION_STATE',
                        payload: { isGuest: isGuestMode, currentConversationId: currentConversationId }
                    }, '*');
                    break;
                    
                case 'UPDATE_CURRENT_CONVERSATION':
                    if (data.payload && data.payload.conversationId) {
                        currentConversationId = data.payload.conversationId;
                    }
                    break;
                    
                case 'UPDATE_TITLE':
                    if (data.payload && data.payload.conversationId && data.payload.text) {
                        // 可能的标题更新API调用
                        try {
                            const convId = data.payload.conversationId;
                            const text = data.payload.text;
                            let newTitle = text.substring(0, 7).trim();
                            if (text.length > 7) newTitle += '...';
                            
                            // 检查是否有标题更新接口
                            fetch(`/wkg/api/conversations/${convId}/title`, {
                                method: 'PUT',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ title: newTitle })
                            }).then(response => {
                                if (response.ok) {
                                }
                            }).catch(error => {
                                console.error(`[Layout] 更新标题失败:`, error);
                            });
                        } catch (e) {
                            console.error(`[Layout] 处理标题更新请求出错:`, e);
                        }
                    }
                    break;
                    
                case 'SHOW_TOAST':
                    if (data.payload && data.payload.message) {
                        const type = data.payload.type || 'info';
                        showToast(data.payload.message, type);
                    }
                    break;
            }
        }

        document.addEventListener('DOMContentLoaded', async () => {
            // 初始化各模块
            initializeResponsiveLayout(); 
            initializeAuthModal();
            initializePageLoader(); // 初始化页面加载器
            
            // 检查登录状态 - 使用会话缓存减少API调用
            try {
                // 尝试从会话缓存获取登录状态
                const cachedState = sessionStorage.getItem('userLoginState');
                if (cachedState) {
                    const parsedState = JSON.parse(cachedState);
                    // 只使用短期有效的缓存（5分钟内）
                    if (parsedState && parsedState.timestamp && Date.now() - parsedState.timestamp < 300000) {
                        isGuestMode = !parsedState.isLoggedIn;
                    } else {
                        // 缓存已过期，删除并重新检查
                        sessionStorage.removeItem('userLoginState');
                        await checkAndCacheLoginState();
                    }
                } else {
                    // 无缓存，执行API检查
                    await checkAndCacheLoginState();
                }
            } catch (error) {
                console.error("[Layout] 处理登录状态时出错:", error);
                // 出错时默认为游客模式
                isGuestMode = true;
            }
            
            // 添加postMessage监听器
            window.addEventListener('message', handleIframeMessage);

            // 从URL获取会话ID和页面参数
            const urlParams = new URLSearchParams(window.location.search);
            const pageParam = urlParams.get('page');
            
            // 处理页面参数
            if (pageParam === 'license') {
                // 许可证页面不需要会话ID，清除相关状态
                currentConversationId = null;
                // 设置iframe加载许可证页面
                const iframe = document.getElementById('contentFrame');
                if (iframe) {
                    iframe.src = '/wkg/license/content';
                    // 监听加载完成事件，然后显示
                    iframe.addEventListener('load', function() {
                        iframe.style.opacity = '1';
                    }, { once: true });
                }
            } else {
                // 只有非许可证页面才处理会话ID
                const conversationIdParam = urlParams.get('conversationId');
                if (conversationIdParam && !isNaN(parseInt(conversationIdParam))) {
                    currentConversationId = parseInt(conversationIdParam);
                }
                // 非许可证页面加载默认的聊天内容
                const iframe = document.getElementById('contentFrame');
                if (iframe) {
                    iframe.src = '/wkg/chat/content';
                    iframe.addEventListener('load', function() {
                        iframe.style.opacity = '1';
                    }, { once: true });
                }
            }
            
            const iframe = document.getElementById('contentFrame');
            const sidebarLinks = document.querySelectorAll('.sidebar .nav-link[target="contentFrame"]');

            // Active Link 更新逻辑
            const updateActiveLink = (targetSrcOrPath) => {
                 let activePath = '/wkg/chat/content'; // Default
                 try {
                     if (targetSrcOrPath.startsWith('/')) {
                          activePath = targetSrcOrPath;
                     } else {
                          activePath = new URL(targetSrcOrPath, window.location.origin).pathname;
                     }
                     activePath = activePath.split('?')[0].split('#')[0];
                 } catch (e) {
                     console.warn("Error parsing targetSrcOrPath:", targetSrcOrPath, e);
                     const pathMatch = targetSrcOrPath.match(/\/wkg(\/[^?#]+)/);
                     if (pathMatch && pathMatch[1]) activePath = '/wkg' + pathMatch[1];
                 }
 
                 sidebarLinks.forEach(link => {
                     const linkPath = new URL(link.href, window.location.origin).pathname;
                     const isActive = (linkPath === activePath);
                     link.classList.toggle('active', isActive);
                 });
             };

            // Iframe 加载后更新链接状态
            if (iframe) {
                 iframe.addEventListener('load', () => {
                     // 显示iframe，避免闪烁
                     iframe.style.opacity = '1';
                     
                     let currentIframePath = iframe.src;
                     try {
                         if (iframe.contentWindow && iframe.contentWindow.location) {
                             currentIframePath = iframe.contentWindow.location.pathname + iframe.contentWindow.location.search + iframe.contentWindow.location.hash;
                         }
                     } catch (e) {
                         console.warn("Could not access iframe location.");
                     }
                     updateActiveLink(currentIframePath);
                 });
                 setTimeout(() => updateActiveLink(iframe.src), 150);
             } else {
                 console.error("Iframe 'contentFrame' not found.");
             }
        });
        
        // 检查并缓存登录状态的辅助函数
        async function checkAndCacheLoginState() {
            const result = await checkLoginStatus();
            isGuestMode = !result.isLoggedIn;
            
            // 缓存结果到会话存储
            try {
                sessionStorage.setItem('userLoginState', JSON.stringify({
                    isLoggedIn: result.isLoggedIn,
                    timestamp: Date.now()
                }));
            } catch (e) {
                console.warn("[Layout] 无法缓存登录状态:", e);
            }
            
            return result.isLoggedIn;
        }

        // 监听来自iframe的消息
        window.addEventListener('message', function(event) {
            // 验证消息来源（可选，增加安全性）
            if (event.origin !== window.location.origin) {
                return;
            }

            // 处理导航请求
            if (event.data && event.data.type === 'navigate') {
                const iframe = document.getElementById('contentFrame');
                if (iframe && event.data.url) {
                    console.log('[Layout] 收到导航请求:', event.data.url);
                    iframe.src = event.data.url;
                }
            }
        });
    </script>

    <!-- 百度统计 - 通用脚本 -->
    <script src="/wkg/js/shared/analytics.js"></script>

</body>
</html>