<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库</title>
    <!-- Vendor CSS -->
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css" />
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css" />
    <!-- 聊天组件样式 -->
    <link rel="stylesheet" href="/wkg/css/vendor/github.min.css" />
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-select.min.css" />
    <link rel="stylesheet" href="/wkg/css/components/chat-area.css" />
    <link rel="stylesheet" href="/wkg/css/components/chat-input-area.css" />
    <link rel="stylesheet" href="/wkg/css/components/chat-history.css" />
    <link rel="stylesheet" href="/wkg/css/components/markdown-styles.css" />
    <link rel="stylesheet" href="/wkg/css/components/code-block-styles.css" />
    <link rel="stylesheet" href="/wkg/css/components/loading-animation.css" />
    <link rel="stylesheet" href="/wkg/css/components/scrollbar-styles.css" />
    <link rel="stylesheet" href="/wkg/css/components/chat-main.css" />
    <link rel="stylesheet" href="/wkg/css/components/model-selector-custom.css" />
    <link rel="stylesheet" href="/wkg/css/components/deep-thinking-toggle.css" />
    <link rel="stylesheet" href="/wkg/css/components/enhanced-retrieval-toggle.css" />
    <link rel="stylesheet" href="/wkg/css/components/document-sources.css" />
    <!-- Custom CSS for this page -->
    <link rel="stylesheet" href="/wkg/css/features/knowledge/knowledge_base.css" />
    <link rel="stylesheet" href="/wkg/css/features/knowledge/node-create-modal.css" />
    <link rel="stylesheet" href="/wkg/css/components/modal-unified.css" />
    <!-- Dropzone.js CSS -->
    <link rel="stylesheet" href="/wkg/css/vendor/dropzone.min.css" />
    <!-- 文档查看器CSS -->
    <link rel="stylesheet" href="/wkg/css/components/document-viewer.css" />
</head>
<body th:data-knowledge-type="${knowledgeType}">

<!-- 页面主内容区 -->
<div class="page-content" id="pageContentArea">

    <!-- 添加用于存储知识库类型和是否需要初始化知识空间的隐藏字段 -->
    <input type="hidden" id="knowledgeType" th:value="${knowledgeType}">
    <input type="hidden" id="initSpace" th:value="${initSpace}">

    <!-- 2.1 左侧文档树侧边栏 -->
    <aside class="knowledge-sidebar" id="knowledgeSidebar">
        <!-- 顶部标题区域 -->
        <div class="sidebar-header mb-3">
            <div class="d-flex align-items-center justify-content-between">
                <h5 class="mb-0 text-primary" id="mainTitle">
                    <i class="bi bi-collection me-2" th:if="${knowledgeType == 'team'}"></i>
                    <i class="bi bi-person me-2" th:if="${knowledgeType == 'private'}"></i>
                    <span th:if="${knowledgeType == 'team'}">知识库</span>
                    <span th:if="${knowledgeType == 'private'}">个人库</span>
                </h5>
                <!-- 移除了创建空间按钮，通过空间管理页面统一管理 -->
            </div>
        </div>

        <!-- 知识库选择器和操作按钮区域 -->
        <div class="space-actions-section mb-3">
            <!-- 知识库选择器 - 只在团队知识库模式下显示 -->
            <div class="space-selector mb-2" id="spaceSelector" th:if="${knowledgeType == 'team'}">
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle w-100 text-start"
                            type="button" data-bs-toggle="dropdown" id="currentSpaceButton">
                        <i class="bi bi-folder me-2"></i>
                        <span id="currentSpaceName">选择知识库</span>
                    </button>
                    <ul class="dropdown-menu w-100" id="spaceDropdownMenu">
                        <!-- 动态加载空间列表 -->
                    </ul>
                </div>
            </div>

            <!-- 新建文档按钮 -->
            <div class="action-buttons">
                <button class="btn btn-primary btn-sm w-100" data-bs-toggle="modal" data-bs-target="#nodeCreateModal">
                    <i class="bi bi-file-earmark-plus me-2"></i>新建文档
                </button>
            </div>
        </div>
<!--        <div class="input-group input-group-sm mb-3">-->
<!--            <span class="input-group-text"><i class="bi bi-search"></i></span>-->
<!--            <input type="text" class="form-control" placeholder="搜索文档..." disabled>-->
<!--        </div>-->
        <!-- 文档树容器 -->
        <ul class="doc-tree flex-grow-1 overflow-auto" id="docTreeContainer">
            <li class="text-muted p-2">加载中...</li>
        </ul>
    </aside>

    <!-- 2.2 中间 AI 聊天侧边栏 -->
    <aside id="aiChatSidebarCenter" class="ai-chat-sidebar">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="aiChatSidebarLabelCenter">与 <span id="chatContextTitleCenter">[文档/文件夹]</span> 对话</h5>
            <button type="button" class="btn-close text-reset" aria-label="Close" id="closeChatSidebarCenterButton"></button>
        </div>
        <div class="offcanvas-body d-flex flex-column">
            <!-- 引入通用聊天组件 -->
            <div th:replace="fragments/chat_fragment :: chat_area(
                chatId='kb-chat',
                messagesDivId='kb-chat-messages',
                userInputId='chat-input',
                modelSelectId='model-select',
                uploadButtonId='upload-button',
                sendButtonId='send-button',
                pauseButtonId='pause-button',
                imageUploadId='image-upload',
                imagePreviewId='image-preview',
                welcomeMessage='你好！我是知识库 AI 助手。选择左侧文档或文件夹开始提问吧。'
            )">
            </div>
        </div>
    </aside>

    <!-- 2.3 右侧主内容区 -->
    <main class="knowledge-main" id="knowledgeMainArea">
        <div class="knowledge-main-header d-flex justify-content-between align-items-center">
            <h4 id="mainViewTitle" class="mb-0 text-truncate">选择节点</h4>
            <div class="d-flex align-items-center">
                <button id="smartParseButton" class="btn btn-sm smart-parse-btn" style="display: none;" title="点击重新解析文档">
                    <i class="bi bi-file-text parse-icon"></i>
                    <span class="parse-text">未解析</span>
                    <div class="parse-spinner d-none">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">解析中...</span>
                        </div>
                    </div>
                </button>
                <span id="docActionButtons" style="display: none;">
                    <button id="uploadButton" class="btn btn-sm btn-outline-primary me-2"><i class="bi bi-upload"></i> 上传</button>
                    <button id="editButton" class="btn btn-sm btn-outline-secondary me-2"><i class="bi bi-pencil-square"></i> 编辑</button>
                    <button id="saveButton" class="btn btn-sm btn-primary me-2 d-none"><i class="bi bi-save"></i> 保存</button>
                    <button id="cancelButton" class="btn btn-sm btn-secondary me-2 d-none">取消</button>
                </span>
            </div>
        </div>
        <div id="documentAreaWrapper">
            <!-- 开源文档查看器容器 -->
            <div id="documentViewer" style="height: 100%;"></div>
            <!-- 原始文档显示区域 (备用) -->
            <div id="documentArea" class="document-content-area d-none">
                <div id="documentView">
                </div>
                <div id="documentEdit" class="d-none">
                    <textarea id="documentEditTextArea" class="form-control" rows="15"></textarea>
                </div>
            </div>
        </div>
    </main>

</div>

<!-- 新建节点模态框 -->
<div class="modal fade" id="nodeCreateModal" tabindex="-1" aria-labelledby="nodeCreateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="nodeCreateForm">
                    <input type="hidden" id="createNodeParentIdInput">
                    <input type="hidden" id="createNodeSpaceIdInput">
                    <input type="hidden" id="createNodeTypeSelect" value="FOLDER">

                    <!-- 类型选择按钮组 -->
                    <div class="mb-4 text-center">
                        <div class="btn-group node-type-selector" role="group" aria-label="选择节点类型">
                            <button type="button" class="btn btn-outline-primary active" data-type="FOLDER">
                                <i class="bi bi-folder me-1"></i>文件夹
                            </button>
                            <button type="button" class="btn btn-outline-primary" data-type="FILE">
                                <i class="bi bi-file-earmark-text me-1"></i>文档
                            </button>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="createNodeNameInput" class="form-label">名称</label>
                        <input type="text" class="form-control" id="createNodeNameInput" required>
                    </div>

                    <!-- 文件夹视图 -->
                    <div id="folderView" class="node-type-view">
                        <div class="text-center p-4 mb-2 rounded bg-light">
                            <i class="bi bi-folder-plus" style="font-size: 3rem; color: #7eb8ff;"></i>
                            <p class="mt-2 mb-0">将创建新文件夹</p>
                        </div>
                    </div>

                    <!-- 文件上传视图 -->
                    <div id="fileView" class="node-type-view" style="display: none;">
                        <div class="dropzone-container mb-3">
                            <!-- 文件上传错误提示 -->
                            <div id="uploadErrorAlert" class="alert alert-danger mb-2 d-none" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <span id="uploadErrorMessage">上传错误信息</span>
                            </div>
                            <div id="documentDropzone" class="dropzone">
                                <div class="dz-message">
                                    <i class="bi bi-cloud-arrow-up" style="font-size: 2rem;"></i>
                                    <p>拖放文件到此处或点击上传</p>
                                    <p class="text-muted small">支持格式: Word、PDF、Markdown、文本、Excel等常见文档格式</p>
                                </div>
                            </div>
                            <input type="file" class="d-none" id="fileUploadInput" accept=".pdf,.docx,.doc,.md,.txt,.html,.htm,.rtf,.odt,.xml,.mht,.xps,.djvu,.xlsx,.xls,.csv,.ods,.tsv,.pptx,.ppt,.odp">
                        </div>
                    </div>

                    <div class="mb-3" id="createNodeParentInfo" style="display: none;">
                        <small class="text-muted">将在 <span id="createNodeParentNameLabel" class="fw-bold">...</span> 下创建</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="submit" class="btn btn-primary" form="nodeCreateForm">创建</button>
            </div>
        </div>
    </div>
</div>

<!-- 重命名节点模态框 -->
<div class="modal fade modal-unified modal-rename" id="nodeRenameModal" tabindex="-1" aria-labelledby="nodeRenameModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="nodeRenameModalLabel">重命名节点</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="nodeRenameForm">
                    <input type="hidden" id="renameNodeIdInput">
                    <div class="mb-3">
                        <label for="renameNodeNameInput" class="form-label">名称</label>
                        <input type="text" class="form-control form-control-sm" id="renameNodeNameInput" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">取消</button>
                <button type="submit" class="btn btn-primary btn-sm" form="nodeRenameForm">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 文件上传模态框 -->
<div class="modal fade" id="fileUploadModal" tabindex="-1" aria-labelledby="fileUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fileUploadModalLabel">上传文档</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="fileUploadForm">
                    <input type="hidden" id="uploadNodeIdInput">
                    <div class="mb-3">
                        <label for="uploadFileInput" class="form-label">选择文件</label>
                        <input type="file" class="form-control" id="uploadFileInput" accept=".pdf,.docx,.doc,.md,.txt,.html,.htm,.rtf,.odt,.xml,.mht,.xps,.djvu,.xlsx,.xls,.csv,.ods,.tsv,.pptx,.ppt,.odp" required>
                        <div class="form-text">支持格式: Word、PDF、Markdown、文本、Excel等常见文档格式</div>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="updateNodeNameCheckbox" checked>
                        <label class="form-check-label" for="updateNodeNameCheckbox">使用完整文件名（包含扩展名）更新节点名称</label>
                    </div>
                    <div class="mb-3">
                        <label id="uploadTargetLabel" class="form-text">上传到: <span id="uploadTargetName" class="fw-bold"></span></label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">取消</button>
                <button type="submit" class="btn btn-primary btn-sm" form="fileUploadForm">上传</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除节点确认模态框 -->
<div class="modal fade modal-unified modal-delete" id="nodeDeleteModal" tabindex="-1" aria-labelledby="nodeDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="nodeDeleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="deleteConfirmMessage"></p>
                <div id="deleteWarningSection" class="mt-2 text-danger small"></div>
                <input type="hidden" id="deleteNodeIdInput">
                <input type="hidden" id="deleteNodeNameInput">
                <input type="hidden" id="deleteNodeTypeInput">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger btn-sm" id="confirmDeleteButton">删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 重新解析确认模态框 -->
<div class="modal fade" id="reparseConfirmModal" tabindex="-1" aria-labelledby="reparseConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reparseConfirmModalLabel">确认重新解析</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要重新解析文档 "<strong id="reparseConfirmNodeName"></strong>" 吗？</p>
                <p class="text-muted small">此操作将重新提取文档内容并生成新的向量索引，旧的索引数据将被覆盖。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary btn-sm" id="confirmReparseButton">确认解析</button>
            </div>
        </div>
    </div>
</div>

<!-- 移除了创建知识空间模态框，通过空间管理页面统一管理 -->

<!-- Toast Container -->
<div th:replace="fragments/_toastContainer :: toastContainer"></div>

<!-- JavaScript 引用 -->
<!-- 引入开源文档查看器依赖 -->
<script src="/wkg/js/vendor/pdf.min.js"></script>
<script src="/wkg/js/vendor/jszip.min.js"></script>
<script src="/wkg/js/vendor/docx-preview.min.js"></script>
<script src="/wkg/js/vendor/marked.min.js"></script>
<script src="/wkg/js/vendor/xlsx.full.min.js"></script>
<script src="/wkg/js/vendor/jquery-3.7.1.min.js"></script>
<script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
<script src="/wkg/js/vendor/highlight.min.js"></script>
<script src="/wkg/js/vendor/bootstrap-select.min.js"></script>
<!-- 聊天组件脚本 -->
<script src="/wkg/js/features/chat/code-block-handler.js"></script>
<script type="module">
    // 这个内联模块用于桥接知识库聊天组件与知识库页面
    import { initializeKnowledgeChat, updateKnowledgeChatNode } from '/wkg/js/features/knowledge/knowledge-chat.js';

    // 将这两个函数挂载到window对象，使普通脚本可以访问
    window.initializeKnowledgeChat = initializeKnowledgeChat;
    window.updateKnowledgeChatNode = updateKnowledgeChatNode;

    console.log("知识库聊天桥接模块已加载，函数已挂载到window对象");
</script>
<script type="module" src="/wkg/js/shared/lib/streaming-markdown.js"></script>
<script type="module" src="/wkg/js/shared/components/deep-thinking-toggle.js"></script>
<script type="module" src="/wkg/js/shared/components/tool-calling-toggle.js"></script>
<script type="module" src="/wkg/js/shared/components/enhanced-retrieval-toggle.js"></script>
<script type="module" src="/wkg/js/features/chat/model-selector-component.js"></script>
<!-- 确保message-handler.js被引入，它包含了处理消息的核心逻辑 -->
<script type="module" src="/wkg/js/features/chat/message-handler.js"></script>

<!-- 将知识库脚本改为普通脚本，通过window对象访问知识库聊天组件函数 -->
<!-- Dropzone.js -->
<script>
    // 在加载Dropzone.js之前禁用自动发现功能
    window.Dropzone = { autoDiscover: false };
</script>
<script src="/wkg/js/vendor/dropzone.min.js"></script>
<script>
    // 再次确保禁用自动发现
    if (typeof Dropzone !== 'undefined') {
        Dropzone.autoDiscover = false;
        console.log('已禁用Dropzone自动发现功能');
    }
</script>
<!-- 文档查看器组件 -->
<script src="/wkg/js/components/document-viewer.js"></script>
<script src="/wkg/js/features/knowledge/knowledge_base.js"></script>
<script src="/wkg/js/features/knowledge/node-create-modal.js"></script>

<!-- 百度统计 - 通用脚本 -->
<script src="/wkg/js/shared/analytics.js"></script>

</body>
</html>