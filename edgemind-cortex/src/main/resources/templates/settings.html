<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置</title>
    <!-- 引入 Bootstrap 和其他通用样式 -->
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css" />
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css" />
    <link rel="stylesheet" href="/wkg/css/components/scrollbar-styles.css" />
    <link rel="stylesheet" href="/wkg/css/features/settings/settings.css" />
</head>
<body>
    <div class="settings-container">
        <div class="page-header">
            <h1>系统设置</h1>
        </div>

        <!-- 系统信息区块 -->
        <div class="settings-section">
            <div class="settings-card">
                <div class="card-header">
                    <h5 class="card-title">系统信息</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-3">
                            <span class="fw-medium text-secondary">系统版本</span>
                        </div>
                        <div class="col-9">
                            <span id="systemVersion" class="text-primary fw-medium">1.0.0</span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-3">
                            <span class="fw-medium text-secondary">系统标识</span>
                        </div>
                        <div class="col-9">
                            <div class="d-flex align-items-center">
                                <span id="systemIdentifier" class="me-2 text-primary fw-medium"></span>
                                <button id="copyIdentifierBtn" class="btn btn-sm btn-outline-secondary" title="复制标识码">
                                    <i class="bi bi-clipboard"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 知识库设置区块 -->
        <div class="settings-section">
            <div class="settings-card">
                <div class="card-header">
                    <h5 class="card-title">知识库设置</h5>
                </div>
                <div class="card-body">
                    <!-- 使用说明 -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">功能说明</h6>
                        <p class="mb-2">设置知识库存储路径，点击同步即可将本地文档一键同步到端智AI助手。</p>
                        <p class="mb-0">支持格式：Word(.docx)、PDF(.pdf)、Excel(.xlsx)、文本(.txt)</p>
                    </div>

                    <form id="knowledgeBaseSettingsForm">
                        <div class="mb-3">
                            <label for="storagePath" class="form-label settings-form-label">知识库存储路径</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="storagePath" placeholder="请输入知识库文件存储路径" autocomplete="off">
                                <button class="btn btn-primary" type="button" id="syncButton">
                                    <i class="bi bi-arrow-repeat me-1"></i> 同步
                                </button>
                            </div>
                            <div class="form-text">指定知识库文件的存储根目录</div>
                        </div>

                        <div class="mb-3">
                            <label for="personalStoragePath" class="form-label settings-form-label">个人库存储路径</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="personalStoragePath" placeholder="请输入个人库文件存储路径" autocomplete="off">
                                <button class="btn btn-outline-secondary" type="button" id="savePersonalPathButton">
                                    <i class="bi bi-save me-1"></i> 保存
                                </button>
                            </div>
                            <div class="form-text">指定个人库文件的存储根目录</div>
                        </div>

                        <!-- 同步状态信息 -->
                        <div class="sync-status-section">
                            <h6 class="section-title">同步状态</h6>
                            <div class="row mb-3">
                                <div class="col-3">
                                    <span class="fw-medium text-secondary">当前状态</span>
                                </div>
                                <div class="col-9">
                                    <span id="syncStatus" class="text-primary fw-medium">未同步</span>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-3">
                                    <span class="fw-medium text-secondary">最后同步时间</span>
                                </div>
                                <div class="col-9">
                                    <span id="lastSyncTime" class="text-primary fw-medium">无</span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 同步确认模态框 -->
        <div class="modal fade" id="syncConfirmModal" tabindex="-1" aria-labelledby="syncConfirmModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-sm">
                <div class="modal-content">
                    <div class="modal-header border-0 pb-0">
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body text-center pt-0">
                        <div class="mb-3">
                            <i class="bi bi-arrow-repeat text-primary" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="mb-3">确认同步操作</h5>
                        <p class="text-muted mb-4">此操作将清除现有数据并重新同步知识库</p>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-primary" id="confirmSyncBtn">
                                <i class="bi bi-check-lg me-2"></i>确认同步
                            </button>
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                取消
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- 引入 Bootstrap JS 和其他脚本 -->
    <script src="/wkg/js/vendor/jquery-3.7.1.min.js"></script>
    <script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
    <script src="/wkg/js/features/settings/settings.js"></script>

    <!-- 添加Toast容器 -->
    <div th:replace="fragments/_toastContainer :: toastContainer"></div>

    <!-- 百度统计 - 通用脚本 -->
    <script src="/wkg/js/shared/analytics.js"></script>
</body>
</html>
