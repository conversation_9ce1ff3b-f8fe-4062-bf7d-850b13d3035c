<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色管理 - 端智AI助手</title>
    <!-- 使用端智AI助手本地资源 -->
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css">
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css">
    <!-- 端智AI助手设计系统样式 -->
    <link rel="stylesheet" href="/wkg/css/base/main.css">
    <link rel="stylesheet" href="/wkg/css/components/modal-unified.css">
</head>
<body style="background-color: #f8f9fa; padding: 20px;">
    <div class="container-fluid">
        <!-- 页面标题 - 使用端智AI助手标准样式 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="text-primary fw-semibold mb-0">
                <i class="bi bi-people-fill me-2"></i>
                角色管理
            </h2>

        </div>

        <!-- 搜索表单 - 使用EdgeMind卡片样式 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body">
                <form id="searchForm" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label fw-medium" style="color: var(--text-primary);">角色名称</label>
                        <input type="text" class="form-control border-1" id="searchRoleName"
                               placeholder="请输入角色名称" style="border-color: var(--border-color);">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label fw-medium" style="color: var(--text-primary);">角色编码</label>
                        <input type="text" class="form-control border-1" id="searchRoleCode"
                               placeholder="请输入角色编码" style="border-color: var(--border-color);">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label fw-medium" style="color: var(--text-primary);">状态</label>
                        <select class="form-select border-1" id="searchStatus" style="border-color: var(--border-color);">
                            <option value="">全部状态</option>
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary px-4"
                                style="background-color: var(--primary-color); border-color: var(--primary-color);">
                            <i class="bi bi-search me-2"></i>搜索
                        </button>
                        <button type="button" class="btn btn-outline-secondary ms-2 px-3" onclick="resetSearch()">
                            <i class="bi bi-arrow-clockwise me-2"></i>重置
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 操作按钮组 - 使用EdgeMind按钮样式 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body">
                <div class="d-flex flex-wrap gap-2">
                    <button type="button" class="btn btn-success px-4" onclick="showCreateModal()">
                        <i class="bi bi-plus-lg me-2"></i>新增角色
                    </button>
                    <button type="button" class="btn btn-danger px-4" onclick="batchDelete()" disabled id="batchDeleteBtn">
                        <i class="bi bi-trash me-2"></i>批量删除
                    </button>
                </div>
            </div>
        </div>

        <!-- 角色列表表格 - 使用EdgeMind表格样式 -->
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0">
                        <thead style="background-color: var(--primary-light);">
                            <tr>
                                <th class="fw-semibold" style="color: var(--text-primary);">
                                    <input type="checkbox" class="form-check-input" id="selectAll">
                                </th>
                                <th class="fw-semibold" style="color: var(--text-primary);">角色名称</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">角色编码</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">描述</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">状态</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">用户数量</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">创建时间</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">操作</th>
                            </tr>
                        </thead>
                        <tbody id="roleTableBody">
                            <!-- 角色数据将通过JS动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 - 使用EdgeMind分页样式 -->
                <nav aria-label="角色列表分页" class="mt-4">
                    <ul class="pagination justify-content-center mb-0" id="pagination">
                        <!-- 分页按钮将通过JS动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 创建/编辑角色模态框 - 使用EdgeMind模态框样式 -->
    <div class="modal fade modal-unified" id="roleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content border-0 shadow">
                <div class="modal-header" style="background-color: var(--primary-light); border-bottom: 1px solid var(--border-color);">
                    <h5 class="modal-title fw-semibold" id="roleModalTitle" style="color: var(--text-primary);">
                        <i class="bi bi-person-plus me-2"></i>新增角色
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="roleForm">
                        <input type="hidden" id="roleId">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">
                                    角色名称 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control border-1" id="roleName" required
                                       style="border-color: var(--border-color);">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">
                                    角色编码 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control border-1" id="roleCode" required
                                       style="border-color: var(--border-color);">
                                <div class="form-text" style="color: var(--text-secondary);">
                                    角色编码只能包含大写字母和下划线
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">状态</label>
                                <select class="form-select border-1" id="status" style="border-color: var(--border-color);">
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">描述</label>
                                <textarea class="form-control border-1" id="description" rows="3"
                                          style="border-color: var(--border-color);"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="background-color: #f8f9fa; border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn btn-outline-secondary px-4" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary px-4" onclick="saveRole()"
                            style="background-color: var(--primary-color); border-color: var(--primary-color);">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 权限分配模态框 - 使用EdgeMind模态框样式 -->
    <div class="modal fade modal-unified" id="permissionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content border-0 shadow">
                <div class="modal-header" style="background-color: #e8f5e8; border-bottom: 1px solid var(--border-color);">
                    <h5 class="modal-title fw-semibold" style="color: var(--text-primary);">
                        <i class="bi bi-shield-check me-2"></i>权限分配
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="permissionRoleId">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <label class="form-label fw-medium mb-0" style="color: var(--text-primary);">权限列表</label>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="expandAll()">
                                    <i class="bi bi-arrows-expand me-1"></i>展开
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAll()">
                                    <i class="bi bi-arrows-collapse me-1"></i>收起
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" onclick="checkAll()">
                                    <i class="bi bi-check-all me-1"></i>全选
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-warning" onclick="uncheckAll()">
                                    <i class="bi bi-x-square me-1"></i>取消
                                </button>
                            </div>
                        </div>
                        <div class="p-3 rounded" id="permissionTree"
                             style="max-height: 400px; overflow-y: auto; border: 1px solid var(--border-color); background-color: var(--hover-bg);">
                            <!-- 权限树将通过JS动态生成 -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="background-color: #f8f9fa; border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn btn-outline-secondary px-4" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success px-4" onclick="savePermissions()">保存权限</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 复制角色模态框 - 使用EdgeMind模态框样式 -->
    <div class="modal fade modal-unified" id="copyRoleModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content border-0 shadow">
                <div class="modal-header" style="background-color: #fff3cd; border-bottom: 1px solid #ffeaa7;">
                    <h5 class="modal-title fw-semibold text-warning">
                        <i class="bi bi-files me-2"></i>复制角色
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="copyRoleForm">
                        <input type="hidden" id="copySourceRoleId">
                        <div class="mb-3">
                            <label class="form-label fw-medium" style="color: var(--text-primary);">
                                新角色名称 <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control border-1" id="newRoleName" required
                                   style="border-color: var(--border-color);">
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-medium" style="color: var(--text-primary);">
                                新角色编码 <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control border-1" id="newRoleCode" required
                                   style="border-color: var(--border-color);">
                            <div class="form-text" style="color: var(--text-secondary);">
                                角色编码只能包含大写字母和下划线
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="background-color: #f8f9fa; border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn btn-outline-secondary px-4" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning px-4" onclick="copyRole()">确认复制</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 使用EdgeMind本地JavaScript资源 -->
    <script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
    <script type="module" src="/wkg/js/shared/components/modal-component.js"></script>
    <script>
        // 导入EdgeMind Toast组件
        import { showToast } from '/wkg/js/shared/components/modal-component.js';
        // 将showToast函数暴露到全局作用域
        window.showToast = showToast;
    </script>
    <script src="/wkg/js/system/role-management.js"></script>

    <!-- 百度统计 - 通用脚本 -->
    <script src="/wkg/js/shared/analytics.js"></script>
</body>
</html>
