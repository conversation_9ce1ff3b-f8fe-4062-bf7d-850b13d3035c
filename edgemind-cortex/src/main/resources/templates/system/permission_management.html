<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限查看 - 端智AI助手</title>
    <!-- 使用端智AI助手本地资源 -->
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css">
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css">
    <!-- 端智AI助手设计系统样式 -->
    <link rel="stylesheet" href="/wkg/css/base/main.css">
    <link rel="stylesheet" href="/wkg/css/components/modal-unified.css">
</head>
<body style="background-color: #f8f9fa; padding: 20px;">
<div class="container-fluid">
    <!-- 页面标题 - 使用端智AI助手标准样式 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="text-primary fw-semibold mb-0">
            <i class="bi bi-shield-check me-2"></i>
            权限查看
        </h2>

    </div>

    <!-- 说明信息 -->
    <div class="alert alert-info border-0 mb-4"
         style="background-color: #e8f4fd; border-left: 4px solid var(--primary-color) !important;">
        <i class="bi bi-info-circle me-2"></i>
        <strong>权限查看模式</strong>：此页面仅用于查看系统权限结构，不支持编辑操作。权限配置请联系系统管理员。
    </div>
    <!-- 搜索功能 -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-4">
                    <label class="form-label fw-medium" style="color: var(--text-primary);">权限名称</label>
                    <input type="text" class="form-control border-1" id="searchPermissionName"
                           placeholder="请输入权限名称" style="border-color: var(--border-color);">
                </div>
                <div class="col-md-4">
                    <label class="form-label fw-medium" style="color: var(--text-primary);">权限类型</label>
                    <select class="form-select border-1" id="searchPermissionType"
                            style="border-color: var(--border-color);">
                        <option value="">全部类型</option>
                        <option value="MENU">菜单</option>
                        <option value="BUTTON">按钮</option>
                        <option value="API">接口</option>
                    </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="button" class="btn btn-primary px-4 me-2" onclick="searchPermissions()"
                            style="background-color: var(--primary-color); border-color: var(--primary-color);">
                        <i class="bi bi-search me-2"></i>搜索
                    </button>
                    <button type="button" class="btn btn-outline-secondary px-3" onclick="resetSearch()">
                        <i class="bi bi-arrow-clockwise me-2"></i>重置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 权限树 -->
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="fw-semibold mb-0" style="color: var(--text-primary);">
                            <i class="bi bi-diagram-3 me-2"></i>权限树结构
                        </h5>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="expandAll()">
                                <i class="bi bi-arrows-expand me-1"></i>展开全部
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="collapseAll()">
                                <i class="bi bi-arrows-collapse me-1"></i>收起全部
                            </button>
                        </div>
                    </div>
                    <div id="permissionTreeContainer" style="max-height: 600px; overflow-y: auto;">
                        <!-- 权限树将通过JS动态生成 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 权限详情 -->
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="fw-semibold mb-3" style="color: var(--text-primary);">
                        <i class="bi bi-info-circle me-2"></i>权限详情
                    </h5>
                    <div id="permissionDetail">
                        <div class="text-center text-muted py-5">
                            <i class="bi bi-cursor-text" style="font-size: 3rem; color: var(--text-secondary);"></i>
                            <p class="mt-2" style="color: var(--text-secondary);">请选择一个权限查看详情</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 使用EdgeMind本地JavaScript资源 -->
<script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
<script type="module" src="/wkg/js/shared/components/modal-component.js"></script>
<script src="/wkg/js/system/permission-management.js"></script>

<!-- 百度统计 - 通用脚本 -->
<script src="/wkg/js/shared/analytics.js"></script>
</body>
</html>
