<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部门管理 - 端智AI助手</title>
    <!-- 使用EdgeMind本地资源 -->
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css">
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css">
    <!-- EdgeMind设计系统样式 -->
    <link rel="stylesheet" href="/wkg/css/base/main.css">
    <link rel="stylesheet" href="/wkg/css/components/modal-unified.css">
</head>
<body style="background-color: #f8f9fa; padding: 20px;">
    <div class="container-fluid">
        <!-- 页面标题 - 使用EdgeMind标准样式 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="text-primary fw-semibold mb-0">
                <i class="bi bi-diagram-3 me-2"></i>
                部门管理
            </h2>

        </div>
        <div class="row g-3">
            <!-- 部门树 -->
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body d-flex flex-column">
                        <div class="d-flex justify-content-between align-items-center mb-3 flex-shrink-0">
                            <h5 class="fw-semibold mb-0" style="color: var(--text-primary);">
                                <i class="bi bi-diagram-3 me-2"></i>组织架构
                            </h5>
                            <div class="d-flex gap-2 flex-wrap">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="expandAll()">
                                    <i class="bi bi-arrows-expand me-1"></i>展开全部
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAll()">
                                    <i class="bi bi-arrows-collapse me-1"></i>收起全部
                                </button>
                                <button type="button" class="btn btn-sm btn-success" onclick="showCreateModal()">
                                    <i class="bi bi-plus-lg me-1"></i>新增部门
                                </button>
                            </div>
                        </div>
                        <div id="deptTreeContainer" class="flex-grow-1" style="min-height: 400px; max-height: 600px; overflow-y: auto;">
                            <!-- 部门树将通过JS动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 部门详情 -->
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body d-flex flex-column">
                        <h5 class="fw-semibold mb-3 flex-shrink-0" style="color: var(--text-primary);">
                            <i class="bi bi-info-circle me-2"></i>部门详情
                        </h5>
                        <div id="deptDetail" class="flex-grow-1">
                            <div class="text-center text-muted py-5">
                                <i class="bi bi-building" style="font-size: 3rem; color: var(--text-secondary);"></i>
                                <p class="mt-2" style="color: var(--text-secondary);">请选择一个部门查看详情</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建/编辑部门模态框 - 使用EdgeMind模态框样式 -->
    <div class="modal fade modal-unified" id="deptModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content border-0 shadow">
                <div class="modal-header" style="background-color: var(--primary-light); border-bottom: 1px solid var(--border-color);">
                    <h5 class="modal-title fw-semibold" id="deptModalTitle" style="color: var(--text-primary);">
                        <i class="bi bi-building-add me-2"></i>新增部门
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="deptForm">
                        <input type="hidden" id="deptId">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">
                                    部门名称 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control border-1" id="deptName" required
                                       style="border-color: var(--border-color);">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">
                                    部门编码 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control border-1" id="deptCode" required
                                       style="border-color: var(--border-color);">
                                <div class="form-text" style="color: var(--text-secondary);">
                                    部门编码必须唯一
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">上级部门</label>
                                <select class="form-select border-1" id="parentId" style="border-color: var(--border-color);">
                                    <option value="0">顶级部门</option>
                                    <!-- 父部门选项将通过JS动态加载 -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">部门负责人</label>
                                <select class="form-select border-1" id="managerId" style="border-color: var(--border-color);">
                                    <option value="">请选择负责人</option>
                                    <!-- 用户选项将通过JS动态加载 -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">联系电话</label>
                                <input type="tel" class="form-control border-1" id="phone"
                                       style="border-color: var(--border-color);">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">邮箱</label>
                                <input type="email" class="form-control border-1" id="email"
                                       style="border-color: var(--border-color);">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">排序</label>
                                <input type="number" class="form-control border-1" id="sortOrder" value="0"
                                       style="border-color: var(--border-color);">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">状态</label>
                                <select class="form-select border-1" id="status" style="border-color: var(--border-color);">
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">部门地址</label>
                                <input type="text" class="form-control border-1" id="address"
                                       style="border-color: var(--border-color);">
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">部门描述</label>
                                <textarea class="form-control border-1" id="description" rows="3"
                                          style="border-color: var(--border-color);"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="background-color: #f8f9fa; border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn btn-outline-secondary px-4" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary px-4" onclick="saveDept()"
                            style="background-color: var(--primary-color); border-color: var(--primary-color);">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap标准树形组件样式 -->
    <style>
        /* 使用Bootstrap标准的树形结构样式 */

        /* 树形节点基础样式 */
        .tree-node {
            position: relative;
            margin-bottom: 2px;
        }

        /* 树形节点内容 */
        .tree-node-content {
            display: flex;
            align-items: center;
            padding: 0.5rem 0.75rem;
            border: 1px solid transparent;
            border-radius: 0.375rem;
            cursor: pointer;
            transition: all 0.2s ease;
            background-color: #fff;
        }

        .tree-node-content:hover {
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        /* 选中状态 */
        .tree-node-content.active {
            background-color: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
            border-left: 4px solid #2196f3;
        }

        /* 禁用状态 */
        .tree-node-content.disabled {
            opacity: 0.6;
            background-color: #f8f9fa;
            cursor: not-allowed;
        }

        /* 展开/收起图标 */
        .tree-toggle {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;
            cursor: pointer;
            border-radius: 0.25rem;
            transition: all 0.2s ease;
        }

        .tree-toggle:hover {
            background-color: #e9ecef;
        }

        .tree-toggle i {
            font-size: 0.875rem;
            transition: transform 0.2s ease;
        }

        .tree-toggle.expanded i {
            transform: rotate(90deg);
        }

        /* 树形内容区域 */
        .tree-content {
            flex: 1;
            min-width: 0;
            display: flex;
            align-items: center;
        }

        /* 部门信息 */
        .dept-info {
            flex: 1;
            min-width: 0;
        }

        .dept-name {
            font-weight: 500;
            color: #212529;
            margin-bottom: 0.25rem;
        }

        .dept-meta {
            font-size: 0.875rem;
            color: #6c757d;
        }

        /* 操作按钮组 */
        .tree-actions {
            opacity: 0;
            transition: opacity 0.2s ease;
            margin-left: 0.75rem;
        }

        .tree-node-content:hover .tree-actions {
            opacity: 1;
        }

        /* 子节点容器 */
        .tree-children {
            margin-left: 1.5rem;
            border-left: 1px dashed #dee2e6;
            padding-left: 0.75rem;
            margin-top: 0.25rem;
        }

        .tree-children.collapsed {
            display: none;
        }

        /* 按钮样式优化 */
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            line-height: 1.25;
        }

        /* 徽章样式 */
        .badge {
            font-size: 0.75rem;
        }

        /* 图标样式 */
        .tree-icon {
            width: 16px;
            height: 16px;
            margin-right: 0.5rem;
            color: #0d6efd;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .tree-children {
                margin-left: 1rem;
                padding-left: 0.5rem;
            }

            .tree-actions .btn {
                padding: 0.125rem 0.25rem;
                font-size: 0.75rem;
            }

            .tree-actions .btn i {
                font-size: 0.75rem;
            }
        }
    </style>

    <!-- 使用EdgeMind本地JavaScript资源 -->
    <script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
    <script type="module" src="/wkg/js/shared/components/modal-component.js"></script>
    <script>
        // 导入EdgeMind Toast组件
        import { showToast } from '/wkg/js/shared/components/modal-component.js';
        // 将showToast函数暴露到全局作用域
        window.showToast = showToast;
    </script>
    <script src="/wkg/js/system/department-management.js"></script>

    <!-- 百度统计 - 通用脚本 -->
    <script src="/wkg/js/shared/analytics.js"></script>
</body>
</html>
