<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lucene数据管理 - EdgeMind Cortex</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .document-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .document-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .field-badge {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 2px 8px;
            margin: 2px;
            font-size: 0.8em;
        }
        
        .search-container {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .text-preview {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 10px;
            margin: 10px 0;
            border-radius: 0 4px 4px 0;
        }
        
        .metadata-json {
            background-color: #2d3748;
            color: #e2e8f0;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .error-message {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .document-card.border-danger {
            border-color: #dc3545 !important;
            background-color: #fff5f5 !important;
        }

        .progress {
            background-color: rgba(255, 255, 255, 0.3);
        }

        .text-xs {
            font-size: 0.7rem;
        }

        .field-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .metadata-json {
            max-height: 300px;
            overflow-y: auto;
        }

        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .knowledge-search-result {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .knowledge-search-result:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .score-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }

        .btn-check:checked + .btn-outline-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
        }

        .btn-check:checked + .btn-outline-success {
            background-color: #198754;
            border-color: #198754;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3">
                <div class="d-flex flex-column vh-100 p-3">
                    <h4 class="mb-4">
                        <i class="bi bi-database"></i> Lucene数据管理
                    </h4>
                    
                    <!-- 统计信息 -->
                    <div class="stats-card" th:if="${stats}">
                        <h6><i class="bi bi-bar-chart"></i> 索引统计</h6>
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="h4" th:text="${stats.totalDocs}">0</div>
                                <small>活跃文档</small>
                            </div>
                            <div class="col-4">
                                <div class="h4" th:text="${stats.deletedDocs}">0</div>
                                <small>已删除</small>
                            </div>
                            <div class="col-4">
                                <div class="h4" th:text="${stats.numSegments}">0</div>
                                <small>段数量</small>
                            </div>
                        </div>

                        <!-- 健康度指示器 -->
                        <div class="mb-3" th:if="${stats.healthScore}">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <small><strong>索引健康度</strong></small>
                                <small th:text="${stats.healthScore} + '%'">100%</small>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar"
                                     th:style="'width: ' + ${stats.healthScore} + '%; background-color: ' + (${stats.healthScore} >= 90 ? '#28a745' : (${stats.healthScore} >= 70 ? '#ffc107' : '#dc3545'))"
                                     th:attr="aria-valuenow=${stats.healthScore}"></div>
                            </div>
                        </div>

                        <hr class="my-3">
                        <div class="small">
                            <div><strong>最大文档ID:</strong> <span th:text="${stats.maxDoc}">0</span></div>
                            <div><strong>已检查文档:</strong> <span th:text="${stats.checkedDocs}">0</span></div>
                            <div><strong>索引路径:</strong> <span th:text="${stats.indexPath}" class="text-break">-</span></div>
                        </div>
                    </div>
                    
                    <!-- 字段列表 -->
                    <div class="card" th:if="${stats != null and stats.fields != null}">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-tags"></i> 索引字段
                                <span class="badge bg-secondary ms-2" th:text="${#lists.size(stats.fields)}">0</span>
                            </h6>
                        </div>
                        <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                            <div th:each="field : ${stats.fields}" class="mb-1">
                                <span class="field-badge d-inline-flex align-items-center">
                                    <span th:text="${field}">field</span>
                                    <span class="badge bg-primary ms-2 text-xs"
                                          th:if="${stats.fieldCounts != null and stats.fieldCounts[field] != null}"
                                          th:text="${stats.fieldCounts[field]}">0</span>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="mt-auto">
                        <button class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="refreshData()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新数据
                        </button>
                        <a href="/admin" class="btn btn-outline-secondary btn-sm w-100">
                            <i class="bi bi-arrow-left"></i> 返回管理
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="col-md-9">
                <div class="p-3">
                    <!-- 搜索区域 -->
                    <div class="search-container">
                        <!-- 搜索模式切换 -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0"><i class="bi bi-search"></i> 文档搜索</h5>
                            <div class="btn-group" role="group">
                                <input type="radio" class="btn-check" name="searchMode" id="basicSearch" checked>
                                <label class="btn btn-outline-primary btn-sm" for="basicSearch">基础搜索</label>

                                <input type="radio" class="btn-check" name="searchMode" id="knowledgeSearch">
                                <label class="btn btn-outline-success btn-sm" for="knowledgeSearch">知识库搜索</label>
                            </div>
                        </div>

                        <!-- 基础搜索 -->
                        <div id="basicSearchPanel">
                            <div class="row">
                                <div class="col-md-8">
                                    <input type="text" id="searchQuery" class="form-control"
                                           placeholder="输入搜索关键词（留空显示所有文档）">
                                </div>
                                <div class="col-md-2">
                                    <select id="pageSize" class="form-select">
                                        <option value="10">10条/页</option>
                                        <option value="20" selected>20条/页</option>
                                        <option value="50">50条/页</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-primary w-100" onclick="searchDocuments()">
                                        <i class="bi bi-search"></i> 搜索
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 知识库搜索 -->
                        <div id="knowledgeSearchPanel" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="text" id="knowledgeQuery" class="form-control"
                                           placeholder="输入问题或关键词进行语义搜索">
                                </div>
                                <div class="col-md-2">
                                    <select id="maxResults" class="form-select">
                                        <option value="5">5个结果</option>
                                        <option value="10" selected>10个结果</option>
                                        <option value="20">20个结果</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <input type="number" id="minScore" class="form-control"
                                           placeholder="最低分数" min="0" max="1" step="0.1" value="0.3">
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-success w-100" onclick="searchKnowledge()">
                                        <i class="bi bi-brain"></i> 语义搜索
                                    </button>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        知识库搜索使用AI向量匹配，可以理解语义相似性，与对话功能使用相同的搜索算法
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 搜索结果 -->
                    <div id="searchResults">
                        <!-- 最近文档 -->
                        <div th:if="${recentDocs != null}">
                            <h5><i class="bi bi-clock-history"></i> 最近文档</h5>
                            <div th:each="doc : ${recentDocs}" class="document-card">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <span class="badge bg-secondary me-2" th:text="'ID: ' + ${doc.id}">ID</span>
                                            <span class="badge bg-info" th:text="'Doc: ' + ${doc.docId}">DocID</span>
                                        </h6>
                                        <div class="text-preview" th:if="${doc.text}">
                                            <small th:text="${doc.text}">文档内容预览...</small>
                                        </div>
                                        <div th:if="${doc.metadata}">
                                            <small class="text-muted">元数据: </small>
                                            <code th:text="${doc.metadata}">metadata</code>
                                        </div>
                                    </div>
                                    <div class="ms-3">
                                        <button class="btn btn-sm btn-outline-primary" 
                                                th:onclick="'viewDocument(' + ${doc.docId} + ')'">
                                            <i class="bi bi-eye"></i> 查看
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分页 -->
                    <div id="pagination" class="d-flex justify-content-center mt-4">
                        <!-- 分页按钮将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 文档详情模态框 -->
    <div class="modal fade" id="documentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">文档详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="documentDetails">
                    <!-- 文档详情内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 错误提示 -->
    <div th:if="${error != null}" class="error-message">
        <i class="bi bi-exclamation-triangle"></i> <span th:text="${error}">错误信息</span>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let currentPage = 0;
        let currentQuery = '';
        let currentPageSize = 20;
        
        // 搜索文档
        function searchDocuments(page = 0) {
            const query = document.getElementById('searchQuery').value;
            const pageSize = document.getElementById('pageSize').value;
            
            currentPage = page;
            currentQuery = query;
            currentPageSize = parseInt(pageSize);
            
            const resultsContainer = document.getElementById('searchResults');
            resultsContainer.innerHTML = '<div class="loading"><i class="bi bi-hourglass-split"></i> 搜索中...</div>';
            
            // 使用管理API路径
            fetch(`/wkg/admin/lucene/search?query=${encodeURIComponent(query)}&page=${page}&size=${pageSize}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success === false || data.error) {
                        resultsContainer.innerHTML = `<div class="error-message">${data.error || '搜索失败'}</div>`;
                        return;
                    }

                    displaySearchResults(data);
                    displayPagination(data);
                })
                .catch(error => {
                    console.error('搜索失败:', error);
                    resultsContainer.innerHTML = `<div class="error-message">搜索失败: ${error.message}</div>`;
                });
        }
        
        // 显示搜索结果
        function displaySearchResults(data) {
            const resultsContainer = document.getElementById('searchResults');

            if (data.documents.length === 0) {
                resultsContainer.innerHTML = '<div class="text-center text-muted p-4">没有找到匹配的文档</div>';
                return;
            }

            let html = `<h5><i class="bi bi-file-text"></i> 搜索结果 (共 ${data.totalHits} 条)</h5>`;

            data.documents.forEach(doc => {
                // 确定删除状态的样式
                const deletedClass = doc.deleted ? 'border-danger bg-light' : '';
                const deletedBadge = doc.deleted ? '<span class="badge bg-danger me-2">已删除</span>' : '';

                // 提取有用的字段信息
                let title = '';
                let source = '';
                let type = '';

                // 尝试从allFields中提取标题、来源等信息
                if (doc.allFields) {
                    title = doc.allFields.title || doc.allFields.name || doc.allFields.filename || '';
                    source = doc.allFields.source || doc.allFields.path || '';
                    type = doc.allFields.type || doc.allFields.fileType || '';
                }

                // 尝试从metadata中提取信息
                if (!title && doc.metadata) {
                    try {
                        const metadata = JSON.parse(doc.metadata);
                        title = metadata.title || metadata.name || metadata.filename || '';
                        source = source || metadata.source || metadata.path || '';
                        type = type || metadata.type || metadata.fileType || '';
                    } catch (e) {
                        // 忽略JSON解析错误
                    }
                }

                html += `
                    <div class="document-card ${deletedClass}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-2">
                                    <span class="badge bg-secondary me-2">ID: ${doc.id || 'N/A'}</span>
                                    <span class="badge bg-info me-2">Doc: ${doc.docId}</span>
                                    ${deletedBadge}
                                    ${doc.score ? `<span class="badge bg-success">Score: ${doc.score.toFixed(3)}</span>` : ''}
                                    ${type ? `<span class="badge bg-warning text-dark">${type}</span>` : ''}
                                </h6>

                                ${title ? `
                                    <div class="mb-2">
                                        <h6 class="text-primary mb-1">
                                            <i class="bi bi-file-text me-1"></i>${title}
                                        </h6>
                                        ${source ? `<small class="text-muted"><i class="bi bi-folder me-1"></i>${source}</small>` : ''}
                                    </div>
                                ` : ''}

                                ${doc.text ? `
                                    <div class="text-preview mb-2">
                                        <small><strong>内容预览:</strong> ${doc.text}</small>
                                    </div>
                                ` : ''}

                                ${doc.metadata ? `
                                    <div class="mb-2">
                                        <small class="text-muted"><strong>元数据:</strong></small>
                                        <code class="d-block mt-1" style="font-size: 0.8em; max-height: 60px; overflow-y: auto;">${doc.metadata}</code>
                                    </div>
                                ` : ''}

                                ${doc.allFields && Object.keys(doc.allFields).length > 0 ? `
                                    <div class="mb-2">
                                        <small class="text-muted"><strong>字段信息:</strong></small>
                                        <div class="mt-1">
                                            ${Object.entries(doc.allFields)
                                                .filter(([key, value]) => !['title', 'name', 'filename', 'source', 'path', 'type', 'fileType'].includes(key))
                                                .slice(0, 5) // 限制显示数量
                                                .map(([key, value]) =>
                                                    `<span class="badge bg-light text-dark me-1 mb-1" title="${key}: ${value}">${key}: ${value.length > 20 ? value.substring(0, 20) + '...' : value}</span>`
                                                ).join('')}
                                            ${Object.keys(doc.allFields).length > 8 ? '<span class="badge bg-secondary">...</span>' : ''}
                                        </div>
                                    </div>
                                ` : ''}
                            </div>
                            <div class="ms-3 d-flex flex-column">
                                <button class="btn btn-sm btn-outline-primary mb-2" onclick="viewDocument(${doc.docId})">
                                    <i class="bi bi-eye"></i> 查看详情
                                </button>
                                ${doc.deleted ?
                                    '<small class="text-danger"><i class="bi bi-trash"></i> 已删除</small>' :
                                    '<small class="text-success"><i class="bi bi-check-circle"></i> 正常</small>'
                                }
                            </div>
                        </div>
                    </div>
                `;
            });

            resultsContainer.innerHTML = html;
        }
        
        // 显示分页
        function displayPagination(data) {
            const paginationContainer = document.getElementById('pagination');
            
            if (data.totalPages <= 1) {
                paginationContainer.innerHTML = '';
                return;
            }
            
            let html = '<nav><ul class="pagination">';
            
            // 上一页
            if (data.page > 0) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="searchDocuments(${data.page - 1})">上一页</a></li>`;
            }
            
            // 页码
            const startPage = Math.max(0, data.page - 2);
            const endPage = Math.min(data.totalPages - 1, data.page + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                const active = i === data.page ? 'active' : '';
                html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="searchDocuments(${i})">${i + 1}</a></li>`;
            }
            
            // 下一页
            if (data.page < data.totalPages - 1) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="searchDocuments(${data.page + 1})">下一页</a></li>`;
            }
            
            html += '</ul></nav>';
            paginationContainer.innerHTML = html;
        }
        
        // 查看文档详情
        function viewDocument(docId) {
            const modal = new bootstrap.Modal(document.getElementById('documentModal'));
            const detailsContainer = document.getElementById('documentDetails');

            detailsContainer.innerHTML = '<div class="loading"><i class="bi bi-hourglass-split"></i> 加载中...</div>';
            modal.show();

            // 使用管理API路径
            fetch(`/wkg/admin/lucene/document/${docId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success === false || data.error) {
                        detailsContainer.innerHTML = `<div class="error-message">${data.error || '获取文档详情失败'}</div>`;
                        return;
                    }

                    displayDocumentDetails(data.document);
                })
                .catch(error => {
                    console.error('获取文档详情失败:', error);
                    detailsContainer.innerHTML = `<div class="error-message">获取文档详情失败: ${error.message}</div>`;
                });
        }
        
        // 显示文档详情
        function displayDocumentDetails(doc) {
            const detailsContainer = document.getElementById('documentDetails');

            // 确定删除状态的样式
            const statusBadge = doc.deleted ?
                '<span class="badge bg-danger">已删除</span>' :
                '<span class="badge bg-success">正常</span>';

            // 提取标题和基本信息
            let title = '';
            let source = '';
            let type = '';
            let size = '';
            let createTime = '';

            // 尝试从各个字段中提取信息
            Object.keys(doc).forEach(key => {
                const value = doc[key];
                if (!value) return;

                const lowerKey = key.toLowerCase();
                if (!title && (lowerKey.includes('title') || lowerKey.includes('name') || lowerKey.includes('filename'))) {
                    title = value;
                } else if (!source && (lowerKey.includes('source') || lowerKey.includes('path'))) {
                    source = value;
                } else if (!type && (lowerKey.includes('type') || lowerKey.includes('format'))) {
                    type = value;
                } else if (!size && (lowerKey.includes('size') || lowerKey.includes('length'))) {
                    size = value;
                } else if (!createTime && (lowerKey.includes('time') || lowerKey.includes('date') || lowerKey.includes('created'))) {
                    createTime = value;
                }
            });

            // 尝试从metadata中提取信息
            if (doc.metadata) {
                try {
                    const metadata = JSON.parse(doc.metadata);
                    title = title || metadata.title || metadata.name || metadata.filename || '';
                    source = source || metadata.source || metadata.path || '';
                    type = type || metadata.type || metadata.fileType || '';
                    size = size || metadata.size || '';
                    createTime = createTime || metadata.createTime || metadata.timestamp || '';
                } catch (e) {
                    // 忽略JSON解析错误
                }
            }

            let html = `
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="bi bi-file-text text-primary me-2"></i>
                            ${title || '文档详情'}
                        </h5>
                        ${statusBadge}
                    </div>

                    <table class="table table-sm table-bordered">
                        <tr>
                            <td class="fw-bold" style="width: 25%;">文档ID</td>
                            <td>${doc.docId}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">唯一ID</td>
                            <td>${doc.id || '<span class="text-muted">N/A</span>'}</td>
                        </tr>
                        ${title ? `
                        <tr>
                            <td class="fw-bold">标题</td>
                            <td>${title}</td>
                        </tr>
                        ` : ''}
                        ${type ? `
                        <tr>
                            <td class="fw-bold">类型</td>
                            <td><span class="badge bg-info">${type}</span></td>
                        </tr>
                        ` : ''}
                        ${source ? `
                        <tr>
                            <td class="fw-bold">来源</td>
                            <td><code>${source}</code></td>
                        </tr>
                        ` : ''}
                        ${size ? `
                        <tr>
                            <td class="fw-bold">大小</td>
                            <td>${size}</td>
                        </tr>
                        ` : ''}
                        ${createTime ? `
                        <tr>
                            <td class="fw-bold">创建时间</td>
                            <td>${createTime}</td>
                        </tr>
                        ` : ''}
                        <tr>
                            <td class="fw-bold">删除状态</td>
                            <td>${doc.deleted ?
                                '<span class="text-danger"><i class="bi bi-x-circle"></i> 已删除</span>' :
                                '<span class="text-success"><i class="bi bi-check-circle"></i> 正常</span>'
                            }</td>
                        </tr>
                    </table>
                </div>
            `;

            // 按类型分组显示字段
            const systemFields = ['docId', 'id', 'deleted'];
            const contentFields = ['text'];
            const metadataFields = ['metadata'];

            // 显示内容字段
            contentFields.forEach(key => {
                if (doc[key]) {
                    html += `
                        <div class="mb-4">
                            <h6><i class="bi bi-file-text"></i> 文档内容</h6>
                            <div class="text-preview" style="max-height: 300px; overflow-y: auto;">
                                ${doc[key]}
                            </div>
                        </div>
                    `;
                }
            });

            // 显示元数据字段
            metadataFields.forEach(key => {
                if (doc[key]) {
                    try {
                        const metadata = JSON.parse(doc[key]);
                        html += `
                            <div class="mb-4">
                                <h6><i class="bi bi-info-circle"></i> 元数据</h6>
                                <div class="metadata-json">${JSON.stringify(metadata, null, 2)}</div>
                            </div>
                        `;
                    } catch (e) {
                        html += `
                            <div class="mb-4">
                                <h6><i class="bi bi-info-circle"></i> 元数据</h6>
                                <div class="text-preview">${doc[key]}</div>
                            </div>
                        `;
                    }
                }
            });

            // 显示其他字段
            const otherFields = Object.keys(doc).filter(key =>
                !systemFields.includes(key) &&
                !contentFields.includes(key) &&
                !metadataFields.includes(key) &&
                doc[key]
            );

            if (otherFields.length > 0) {
                html += `
                    <div class="mb-4">
                        <h6><i class="bi bi-tags"></i> 其他字段</h6>
                        <div class="row">
                `;

                otherFields.forEach(key => {
                    const value = doc[key];
                    html += `
                        <div class="col-md-6 mb-3">
                            <div class="card">
                                <div class="card-header py-2">
                                    <small class="fw-bold">${key}</small>
                                </div>
                                <div class="card-body py-2">
                                    <small class="text-break">${value}</small>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            }

            detailsContainer.innerHTML = html;
        }
        
        // 刷新数据
        function refreshData() {
            location.reload();
        }
        
        // 知识库搜索功能
        function searchKnowledge() {
            const query = document.getElementById('knowledgeQuery').value;
            const maxResults = parseInt(document.getElementById('maxResults').value);
            const minScore = parseFloat(document.getElementById('minScore').value) || 0.0;

            if (!query.trim()) {
                alert('请输入搜索内容');
                return;
            }

            const resultsContainer = document.getElementById('searchResults');
            resultsContainer.innerHTML = '<div class="loading"><i class="bi bi-hourglass-split"></i> 正在进行语义搜索...</div>';

            const requestBody = {
                query: query,
                maxResults: maxResults,
                minScore: minScore
            };

            fetch('/wkg/admin/lucene/knowledge-search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success === false || data.error) {
                    resultsContainer.innerHTML = `<div class="error-message">${data.error || '知识库搜索失败'}</div>`;
                    return;
                }

                displayKnowledgeSearchResults(data);
            })
            .catch(error => {
                console.error('知识库搜索失败:', error);
                resultsContainer.innerHTML = `<div class="error-message">知识库搜索失败: ${error.message}</div>`;
            });
        }

        // 显示知识库搜索结果
        function displayKnowledgeSearchResults(data) {
            const resultsContainer = document.getElementById('searchResults');

            if (!data.matches || data.matches.length === 0) {
                resultsContainer.innerHTML = '<div class="text-center text-muted p-4">没有找到相关的知识内容</div>';
                return;
            }

            let html = `
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="bi bi-brain text-success"></i> 知识库搜索结果 (共 ${data.totalMatches} 条)</h5>
                    <div class="text-muted small">
                        查询: "${data.query}" |
                        平均分数: ${data.stats.avgScore.toFixed(3)} |
                        最高分数: ${data.stats.maxScore.toFixed(3)}
                    </div>
                </div>
            `;

            data.matches.forEach((match, index) => {
                const scoreColor = match.score >= 0.8 ? 'success' : match.score >= 0.6 ? 'warning' : 'secondary';
                const scorePercentage = Math.round(match.score * 100);

                html += `
                    <div class="document-card border-start border-${scoreColor} border-3">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="badge bg-${scoreColor} me-2">${scorePercentage}% 匹配</span>
                                    <span class="badge bg-secondary me-2">ID: ${match.embeddingId || 'N/A'}</span>
                                    ${match.type ? `<span class="badge bg-info">${match.type}</span>` : ''}
                                </div>

                                ${match.title ? `
                                    <h6 class="text-primary mb-2">
                                        <i class="bi bi-file-text me-1"></i>${match.title}
                                    </h6>
                                ` : ''}

                                ${match.source ? `
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="bi bi-folder me-1"></i>${match.source}
                                        </small>
                                    </div>
                                ` : ''}

                                <div class="text-preview mb-2">
                                    <small><strong>内容:</strong> ${match.textPreview || match.text}</small>
                                </div>

                                ${match.metadata && Object.keys(match.metadata).length > 0 ? `
                                    <div class="mb-2">
                                        <small class="text-muted"><strong>元数据:</strong></small>
                                        <div class="mt-1">
                                            ${Object.entries(match.metadata).slice(0, 3).map(([key, value]) =>
                                                `<span class="badge bg-light text-dark me-1">${key}: ${value}</span>`
                                            ).join('')}
                                        </div>
                                    </div>
                                ` : ''}

                                <div class="small text-muted">
                                    <i class="bi bi-info-circle"></i>
                                    文本长度: ${match.textLength} 字符 |
                                    相似度分数: ${match.score.toFixed(4)}
                                </div>
                            </div>
                            <div class="ms-3 text-center">
                                <div class="progress mb-2" style="width: 60px; height: 60px; border-radius: 50%;">
                                    <div class="progress-bar bg-${scoreColor}"
                                         style="width: ${scorePercentage}%; height: 100%; border-radius: 50%;">
                                    </div>
                                </div>
                                <small class="text-muted">${scorePercentage}%</small>
                            </div>
                        </div>
                    </div>
                `;
            });

            resultsContainer.innerHTML = html;

            // 清空分页（知识库搜索不需要分页）
            document.getElementById('pagination').innerHTML = '';
        }

        // 切换搜索模式
        function toggleSearchMode() {
            const basicMode = document.getElementById('basicSearch').checked;
            const basicPanel = document.getElementById('basicSearchPanel');
            const knowledgePanel = document.getElementById('knowledgeSearchPanel');

            if (basicMode) {
                basicPanel.style.display = 'block';
                knowledgePanel.style.display = 'none';
            } else {
                basicPanel.style.display = 'none';
                knowledgePanel.style.display = 'block';
            }

            // 清空搜索结果
            document.getElementById('searchResults').innerHTML = '';
            document.getElementById('pagination').innerHTML = '';
        }

        // 页面加载时自动搜索
        document.addEventListener('DOMContentLoaded', function() {
            // 绑定搜索模式切换
            document.getElementById('basicSearch').addEventListener('change', toggleSearchMode);
            document.getElementById('knowledgeSearch').addEventListener('change', toggleSearchMode);

            // 绑定回车键搜索
            document.getElementById('searchQuery').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchDocuments();
                }
            });

            document.getElementById('knowledgeQuery').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchKnowledge();
                }
            });

            // 如果没有最近文档，自动执行一次搜索
            if (!document.querySelector('.document-card')) {
                searchDocuments();
            }
        });
    </script>

    <!-- 百度统计 - 通用脚本 -->
    <script src="/wkg/js/shared/analytics.js"></script>
</body>
</html>
