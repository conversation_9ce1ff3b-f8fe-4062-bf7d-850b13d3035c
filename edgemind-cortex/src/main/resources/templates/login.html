<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 端智AI助手</title>
    
    <!-- Bootstrap CSS -->
    <link th:href="@{/css/vendor/bootstrap.min.css}" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link th:href="@{/css/vendor/bootstrap-icons.css}" rel="stylesheet">
    <!-- 主样式 -->
    <link th:href="@{/css/base/main.css}" rel="stylesheet">
    <!-- 登录页面样式 -->
    <link th:href="@{/css/features/login/login-page.css}" rel="stylesheet">

    <link rel="icon" th:href="@{/images/ai-assistant-logo.ico}" type="image/x-icon">
</head>
<body class="login-body">
    <div class="login-container">
        <!-- 背景装饰 -->
        <div class="login-background">
            <div class="bg-shape shape-1"></div>
            <div class="bg-shape shape-2"></div>
            <div class="bg-shape shape-3"></div>
        </div>
        
        <!-- 登录卡片 -->
        <div class="login-card">
            <!-- Logo和标题 -->
            <div class="login-header">
                <div class="logo-container">
                    <img th:src="@{/images/ai-assistant-logo.svg}" alt="端智AI助手" width="64" height="64">
                </div>
                <h1 class="login-title">端智AI助手</h1>
                <p class="login-subtitle">智能办公，高效协作</p>
            </div>
            
            <!-- 首次启动密码提示 -->
            <div th:if="${isFirstStart}" class="alert alert-info mb-4" style="border-radius: 12px;">
                <div class="d-flex align-items-center mb-3">
                    <i class="bi bi-info-circle-fill me-2" style="font-size: 1.2rem;"></i>
                    <strong>首次启动提示</strong>
                </div>

                <!-- 管理员账号 -->
                <div class="mb-2">
                    <label class="form-label mb-1"><strong>管理员账号：</strong></label>
                    <code class="d-block" style="background: #f8f9fa; padding: 0.5rem; border-radius: 6px; font-size: 1.1rem;">admin</code>
                </div>

                <!-- 管理员密码 -->
                <div class="mb-3">
                    <label class="form-label mb-1"><strong>管理员密码：</strong></label>
                    <code class="d-block" style="background: #f8f9fa; padding: 0.5rem; border-radius: 6px; font-size: 1.1rem;" th:text="${generatedPassword}">密码</code>
                </div>

                <div class="alert alert-warning mt-2 mb-0" style="border-radius: 8px; ">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-exclamation-triangle-fill me-2" style="color: #ff6b35; font-size: 1.1rem;"></i>
                        <strong>请务必保存此信息，登录后当前提示消失</strong>
                    </div>
                </div>
            </div>

            <!-- 登录表单 -->
            <div class="login-form-container">
                <form id="loginForm" class="login-form">
                    <!-- 用户名输入 -->
                    <div class="form-group">
                        <label for="username" class="form-label">用户名</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-person-fill"></i>
                            </span>
                            <input type="text"
                                   class="form-control"
                                   id="username"
                                   name="username"
                                   placeholder="请输入用户名"
                                   value="admin"
                                   required>
                        </div>
                    </div>
                    
                    <!-- 密码输入 -->
                    <div class="form-group">
                        <label for="password" class="form-label">密码</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-lock-fill"></i>
                            </span>
                            <input type="password"
                                   class="form-control"
                                   id="password"
                                   name="password"
                                   placeholder="请输入密码"
                                   value="duanzhiadmin@123"
                                   required>
                            <button type="button" class="btn btn-outline-secondary password-toggle" id="passwordToggle">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 记住我 -->
                    <div class="form-group form-check-container">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="rememberMe" name="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                记住登录状态
                            </label>
                        </div>
                    </div>
                    
                    <!-- 错误信息 -->
                    <div id="errorMessage" class="alert alert-danger" style="display: none;" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <span id="errorText"></span>
                    </div>
                    
                    <!-- 登录按钮 -->
                    <button type="submit" class="btn btn-primary btn-login" id="loginButton">
                        <span class="btn-text">
                            <i class="bi bi-box-arrow-in-right me-2"></i>
                            登录
                        </span>
                        <span class="btn-loading" style="display: none;">
                            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            登录中...
                        </span>
                    </button>
                </form>
            </div>
            
            <!-- 页脚信息 -->
            <div class="login-footer">
                <p class="text-muted">
                    登录即表示您同意我们的
                    <a href="#" class="text-decoration-none">服务条款</a>
                    和
                    <a href="#" class="text-decoration-none">隐私政策</a>
                </p>
            </div>
        </div>
        
        <!-- 版权信息 -->
        <div class="copyright">
            <p>&copy; 2024 端智AI助手. 保留所有权利.</p>
        </div>
    </div>
    
    <!-- 隐藏字段：重定向URL -->
    <input type="hidden" id="redirectUrl" th:value="${redirect}" />
    
    <!-- JavaScript -->
    <script th:src="@{/js/vendor/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/features/login/login-page.js}"></script>



    <!-- 百度统计 - 通用脚本 -->
    <script src="/wkg/js/shared/analytics.js"></script>
</body>
</html>
