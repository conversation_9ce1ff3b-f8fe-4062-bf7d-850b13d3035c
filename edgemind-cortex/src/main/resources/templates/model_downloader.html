<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型中心 - 本地推理与云端API模型管理</title>
    <!-- Vendor CSS -->
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css" />
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css" />
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-select.min.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/wkg/css/features/model-downloader/model-downloader.css" />
    <link rel="stylesheet" href="/wkg/css/components/scrollbar-styles.css" />
</head>
<body>
<!-- 主要内容区 -->
<div class="container-fluid mt-4 mb-4">
    <div class="row">
        <!-- 左侧栏：系统信息和过滤器 -->
        <div class="col-lg-3 col-md-4 sidebar-column">
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">系统概览</h5>
                </div>
                <div class="card-body">
                    <div id="system-info">
                        <div class="loader-sm"></div>
                        <p class="text-muted text-center mt-2 small">正在检测系统信息...</p>
                    </div>
                </div>
            </div>

            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">筛选模型</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="size-filter" class="form-label">模型大小</label>
                        <select class="form-select" id="size-filter">
                            <option value="all" selected>所有大小</option>
                            <option value="small">小型 (< 5GB)</option>
                            <option value="medium">中型 (5-10GB)</option>
                            <option value="large">大型 (> 10GB)</option>
                        </select>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="show-recommended">
                        <label class="form-check-label" for="show-recommended">
                            优先推荐
                        </label>
                    </div>

                    <!-- 模型类型筛选 -->
                    <div class="mt-3">
                        <label class="form-label small fw-bold">模型类型</label>
                        <select class="form-select form-select-sm" id="model-type-filter">
                            <option value="all">全部模型</option>
                            <option value="local">本地模型</option>
                            <option value="remote">远程模型</option>
                        </select>
                    </div>

                    <!-- 远程模型提供商筛选 -->
                    <div class="mt-3" id="provider-filter-container" style="display: none;">
                        <label class="form-label small fw-bold">提供商</label>
                        <select class="form-select form-select-sm" id="provider-filter">
                            <option value="all">全部提供商</option>
                            <option value="deepseek">DeepSeek</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧内容：模型列表 -->
        <div class="col-lg-9 col-md-8 main-content-column">
            <div class="card shadow-sm">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center flex-wrap mb-3">
                        <h5 class="card-title mb-0 me-3">
                            <i class="bi bi-cpu me-2"></i>模型中心
                        </h5>
                        <div class="d-flex gap-2 mt-2 mt-md-0">
                            <div class="input-group input-group-sm search-bar">
                                <input type="text" class="form-control" id="search-model" placeholder="搜索名称或ID...">
                                <button class="btn btn-outline-secondary" type="button" id="refresh-btn" title="刷新列表">
                                    <i class="bi bi-arrow-repeat"></i>
                                </button>
                            </div>
                            <button type="button" class="btn btn-outline-success btn-sm" id="refresh-remote-btn" title="刷新云端API模型集成">
                                <i class="bi bi-cloud-arrow-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 标签页导航 -->
                    <ul class="nav nav-tabs card-header-tabs" id="model-tabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="local-models-tab" data-bs-toggle="tab" data-bs-target="#local-models" type="button" role="tab" aria-controls="local-models" aria-selected="true">
                                <i class="bi bi-hdd-stack me-2"></i>本地模型
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="remote-models-tab" data-bs-toggle="tab" data-bs-target="#remote-models" type="button" role="tab" aria-controls="remote-models" aria-selected="false">
                                <i class="bi bi-cloud me-2"></i>云端模型
                            </button>
                        </li>
                    </ul>
                </div>

                <div class="card-body">
                    <!-- 标签页内容 -->
                    <div class="tab-content" id="model-tabs-content">
                        <!-- 本地推理模型标签页 -->
                        <div class="tab-pane fade show active" id="local-models" role="tabpanel" aria-labelledby="local-models-tab">
                            <div class="mb-3">
                                <div class="alert alert-info d-flex align-items-center">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <div>
                                        <strong>本地模型</strong> - 在您的设备上运行，无需网络连接，数据完全私密
                                    </div>
                                </div>
                            </div>

                            <div id="local-model-list-container">
                                <div class="text-center my-5" id="local-loading-container">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2 text-muted">正在获取本地推理模型列表...</p>
                                </div>
                                <div id="local-model-grid">
                                    <!-- 本地模型卡片将由JS动态生成 -->
                                </div>
                                <div class="alert alert-light text-center mt-3 d-none" id="no-local-models-message">
                                    <i class="bi bi-hdd-stack fs-3 d-block mb-2"></i>
                                    <h6>暂无本地推理模型</h6>
                                    <p class="text-muted mb-0">您可以下载Ollama模型到本地进行离线推理</p>
                                </div>
                            </div>
                        </div>

                        <!-- 云端API模型标签页 -->
                        <div class="tab-pane fade" id="remote-models" role="tabpanel" aria-labelledby="remote-models-tab">
                            <div class="mb-3">
                                <div class="alert alert-success d-flex align-items-center">
                                    <i class="bi bi-cloud-check me-2"></i>
                                    <div>
                                        <strong>云端模型</strong> - 通过API调用云端服务，性能强大，需要网络连接
                                    </div>
                                </div>
                            </div>

                            <div id="remote-model-list-container">
                                <div id="remote-model-grid">
                                    <!-- 远程模型卡片将由JS动态生成 -->
                                </div>
                                <div class="alert alert-light text-center mt-3 d-none" id="no-remote-models-message">
                                    <i class="bi bi-cloud-slash fs-3 d-block mb-2"></i>
                                    <h6>暂无云端API模型配置</h6>
                                    <p class="text-muted mb-0">您可以配置DeepSeek等云端API模型</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模型详情模态框 -->
<div class="modal fade" id="model-details-modal" tabindex="-1" aria-labelledby="model-details-label" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="model-details-label">模型详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6 modal-info-section">
                        <h6 class="modal-section-title">基本信息</h6>
                        <div class="modal-info-item">
                            <span class="modal-info-key">名称:</span>
                            <span class="modal-info-value" id="modal-model-name"></span>
                        </div>
                        <div class="modal-info-item">
                            <span class="modal-info-key">大小:</span>
                            <span class="modal-info-value" id="modal-model-size"></span>
                        </div>
                        <div class="modal-info-item">
                            <span class="modal-info-key">类型:</span>
                            <span class="modal-info-value" id="modal-model-type"></span>
                        </div>
                        <div class="modal-info-item">
                            <span class="modal-info-key">推荐度:</span>
                            <span class="modal-info-value" id="modal-model-compatibility"></span>
                        </div>
                    </div>
                    <div class="col-md-6 modal-info-section">
                        <h6 class="modal-section-title">推荐配置</h6>
                        <div class="modal-info-item">
                            <span class="modal-info-key">最低内存:</span>
                            <span class="modal-info-value" id="modal-min-ram"></span>
                        </div>
                        <div class="modal-info-item">
                            <span class="modal-info-key">显存要求:</span>
                            <span class="modal-info-value" id="modal-gpu-req"></span>
                        </div>
                        <div class="modal-info-item">
                            <span class="modal-info-key">磁盘空间:</span>
                            <span class="modal-info-value" id="modal-disk-space"></span>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <h6 class="modal-section-title">模型描述</h6>
                    <p id="modal-model-description" class="modal-description-text"></p>
                </div>
                <div id="download-progress-container" class="d-none mt-4">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <h6 class="modal-section-title mb-0">下载进度</h6>
                        <small id="download-status" class="text-muted modal-progress-status">准备下载中...</small>
                    </div>
                    <div class="progress modal-progress" style="height: 8px;">
                        <div id="download-progress-bar" class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="download-model-btn">下载模型</button>
            </div>
        </div>
    </div>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 1100">
    <!-- Toasts will be appended here -->
</div>

<!-- Vendor JS -->
<script src="/wkg/js/vendor/jquery-3.7.1.min.js"></script>
<script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
<script src="/wkg/js/vendor/bootstrap-select.min.js"></script>
<!-- Custom JS -->
<script src="/wkg/js/features/model-downloader/model-downloader.js"></script>

<!-- 远程模型配置弹窗 -->
<div class="modal fade" id="remoteModelConfigModal" tabindex="-1" aria-labelledby="remoteModelConfigModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="remoteModelConfigModalLabel">
                    <i class="bi bi-cloud-arrow-up me-2"></i>配置云端API模型
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="remoteModelConfigForm">
                    <!-- 隐藏的模型ID字段 -->
                    <input type="hidden" id="config-model-id" name="modelId">

                    <div class="mb-3">
                        <label class="form-label">模型信息</label>
                        <div class="card bg-light">
                            <div class="card-body py-2">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-cloud me-2 text-info"></i>
                                    <div>
                                        <strong id="config-model-name"></strong>
                                        <br>
                                        <small class="text-muted" id="config-model-description"></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="config-api-key" class="form-label">API密钥 <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="config-api-key" name="apiKey" required
                                   placeholder="请输入API密钥">
                            <button class="btn btn-outline-secondary" type="button" id="toggle-api-key">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            API密钥将被安全加密存储，仅用于模型调用
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="config-remark" class="form-label">备注</label>
                        <textarea class="form-control" id="config-remark" name="remark" rows="2"
                                  placeholder="可选的备注信息"></textarea>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="config-enabled" name="enabled" checked>
                        <label class="form-check-label" for="config-enabled">
                            启用此配置
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-info me-2" id="test-connection-btn">
                    <i class="bi bi-wifi me-1"></i>测试连接
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="save-remote-config-btn">
                    <i class="bi bi-check-lg me-1"></i>保存配置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 百度统计 - 通用脚本 -->
<script src="/wkg/js/shared/analytics.js"></script>
</body>
</html>