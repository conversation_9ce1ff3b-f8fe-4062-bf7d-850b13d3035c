<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库空间管理 - 端智AI助手</title>

    <!-- CSS -->
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css">
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css">
    <link rel="stylesheet" href="/wkg/css/base/main.css">
    <link rel="stylesheet" href="/wkg/css/features/knowledge/space-management.css">
    <link rel="stylesheet" href="/wkg/css/components/modal-unified.css">
</head>
<body>
    <div class="container-fluid p-4">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1">知识库空间管理</h2>
                <p class="text-muted mb-0">创建和管理团队知识库空间，配置基于角色的访问权限</p>
            </div>
            <button class="btn btn-primary" onclick="showCreateSpaceModal()"
                    th:if="${@stpUtil.hasPermission('knowledge:space:create')}">
                <i class="bi bi-plus-lg"></i> 创建知识库空间
            </button>
        </div>

        <!-- 空间列表 -->
        <div class="row" id="spaceCardsContainer">
            <!-- 动态加载空间卡片 -->
        </div>
    </div>

    <!-- 创建空间模态框 -->
    <div class="modal fade" id="createSpaceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建知识库空间</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createSpaceForm">
                        <div class="mb-3">
                            <label class="form-label">知识库空间名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="name" required
                                   placeholder="请输入知识库空间名称" maxlength="20">
                            <small class="form-text text-muted">最多20个字符</small>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">空间描述</label>
                            <textarea class="form-control" name="description" rows="3"
                                      placeholder="请输入知识库空间的用途和描述" maxlength="100"></textarea>
                            <small class="form-text text-muted">最多100个字符</small>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">访问类型 <span class="text-danger">*</span></label>
                            <select class="form-control" name="accessType" onchange="toggleRolePermissions(this.value)">
                                <option value="PUBLIC">公开（所有用户可访问）</option>
                                <option value="ROLE_BASED" selected>基于角色（指定角色可访问）</option>
                            </select>
                            <small class="form-text text-muted">知识库空间不支持私有模式，个人文档请使用个人库</small>
                        </div>
                        
                        <!-- 角色选择 -->
                        <div id="rolePermissionsSection" class="mb-3">
                            <label class="form-label">可访问角色 <span class="text-danger">*</span></label>
                            <div id="roleCheckboxList" class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                <!-- 动态加载角色复选框 -->
                            </div>
                            <small class="form-text text-muted">选择可以访问此知识库空间的角色，选中的角色将拥有完整权限</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createSpace()">创建知识库空间</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑空间权限模态框 -->
    <div class="modal fade" id="editSpacePermissionsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑空间权限</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editPermissionsForm">
                        <input type="hidden" id="editSpaceId" name="spaceId">
                        <label class="form-label">可访问角色</label>
                        <div id="editRoleCheckboxList" class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                            <!-- 动态加载角色复选框 -->
                        </div>
                        <small class="form-text text-muted">选择可以访问此知识库空间的角色，选中的角色将拥有完整权限</small>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="updateSpacePermissions()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
    <script type="module" src="/wkg/js/shared/components/modal-component.js"></script>
    <script src="/wkg/js/features/knowledge/space-management.js"></script>

    <!-- 百度统计 - 通用脚本 -->
    <script src="/wkg/js/shared/analytics.js"></script>
</body>
</html>
