<!-- fragments/_sidebar.html - 独立的侧边栏组件 -->
<aside class="sidebar d-flex flex-column bg-body-tertiary border-end" th:fragment="sidebar">

    <!-- 顶部Logo区域 -->
    <div class="p-3 logo-area flex-shrink-0 d-flex justify-content-center align-items-center">
        <!-- AI办公助手SVG Logo -->
        <div class="sidebar-logo">
            <img th:src="@{/images/ai-assistant-logo.svg}" alt="AI Assistant Logo" width="32" height="32">
            <span class="logo-text ms-2 fw-bold">端智AI助手</span>
        </div>
    </div>

    <!-- 主导航区域 (使用 Bootstrap Nav) -->
    <nav class="nav nav-pills flex-column main-nav flex-grow-1 p-2 overflow-auto" id="sidebar-nav-accordion">

        <!-- AI 对话 (不再是折叠触发器) -->
        <div class="nav-item mb-1">
            <a class="nav-link d-flex align-items-center" 
               href="/wkg/chat/content" 
               target="contentFrame">
                <i class="bi bi-chat-left-text"></i>
                <span class="nav-link-text flex-grow-1">AI 对话</span>
                <!-- 移除折叠图标 -->
            </a>
            <!-- 移除折叠容器 div#collapseAiChat -->
        </div>

        <!-- 知识库 -->
        <div class="nav-item mb-1">
            <a href="/wkg/new-knowledge/team/content?init=true"
               class="nav-link d-flex align-items-center"
               target="contentFrame">
                <i class="bi bi-people"></i>
                <span class="nav-link-text">知识库</span>
            </a>
        </div>

        <!-- 个人库 -->
        <div class="nav-item mb-1">
            <a href="/wkg/new-knowledge/private/content?init=true"
               class="nav-link d-flex align-items-center"
               target="contentFrame">
                <i class="bi bi-person"></i>
                <span class="nav-link-text">个人库</span>
            </a>
        </div>

        <!-- 知识库空间管理 - 仅企业版显示 -->
        <div class="nav-item mb-1" th:if="${@editionUtil.isEnterprise() and @stpUtil.hasPermission('knowledge:space:create')}">
            <a href="/wkg/knowledge/space-management"
               class="nav-link d-flex align-items-center"
               target="contentFrame">
                <i class="bi bi-folder-plus"></i>
                <span class="nav-link-text">空间管理</span>
            </a>
        </div>

        <!-- 模型中心 -->
        <div class="nav-item mb-1" th:if="${@stpUtil.hasPermission('menu:view:models')}">
             <a href="/wkg/models"
                 class="nav-link d-flex align-items-center"
                 target="contentFrame">
                 <i class="bi bi-cpu"></i>
                 <span class="nav-link-text">模型中心</span>
             </a>
        </div>

        <!-- 系统设置 -->
        <div class="nav-item mb-1" th:if="${@stpUtil.hasPermission('menu:view:settings')}">
             <a href="/wkg/settings/content"
                class="nav-link d-flex align-items-center"
                target="contentFrame">
                 <i class="bi bi-gear"></i>
                 <span class="nav-link-text">系统设置</span>
             </a>
        </div>

        <!-- 权限管理 (带有折叠子菜单) - 仅企业版显示 -->
        <div class="nav-item mb-1" th:if="${@editionUtil.isEnterprise() and @stpUtil.hasPermission('menu:view:permission_group')}">
            <a class="nav-link d-flex align-items-center"
               href="#collapsePermission"
               data-bs-toggle="collapse"
               role="button"
               aria-expanded="false"
               aria-controls="collapsePermission">
                <i class="bi bi-shield-lock me-2"></i>
                <span class="nav-link-text flex-grow-1">权限管理</span>
                <!-- 只在展开状态下显示折叠图标 -->
                <i class="bi bi-chevron-down collapse-icon d-none d-sidebar-expanded-inline"></i>
            </a>
            <div class="collapse" id="collapsePermission">
                <div class="sub-nav ps-3 pt-1">
                    <!-- 用户管理子菜单 -->
                    <div class="nav-item mb-1" th:if="${@stpUtil.hasPermission('menu:view:users')}">
                        <a href="/wkg/system/user/content"
                           class="nav-link d-flex align-items-center"
                           target="contentFrame">
                            <i class="bi bi-person me-2"></i>
                            <span class="nav-link-text">用户管理</span>
                        </a>
                    </div>
                    <!-- 部门管理子菜单 -->
                    <div class="nav-item mb-1" th:if="${@stpUtil.hasPermission('menu:view:departments')}">
                        <a href="/wkg/system/dept/content"
                           class="nav-link d-flex align-items-center"
                           target="contentFrame">
                            <i class="bi bi-diagram-3 me-2"></i>
                            <span class="nav-link-text">部门管理</span>
                        </a>
                    </div>
                    <!-- 角色管理子菜单 -->
                    <div class="nav-item mb-1" th:if="${@stpUtil.hasPermission('menu:view:roles')}">
                        <a href="/wkg/system/role/content"
                           class="nav-link d-flex align-items-center"
                           target="contentFrame">
                            <i class="bi bi-people me-2"></i>
                            <span class="nav-link-text">角色管理</span>
                        </a>
                    </div>
                    <!-- 权限设置子菜单 -->
<!--                    <div class="nav-item mb-1" th:if="${@stpUtil.hasPermission('menu:view:permissions')}">-->
<!--                        <a href="/wkg/system/permission/content"-->
<!--                           class="nav-link d-flex align-items-center"-->
<!--                           target="contentFrame">-->
<!--                            <i class="bi bi-key me-2"></i>-->
<!--                            <span class="nav-link-text">权限设置</span>-->
<!--                        </a>-->
<!--                    </div>-->

                </div>
            </div>
        </div>

    </nav>

    <!-- 底部切换按钮 -->
    <div class="sidebar-footer mt-auto p-2 border-top flex-shrink-0">
        <button id="sidebar-toggle" class="btn btn-sm btn-outline-secondary w-100 d-flex align-items-center justify-content-center gap-1">
            <i class="bi bi-arrow-bar-left"></i>
            <span class="toggle-text">收起</span>
        </button>
    </div>
</aside>