<!-- 工作台导航栏 -->
<div class="auth-header border-bottom" th:fragment="authStatus">
    <div class="container-fluid px-3">
        <div class="d-flex align-items-center justify-content-between py-0">
            <!-- 左侧导航 -->
            <div class="d-flex align-items-center">
                <ul class="nav">
                    <li class="nav-item">
                        <a class="nav-link px-3" href="/wkg/">
                            <i class="bi bi-house"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link px-3" href="/wkg/license">
                            <i class="bi bi-award"></i> 许可证
                        </a>
                    </li>
                </ul>
            </div>

            <!-- 右侧用户信息 -->
            <div class="d-flex align-items-center" id="auth-status-container">

                <!-- 用户信息/登录选项 -->
                <div id="auth-status">
                    <!-- Content will be dynamically generated by auth-modal.js -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加样式 -->
<style>
    .auth-header {
        background-color: #fff;
    }

    .auth-header .nav-link {
        color: #6c757d;
        position: relative;
        padding-top: 0.7rem;
        padding-bottom: 0.7rem;
        transition: color 0.2s;
    }

    .auth-header .nav-link.active {
        color: #0d6efd;
        font-weight: 500;
    }

    .auth-header .nav-link.active::after {
        content: '';
        position: absolute;
        left: 0.75rem;
        right: 0.75rem;
        bottom: -1px;
        height: 2px;
        background-color: #0d6efd;
    }

    .auth-header .nav-link:hover:not(.active) {
        color: #212529;
    }

    .form-control-sm.rounded-pill {
        height: calc(1.5em + 0.5rem + 2px);
        padding-left: 1rem;
    }
</style>