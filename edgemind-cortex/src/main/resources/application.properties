spring.application.name=edgemind-cortex
server.port=8080


# ================= 运行环境配置 =================
run.env=dev

#run.env=demo

# ================= AI 模型配置 =================
ai.model.type=ollama
ai.model.name=qwen3:14b
ai.model.baseUrl=http://localhost:11434
ai.model.apiKey=your-api-key

# ================= 许可证服务器配置 =================
app.license.server.url=https://duanzhi.cc
app.version=1.0.0

# ================= Apache Lucene 9.x 向量数据库配置 =================
# 向量存储类型：lucene（推荐）, memory（测试用）
lucene.store.type=lucene

# 基础配置（完全内嵌式，无需外部服务）
lucene.index.path=./data/lucene-index
lucene.dimension=512
lucene.batch.size=100

# Lucene混合搜索权重配置（向量 + 关键词）
lucene.hybrid.alpha=0.7

# ================= BGE中文嵌入模型配置 =================
# BGE Small Chinese v1.5 模型配置（本地ONNX实现）
embedding.model.type=bge-small-zh-v15
embedding.model.dimension=512

# ================= 通用向量搜索配置 =================
# 向量搜索最大结果数
vector.search.max-results=10
# 向量搜索最小分数阈值（过滤低质量匹配）
vector.search.min-score=0.4
# 混合搜索开关（向量搜索 + 关键词搜索）
vector.hybrid.enabled=true

# ================= 浏览器自动化配置 =================
# 浏览器自动化功能开关
chat.browser.enabled=true
# 浏览器自动化最大历史消息数
chat.browser.max-history=5
# 浏览器自动化深度思考功能
chat.browser.thinking.enabled=true

# WebSocket配置
# WebSocket允许的来源（生产环境应该限制）
websocket.allowed-origins=*
# WebSocket连接超时（毫秒）
websocket.connection-timeout=30000
# WebSocket消息最大大小（字节）
websocket.max-message-size=10485760

# 浏览器工具配置
browser.tools.timeout=30000
browser.tools.retry-attempts=3
browser.tools.max-concurrent-requests=10

# ================= 文件上传配置 =================
spring.servlet.multipart.max-file-size=30MB
spring.servlet.multipart.max-request-size=30MB

# ================= 服务器配置 =================
server.tomcat.max-swallow-size=30MB
server.servlet.context-path=/wkg

# ================= 开发工具配置 =================
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true
spring.thymeleaf.cache=false
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.cache.period=0

# ================= 数据库配置 =================
spring.datasource.driver-class-name=org.h2.Driver
# H2数据库连接URL - 使用文件模式存储在当前目录的data子目录
spring.datasource.url=jdbc:h2:file:./data/edgemind;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE
spring.datasource.username=sa
spring.datasource.password=

# H2数据库额外配置
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
spring.h2.console.settings.web-allow-others=false
# 初始化数据库脚本（由edgemind-licence模块的DatabaseInitializer处理）
# spring.sql.init.mode=always
# spring.sql.init.schema-locations=classpath:sql/schema.sql
# spring.sql.init.continue-on-error=false

# 轻量级模式配置 - 无需Docker和Redis
# 使用H2数据库和内存缓存提供完整功能

# Jasypt加密配置（H2数据库不需要密码加密）
# jasypt.encryptor.algorithm=PBEWithMD5AndDES
# jasypt.encryptor.password=0f7b0a5d-46bc-40fd-b8ed-3181d21d644f
# jasypt.encryptor.iv-generator-classname=org.jasypt.iv.NoIvGenerator

# ================= 缓存配置 =================
# 使用内存缓存替代Redis，提供轻量级缓存解决方案

# ================= Sa-Token 配置 =================
sa-token.token-name=satoken
sa-token.is-log=true
sa-token.token-style=uuid
sa-token.is-concurrent=true
sa-token.is-share=true
#sa-token.max-login-count=1

# ================= MyBatis Plus 配置 =================
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.type-aliases-package=com.zibbava.edgemind.cortex.entity
mybatis-plus.configuration.map-underscore-to-camel-case=true


# ================= 文档查看器配置 =================
# 使用开源前端文档查看器，无需外部服务
document.viewer.enabled=true

# ================= 企业级对话策略配置 =================

# 对话风格配置
chat.style.professional=true
chat.style.friendly=true
chat.style.structured=true

# 功能开关配置
chat.context.analysis=true
chat.thinking.enabled=true
chat.history.aware=true

# LangChain4j集成配置
chat.use-langchain4j=true

# Ollama服务配置
ollama.base-url=http://localhost:11434
ollama.timeout=60

# 对话质量配置
chat.quality.accuracy-first=true
chat.quality.completeness-check=true
chat.quality.relevance-filter=true

# 增强检索配置
# 启用增强检索功能（查询扩展）
enhanced.retrieval.enabled=true
# 子查询数量
enhanced.retrieval.sub-queries=3
# 假设文档数量
enhanced.retrieval.hypothetical-docs=2

# ================= 历史消息处理配置 =================
chat.history.max-messages=10

# ================= 日志配置 =================
# logging.level.com.zibbava=DEBUG  # ????logback.xml??springProfile??

# Spring Profile??
# IDEA????????dev profile?Maven???????
spring.profiles.active=dev
logging.level.org.springframework=WARN
logging.level.org.apache.lucene=WARN

# ================= ???? =================
# ?????personal(???) ? enterprise(???)
edgemind.edition.type=enterprise

