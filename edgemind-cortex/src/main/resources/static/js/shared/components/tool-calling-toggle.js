/**
 * 工具调用切换按钮组件
 * 用于控制AI对话中是否启用工具调用功能
 */

/**
 * 工具调用切换按钮类
 */
class ToolCallingToggle {
    constructor(buttonElement) {
        this.button = buttonElement;
        this.isEnabled = false; // 默认关闭
        this.isProcessing = false; // 是否正在处理中
        
        this.init();
    }
    
    /**
     * 初始化组件
     */
    init() {
        // 设置初始状态
        this.updateButtonState();
        
        // 绑定点击事件
        this.button.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggle();
        });
        
        // 初始化 Bootstrap tooltip
        if (this.button.hasAttribute('data-bs-toggle')) {
            new bootstrap.Tooltip(this.button);
        }
    }
    
    /**
     * 切换工具调用状态
     */
    toggle() {
        // 手动隐藏tooltip
        const tooltipInstance = bootstrap.Tooltip.getInstance(this.button);
        if (tooltipInstance) {
            tooltipInstance.hide();
        }
        
        // 切换状态
        this.isEnabled = !this.isEnabled;
        this.updateButtonState();
        
        // 触发状态变化事件
        this.dispatchStateChangeEvent();
    }
    
    /**
     * 设置工具调用状态
     */
    setEnabled(enabled) {
        // 手动隐藏tooltip
        const tooltipInstance = bootstrap.Tooltip.getInstance(this.button);
        if (tooltipInstance) {
            tooltipInstance.hide();
        }
        
        this.isEnabled = enabled;
        this.updateButtonState();
        this.dispatchStateChangeEvent();
    }
    
    /**
     * 获取当前状态
     */
    getEnabled() {
        return this.isEnabled;
    }
    
    /**
     * 设置处理中状态（仅用于视觉指示，不影响按钮功能）
     */
    setProcessing(processing) {
        this.isProcessing = processing;
        this.updateButtonState();
    }
    
    /**
     * 更新按钮状态
     */
    updateButtonState() {
        // 移除所有状态类
        this.button.classList.remove('active', 'processing');
        
        // 保持始终可点击
        this.button.disabled = false;
        
        // 添加对应状态类
        if (this.isEnabled) {
            this.button.classList.add('active');
        }
        
        if (this.isProcessing) {
            this.button.classList.add('processing');
        }
        
        // 更新 tooltip 文本
        let tooltipText;
        if (this.isProcessing) {
            tooltipText = '工具调用中...';
        } else {
            tooltipText = this.isEnabled ? '工具调用已启用' : '工具调用已禁用';
        }
        
        this.button.setAttribute('title', tooltipText);
        this.button.setAttribute('data-bs-original-title', tooltipText);
        
        // 如果 tooltip 已初始化，更新其内容
        const tooltipInstance = bootstrap.Tooltip.getInstance(this.button);
        if (tooltipInstance) {
            tooltipInstance.setContent({ '.tooltip-inner': tooltipText });
        }
    }
    
    /**
     * 触发状态变化事件
     */
    dispatchStateChangeEvent() {
        const event = new CustomEvent('toolCallingStateChange', {
            detail: {
                enabled: this.isEnabled,
                buttonId: this.button.id || this.button.dataset.toolCallingId
            },
            bubbles: true
        });
        
        this.button.dispatchEvent(event);
    }
}

/**
 * 工具调用管理器
 * 管理页面中所有的工具调用切换按钮
 */
class ToolCallingManager {
    constructor() {
        this.toggles = new Map();
        this.globalState = false; // 全局默认状态
    }
    
    /**
     * 注册工具调用切换按钮
     */
    register(buttonElement, id = null) {
        const toggleId = id || buttonElement.id || buttonElement.dataset.toolCallingId || 'default';
        
        if (this.toggles.has(toggleId)) {
            console.warn(`[ToolCallingManager] Toggle with ID '${toggleId}' already registered.`);
            return this.toggles.get(toggleId);
        }
        
        const toggle = new ToolCallingToggle(buttonElement);
        this.toggles.set(toggleId, toggle);
        
        console.log(`[ToolCallingManager] Registered toggle: ${toggleId}`);
        return toggle;
    }
    
    /**
     * 获取指定ID的切换按钮
     */
    getToggle(id) {
        return this.toggles.get(id);
    }
    
    /**
     * 设置指定ID的状态
     */
    setState(id, enabled) {
        const toggle = this.toggles.get(id);
        if (toggle) {
            toggle.setEnabled(enabled);
        }
    }
    
    /**
     * 设置全局状态（影响所有切换按钮）
     */
    setGlobalState(enabled) {
        this.globalState = enabled;
        
        this.toggles.forEach((toggle) => {
            toggle.setEnabled(enabled);
        });
    }
    
    /**
     * 获取指定ID的状态
     */
    getState(id) {
        const toggle = this.toggles.get(id);
        return toggle ? toggle.getEnabled() : this.globalState;
    }
    
    /**
     * 设置处理中状态
     */
    setProcessing(id, processing) {
        const toggle = this.toggles.get(id);
        if (toggle) {
            toggle.setProcessing(processing);
        }
    }
    
    /**
     * 自动发现并注册页面中的工具调用按钮
     */
    autoDiscover() {
        const buttons = document.querySelectorAll('.tool-calling-toggle');
        let count = 0;
        
        buttons.forEach((button) => {
            const id = button.dataset.toolCallingId || button.id || null;
            this.register(button, id);
            count++;
        });
        
        console.log(`[ToolCallingManager] Auto-discovered ${count} tool calling toggles`);
        return count;
    }
}

// 创建全局管理器实例
window.toolCallingManager = new ToolCallingManager();

// DOM加载完成后自动发现按钮
document.addEventListener('DOMContentLoaded', () => {
    window.toolCallingManager.autoDiscover();
});

// 导出供其他模块使用
export { ToolCallingToggle, ToolCallingManager };
