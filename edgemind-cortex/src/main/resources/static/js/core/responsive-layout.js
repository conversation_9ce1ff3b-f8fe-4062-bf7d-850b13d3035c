/**
 * 响应式布局处理 和 侧边栏切换
 */

const SIDEBAR_COLLAPSED_KEY = 'sidebarCollapsed';

function initialize() {
    const menuToggle = document.getElementById('menu-toggle'); // Mobile toggle
    const sidebarToggle = document.getElementById('sidebar-toggle'); // Desktop toggle
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    const appContainer = document.querySelector('.app-container');
    const toggleIcon = sidebarToggle?.querySelector('i');
    const toggleText = sidebarToggle?.querySelector('.toggle-text');

    if (!sidebar || !appContainer) {
        console.error("Sidebar or App Container not found!");
        return;
    }

    // 初始化时禁用过渡效果
    appContainer.classList.add('no-transition');

    // --- Helper Function ---
    const setSidebarState = (isCollapsed) => {
        sidebar.classList.toggle('collapsed', isCollapsed);
        appContainer.classList.toggle('sidebar-collapsed', isCollapsed);
        if (toggleIcon) {
            toggleIcon.classList.toggle('bi-arrow-bar-left', !isCollapsed);
            toggleIcon.classList.toggle('bi-arrow-bar-right', isCollapsed);
        }
        if (toggleText) {
            toggleText.textContent = isCollapsed ? '展开' : '收起';
        }
        // Save state to localStorage
        try {
            localStorage.setItem(SIDEBAR_COLLAPSED_KEY, isCollapsed ? 'true' : 'false');
        } catch (e) {
            console.warn("Could not save sidebar state to localStorage:", e);
        }

        // 状态切换后，恢复正确的子菜单状态
        setTimeout(() => {
            if (typeof restoreSubmenuState === 'function') {
                restoreSubmenuState();
            }
        }, 50); // 短暂延迟确保DOM更新完成
    };

    // --- Load Initial State (Desktop) --- 
    if (window.innerWidth > 768) {
        let initialStateCollapsed = false;
        try {
            initialStateCollapsed = localStorage.getItem(SIDEBAR_COLLAPSED_KEY) === 'true';
        } catch (e) {
             console.warn("Could not read sidebar state from localStorage:", e);
        }
         if (initialStateCollapsed) {
             // 立即应用折叠状态，避免闪烁
             sidebar.classList.add('collapsed');
             appContainer.classList.add('sidebar-collapsed');
             if (toggleIcon) {
                 toggleIcon.classList.remove('bi-arrow-bar-left');
                 toggleIcon.classList.add('bi-arrow-bar-right');
             }
             if (toggleText) {
                 toggleText.textContent = '展开';
             }
         }
         
         // 移除初始折叠类，让JavaScript接管
         document.documentElement.classList.remove('sidebar-initially-collapsed');
         
         // 重置CSS变量，让正常的CSS类控制生效
         document.documentElement.style.removeProperty('--initial-sidebar-width');
         document.documentElement.style.removeProperty('--initial-main-margin');
         
         // 设置完初始状态后启用过渡效果
         setTimeout(() => {
             appContainer.classList.remove('no-transition');
         }, 50);
    }
    
    // --- Mobile Menu Toggle --- 
    if (menuToggle) {
        menuToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            sidebar.classList.toggle('show');
            if (sidebar.classList.contains('show')) {
                sidebar.classList.remove('collapsed');
                appContainer.classList.remove('sidebar-collapsed');
            } 
        });
    }
        
    // --- Click outside to close mobile menu --- 
    if (mainContent) {
        mainContent.addEventListener('click', function() {
            if (window.innerWidth <= 768 && sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        });
    }
    document.body.addEventListener('click', function(event) {
         if (window.innerWidth <= 768 && sidebar.classList.contains('show')) {
              if (!sidebar.contains(event.target) && !menuToggle?.contains(event.target)) {
                   sidebar.classList.remove('show');
              }
         }
    });

    // --- Desktop Sidebar Toggle --- 
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            const isCurrentlyCollapsed = sidebar.classList.contains('collapsed');
            setSidebarState(!isCurrentlyCollapsed);
        });
    } else {
        console.warn("Desktop sidebar toggle button (#sidebar-toggle) not found.");
    }
    
    // --- Window Resize Handling --- 
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768 && sidebar.classList.contains('show')) {
            sidebar.classList.remove('show');
        }
         if (window.innerWidth > 768) {
             let storedStateCollapsed = false;
             try { storedStateCollapsed = localStorage.getItem(SIDEBAR_COLLAPSED_KEY) === 'true'; } catch(e){}
             if (sidebar.classList.contains('collapsed') !== storedStateCollapsed) {
                  setSidebarState(storedStateCollapsed);
             }
         } else {
            sidebar.classList.remove('collapsed');
            appContainer.classList.remove('sidebar-collapsed');
         }
    });

    // 初始化收起状态下的子菜单图标显示
    initCollapsedSubmenu();

    // 监听页面加载完成，确保状态恢复（移除延迟，立即执行）
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', restoreSubmenuState);
    } else {
        restoreSubmenuState();
    }
}

/**
 * 初始化收起状态下的子菜单图标显示
 * 在侧边栏收起状态下，点击带有子菜单的项目时直接显示子节点图标
 */
function initCollapsedSubmenu() {
    const sidebar = document.querySelector('.sidebar');
    if (!sidebar) return;

    // 恢复之前保存的子菜单状态
    restoreSubmenuState();

    // 查找所有带有折叠子菜单的导航项
    const collapseLinks = sidebar.querySelectorAll('[data-bs-toggle="collapse"]');

    collapseLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const targetId = link.getAttribute('href').substring(1);
            const targetCollapse = document.getElementById(targetId);
            if (!targetCollapse) return;

            if (sidebar.classList.contains('collapsed')) {
                // 收起状态下的自定义处理
                e.preventDefault(); // 阻止默认的折叠行为

                // 切换子菜单的显示状态
                const isCurrentlyVisible = targetCollapse.style.display === 'block';

                // 先隐藏所有其他的子菜单
                sidebar.querySelectorAll('.collapse').forEach(collapse => {
                    if (collapse !== targetCollapse) {
                        collapse.style.display = 'none';
                        collapse.classList.remove('show');
                    }
                });

                // 切换当前子菜单的显示状态
                if (isCurrentlyVisible) {
                    targetCollapse.style.display = 'none';
                    targetCollapse.classList.remove('show');
                    // 清除保存的状态
                    localStorage.removeItem('activeSubmenu');
                } else {
                    targetCollapse.style.display = 'block';
                    targetCollapse.classList.add('show');
                    // 保存当前激活的子菜单状态
                    localStorage.setItem('activeSubmenu', targetId);
                }
            } else {
                // 展开状态下，清除可能影响Bootstrap折叠功能的内联样式
                clearInlineStyles(targetCollapse);
                // 让Bootstrap的默认折叠行为正常工作
                // 不调用 e.preventDefault()，让Bootstrap处理折叠逻辑
            }
        });
    });

    // 为所有菜单项添加tooltip
    initAllTooltips();
}

/**
 * 恢复子菜单状态
 */
function restoreSubmenuState() {
    const sidebar = document.querySelector('.sidebar');
    if (!sidebar) return;

    if (sidebar.classList.contains('collapsed')) {
        // 收起状态下恢复子菜单显示
        const activeSubmenuId = localStorage.getItem('activeSubmenu');
        if (activeSubmenuId) {
            const targetCollapse = document.getElementById(activeSubmenuId);
            if (targetCollapse) {
                targetCollapse.style.display = 'block';
                targetCollapse.classList.add('show');
            }
        }
    } else {
        // 展开状态下清除所有内联样式，让Bootstrap正常工作
        const allCollapses = sidebar.querySelectorAll('.collapse');
        allCollapses.forEach(collapse => {
            clearInlineStyles(collapse);
        });
    }
}

/**
 * 清除可能影响Bootstrap折叠功能的内联样式
 */
function clearInlineStyles(element) {
    if (element) {
        element.style.display = '';
        // 不移除show类，让Bootstrap自己管理
    }
}

/**
 * 初始化所有菜单项的tooltip提示
 */
function initAllTooltips() {
    const sidebar = document.querySelector('.sidebar');
    if (!sidebar) return;

    // 为父菜单项添加tooltip
    initParentMenuTooltips();

    // 为子菜单项添加tooltip
    initSubmenuTooltips();
}

/**
 * 初始化父菜单项的tooltip提示
 */
function initParentMenuTooltips() {
    const sidebar = document.querySelector('.sidebar');
    if (!sidebar) return;

    // 查找所有主菜单项
    const parentLinks = sidebar.querySelectorAll('.nav-item > .nav-link');

    parentLinks.forEach(link => {
        // 获取链接的文字内容作为tooltip
        const textElement = link.querySelector('.nav-link-text');
        if (textElement) {
            const tooltipText = textElement.textContent.trim();

            // 创建自定义tooltip元素
            const tooltip = document.createElement('div');
            tooltip.className = 'custom-tooltip';
            tooltip.textContent = tooltipText;
            document.body.appendChild(tooltip);

            // 鼠标进入时显示tooltip
            link.addEventListener('mouseenter', function(e) {
                if (sidebar.classList.contains('collapsed')) {
                    const rect = link.getBoundingClientRect();
                    tooltip.style.left = (rect.right + 10) + 'px';
                    tooltip.style.top = (rect.top + rect.height / 2 - tooltip.offsetHeight / 2) + 'px';
                    tooltip.style.opacity = '1';
                    tooltip.style.visibility = 'visible';
                }
            });

            // 鼠标离开时隐藏tooltip
            link.addEventListener('mouseleave', function() {
                tooltip.style.opacity = '0';
                tooltip.style.visibility = 'hidden';
            });
        }
    });
}

/**
 * 初始化子菜单项的tooltip提示
 */
function initSubmenuTooltips() {
    const sidebar = document.querySelector('.sidebar');
    if (!sidebar) return;

    // 查找所有子菜单中的链接
    const submenuLinks = sidebar.querySelectorAll('.collapse .nav-link');

    submenuLinks.forEach(link => {
        // 获取链接的文字内容作为tooltip
        const textElement = link.querySelector('.nav-link-text');
        if (textElement) {
            const tooltipText = textElement.textContent.trim();

            // 创建自定义tooltip元素
            const tooltip = document.createElement('div');
            tooltip.className = 'custom-tooltip';
            tooltip.textContent = tooltipText;
            document.body.appendChild(tooltip);

            // 鼠标进入时显示tooltip
            link.addEventListener('mouseenter', function(e) {
                if (sidebar.classList.contains('collapsed')) {
                    const rect = link.getBoundingClientRect();
                    tooltip.style.left = (rect.right + 10) + 'px';
                    tooltip.style.top = (rect.top + rect.height / 2 - tooltip.offsetHeight / 2) + 'px';
                    tooltip.style.opacity = '1';
                    tooltip.style.visibility = 'visible';
                }
            });

            // 鼠标离开时隐藏tooltip
            link.addEventListener('mouseleave', function() {
                tooltip.style.opacity = '0';
                tooltip.style.visibility = 'hidden';
            });
        }

        // 子菜单项点击时的处理
        link.addEventListener('click', function(e) {
            if (sidebar.classList.contains('collapsed')) {
                // 添加选中状态样式
                link.classList.add('active');

                // 移除其他子菜单项的选中状态
                const allSubmenuLinks = sidebar.querySelectorAll('.collapse .nav-link');
                allSubmenuLinks.forEach(otherLink => {
                    if (otherLink !== link) {
                        otherLink.classList.remove('active');
                    }
                });

                // 短暂显示选中效果后跳转
                setTimeout(() => {
                    // 不阻止默认行为，让链接正常跳转
                    // 不清除localStorage状态，保持子菜单显示
                }, 150);
            }
        });
    });
}

// Export the initialize function
export { initialize };