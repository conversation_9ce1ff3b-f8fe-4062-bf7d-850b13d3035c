<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>PDF Viewer</title>
    <style>
        body { margin: 0; padding: 0; background: #525659; }
        #pdfContainer { width: 100%; height: 100vh; overflow: auto; text-align: center; }
        .page { margin: 10px auto; box-shadow: 0 4px 8px rgba(0,0,0,0.3); }
        #loading { color: white; text-align: center; padding: 50px; }
    </style>
</head>
<body>
    <div id="loading">正在加载PDF...</div>
    <div id="pdfContainer"></div>
    
    <script src="/wkg/js/vendor/pdf.min.js"></script>
    <script>
        pdfjsLib.GlobalWorkerOptions.workerSrc = '/wkg/js/vendor/pdf.worker.min.js';
        
        const urlParams = new URLSearchParams(window.location.search);
        const fileUrl = urlParams.get('file');
        
        if (fileUrl) {
            loadPDF(decodeURIComponent(fileUrl));
        }
        
        async function loadPDF(url) {
            try {
                const pdf = await pdfjsLib.getDocument(url).promise;
                const container = document.getElementById('pdfContainer');
                const loading = document.getElementById('loading');
                
                loading.style.display = 'none';
                
                for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
                    const page = await pdf.getPage(pageNum);
                    const viewport = page.getViewport({ scale: 1.2 });
                    
                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;
                    canvas.className = 'page';
                    
                    container.appendChild(canvas);
                    
                    await page.render({
                        canvasContext: context,
                        viewport: viewport
                    }).promise;
                }
            } catch (error) {
                document.getElementById('loading').innerHTML = '加载PDF失败: ' + error.message;
            }
        }
    </script>
</body>
</html>
