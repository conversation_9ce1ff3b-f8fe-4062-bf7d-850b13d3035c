/**
 * 开源文档查看器组件
 * 替代OnlyOffice，支持多种文档格式
 */

class DocumentViewer {
    constructor(container) {
        this.container = container;
        this.currentConfig = null;
        this.currentEditor = null;
        this.currentZoom = 1;
        this.currentRotation = 0;
        
        this.init();
    }
    
    init() {
        this.container.innerHTML = this.getLoadingHTML();
    }
    
    /**
     * 加载文档
     * @param {Object} config 文档查看器配置
     */
    async loadDocument(config) {
        this.currentConfig = config;
        this.showLoading('正在加载文档...');
        
        try {
            switch (config.viewerType) {
                case 'pdf':
                    await this.loadPDF(config);
                    break;
                case 'office':
                    await this.loadOfficeDocument(config);
                    break;
                case 'excel':
                    await this.loadExcelViewer(config);
                    break;
                case 'markdown':
                    await this.loadMarkdownViewer(config);
                    break;
                case 'image':
                    await this.loadImageViewer(config);
                    break;
                case 'text':
                    await this.loadTextViewer(config);
                    break;
                default:
                    this.showUnsupported(config);
            }
        } catch (error) {
            console.error('加载文档失败:', error);
            this.showError('加载文档失败: ' + error.message);
        }
    }
    
    /**
     * 加载PDF文档
     */
    async loadPDF(config) {
        // 确保PDF.js已加载
        if (typeof pdfjsLib === 'undefined') {
            await this.loadScript('/wkg/js/vendor/pdf.min.js');
            pdfjsLib.GlobalWorkerOptions.workerSrc = '/wkg/js/vendor/pdf.worker.min.js';
        }
        
        const html = `
            <div class="document-viewer-container">
                <div class="document-viewer-content">
                    <div class="pdf-viewer-container">
                        <iframe class="pdf-viewer" src="/wkg/js/vendor/pdfjs/web/viewer.html?file=${encodeURIComponent(config.fileUrl)}"></iframe>
                    </div>
                </div>
            </div>
        `;
        
        this.container.innerHTML = html;
    }
    
    /**
     * 加载Office文档
     */
    async loadOfficeDocument(config) {
        // 对于Office文档，我们使用docx-preview处理Word文档，其他格式显示不支持提示
        const extension = this.getFileExtension(config.fileName).toLowerCase();

        if (extension === 'docx') {
            await this.loadWordDocument(config);
        } else {
            this.showUnsupportedFormat(config);
        }
    }

    /**
     * 显示不支持的格式提示
     */
    showUnsupportedFormat(config) {
        const extension = this.getFileExtension(config.fileName).toUpperCase();
        const html = `
            <div class="document-viewer-container">
                <div class="document-viewer-content">
                    <div class="unsupported-format-prompt">
                        <i class="bi bi-file-earmark-x icon"></i>
                        <div class="message">文件格式不支持</div>
                        <div class="description">
                            ${extension} 文件暂不支持在线预览。
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.container.innerHTML = html;
    }
    
    /**
     * 加载Word文档 - 使用docx-preview实现高质量渲染
     */
    async loadWordDocument(config) {
        // 确保docx-preview和jszip已加载
        if (typeof docx === 'undefined') {
            await this.loadScript('/wkg/js/vendor/jszip.min.js');
            await this.loadScript('/wkg/js/vendor/docx-preview.min.js');
        }

        const html = `
            <div class="document-viewer-container">
                <div class="document-viewer-content">
                    <div class="office-viewer">
                        <div class="office-content" id="wordContent">
                            <div class="document-loading">
                                <div class="spinner"></div>
                                <div class="message">正在使用高质量引擎解析Word文档...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.container.innerHTML = html;

        try {
            const response = await fetch(config.fileUrl);
            const arrayBuffer = await response.arrayBuffer();

            // docx-preview配置选项 - 参考官网优化
            const options = {
                className: "docx-preview", // CSS类名前缀
                inWrapper: false, // 禁用文档包装器，避免额外边框
                hideWrapperOnPrint: true, // 打印时隐藏包装器
                ignoreWidth: true, // 忽略页面宽度限制，充分利用空间
                ignoreHeight: true, // 忽略页面高度限制
                ignoreFonts: false, // 不忽略字体
                breakPages: false, // 禁用分页，连续显示
                ignoreLastRenderedPageBreak: true, // 忽略最后渲染的分页符
                experimental: true, // 启用实验性功能（制表符计算等）
                trimXmlDeclaration: true, // 移除XML声明
                useBase64URL: true, // 使用base64 URL处理图片
                renderChanges: false, // 不渲染文档变更
                renderHeaders: false, // 不渲染页眉，避免额外空间
                renderFooters: false, // 不渲染页脚，避免额外空间
                renderFootnotes: true, // 渲染脚注
                renderEndnotes: true, // 渲染尾注
                renderComments: false, // 不渲染评论
                renderAltChunks: true, // 渲染altChunks（HTML部分）
                debug: false // 不启用调试模式
            };

            // 清空加载提示
            const wordContentElement = document.getElementById('wordContent');
            wordContentElement.innerHTML = '';

            // 使用docx-preview渲染文档
            await docx.renderAsync(arrayBuffer, wordContentElement, null, options);

            console.log('✅ docx-preview渲染成功:', config.fileName);

        } catch (error) {
            console.error('docx-preview渲染失败:', error);
            document.getElementById('wordContent').innerHTML = `
                <div class="document-error">
                    <i class="bi bi-exclamation-triangle icon"></i>
                    <div class="message">Word文档解析失败</div>
                    <div class="description">${error.message}</div>
                    <div class="suggestion">
                        <small>建议：<a href="${config.fileUrl}" download>下载原文档</a> 查看完整内容</small>
                    </div>
                </div>
            `;
        }
    }




    /**
     * 加载Excel查看器
     */
    async loadExcelViewer(config) {
        // 确保SheetJS已加载
        if (typeof XLSX === 'undefined') {
            await this.loadScript('/wkg/js/vendor/xlsx.full.min.js');
        }

        const html = `
            <div class="document-viewer-container">
                <div class="document-viewer-content">
                    <div class="excel-viewer">
                        <div class="excel-sheets" id="excelSheets"></div>
                        <div class="excel-content" id="excelContent">
                            <div class="loading">正在解析Excel文件...</div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.container.innerHTML = html;

        try {
            // 显示加载进度
            const loadingElement = document.getElementById('excelContent');
            loadingElement.innerHTML = `
                <div class="document-loading">
                    <div class="spinner"></div>
                    <div class="message">正在下载Excel文件...</div>
                </div>
            `;

            const response = await fetch(config.fileUrl);
            const arrayBuffer = await response.arrayBuffer();

            loadingElement.innerHTML = `
                <div class="document-loading">
                    <div class="spinner"></div>
                    <div class="message">正在解析Excel文件...</div>
                </div>
            `;

            // 使用SheetJS解析Excel文件，优化性能配置
            const workbook = XLSX.read(arrayBuffer, {
                type: 'array',
                cellFormula: false,  // 不解析公式，提高性能
                cellHTML: false,     // 不生成HTML，提高性能
                cellNF: false,       // 不解析数字格式，提高性能
                cellDates: false,    // 不解析日期，提高性能
                cellStyles: false,   // 不解析样式，提高性能
                sheetStubs: false,   // 不生成空单元格，提高性能
                bookDeps: false,     // 不解析依赖，提高性能
                bookFiles: false,    // 不解析文件，提高性能
                bookProps: false,    // 不解析属性，提高性能
                bookSheets: false,   // 不解析工作表属性，提高性能
                bookVBA: false,      // 不解析VBA，提高性能
                password: "",        // 密码
                WTF: false          // 不显示调试信息
            });

            this.renderExcelWorkbook(workbook);
        } catch (error) {
            document.getElementById('excelContent').innerHTML = `
                <div class="document-error">
                    <i class="bi bi-exclamation-triangle icon"></i>
                    <div class="message">Excel文件解析失败</div>
                    <div class="description">${error.message}</div>
                </div>
            `;
        }
    }

    /**
     * 渲染Excel工作簿
     */
    renderExcelWorkbook(workbook) {
        const sheetsContainer = document.getElementById('excelSheets');
        const contentContainer = document.getElementById('excelContent');

        // 安全检查工作簿数据
        if (!workbook || !workbook.SheetNames || !Array.isArray(workbook.SheetNames)) {
            contentContainer.innerHTML = `
                <div class="document-error">
                    <i class="bi bi-exclamation-triangle icon"></i>
                    <div class="message">Excel工作簿数据无效</div>
                    <div class="description">无法读取工作表信息</div>
                </div>
            `;
            return;
        }

        // 创建工作表标签
        const sheetNames = workbook.SheetNames.filter(name => name && typeof name === 'string');
        if (sheetNames.length === 0) {
            contentContainer.innerHTML = `
                <div class="document-error">
                    <i class="bi bi-exclamation-triangle icon"></i>
                    <div class="message">Excel文件中没有有效的工作表</div>
                </div>
            `;
            return;
        }

        if (sheetNames.length > 1) {
            const tabsHtml = sheetNames.map((name, index) => {
                const safeName = this.escapeHtml(String(name));
                const safeNameForJs = name.replace(/'/g, "\\'");
                return `<button class="sheet-tab ${index === 0 ? 'active' : ''}"
                         onclick="documentViewer.switchExcelSheet('${safeNameForJs}')">${safeName}</button>`;
            }).join('');
            sheetsContainer.innerHTML = `<div class="sheet-tabs">${tabsHtml}</div>`;
        }

        // 显示第一个工作表
        this.currentWorkbook = workbook;
        this.switchExcelSheet(sheetNames[0]);
    }

    /**
     * 切换Excel工作表
     */
    switchExcelSheet(sheetName) {
        if (!this.currentWorkbook || !sheetName || typeof sheetName !== 'string') {
            console.error('无效的工作簿或工作表名称:', sheetName);
            return;
        }

        // 显示加载状态
        const contentContainer = document.getElementById('excelContent');
        if (!contentContainer) {
            console.error('找不到Excel内容容器');
            return;
        }

        contentContainer.innerHTML = `
            <div class="document-loading">
                <div class="spinner"></div>
                <div class="message">正在渲染工作表...</div>
            </div>
        `;

        // 使用setTimeout让UI有时间更新
        setTimeout(() => {
            try {
                // 更新标签状态
                document.querySelectorAll('.sheet-tab').forEach(tab => {
                    tab.classList.toggle('active', tab.textContent === sheetName);
                });

                // 安全检查工作簿和工作表
                if (!this.currentWorkbook.Sheets || typeof this.currentWorkbook.Sheets !== 'object') {
                    throw new Error('工作簿数据结构无效');
                }

                // 渲染工作表内容
                const worksheet = this.currentWorkbook.Sheets[sheetName];

                // 检查工作表是否存在
                if (!worksheet || typeof worksheet !== 'object') {
                    contentContainer.innerHTML = `
                        <div class="document-error">
                            <i class="bi bi-exclamation-triangle icon"></i>
                            <div class="message">工作表不存在</div>
                            <div class="description">工作表 "${this.escapeHtml(String(sheetName))}" 不存在或无法访问</div>
                        </div>
                    `;
                    return;
                }

                // 优化的Excel表格渲染，支持大数据量
                let htmlTable;
                try {
                    // 安全检查工作表数据
                    if (!worksheet || typeof worksheet !== 'object') {
                        throw new Error('工作表数据无效');
                    }

                    // 检查数据量，如果太大则使用分页渲染
                    const range = worksheet['!ref'];
                    const decoded = range ? XLSX.utils.decode_range(range) : null;
                    const totalRows = decoded ? decoded.e.r + 1 : 0;
                    const totalCols = decoded ? decoded.e.c + 1 : 0;

                    console.log(`Excel数据量: ${totalRows}行 x ${totalCols}列`);

                    if (totalRows > 1000 || totalCols > 50) {
                        // 大数据量使用分页渲染
                        htmlTable = this.renderLargeExcelTable(worksheet, totalRows, totalCols);
                    } else {
                        // 小数据量直接渲染，添加安全检查
                        try {
                            htmlTable = XLSX.utils.sheet_to_html(worksheet, {
                                table: true,
                                header: 1,
                                editable: false,
                                cellHTML: false,
                                defval: '' // 为空单元格设置默认值
                            });
                        } catch (sheetError) {
                            console.warn('XLSX.utils.sheet_to_html失败，使用自定义渲染:', sheetError);
                            htmlTable = this.renderExcelTableSafely(worksheet, totalRows, totalCols);
                        }
                    }
                } catch (error) {
                    console.error('SheetJS渲染错误:', error);
                    contentContainer.innerHTML = `
                        <div class="document-error">
                            <i class="bi bi-exclamation-triangle icon"></i>
                            <div class="message">工作表渲染失败</div>
                            <div class="description">无法渲染工作表内容: ${error.message}</div>
                            <div class="suggestion">
                                <small>建议：<a href="${config.fileUrl}" download>下载原文件</a> 查看完整内容</small>
                            </div>
                        </div>
                    `;
                    return;
                }

                // 增强表格样式和功能
                contentContainer.innerHTML = `
                    <div class="excel-table-container">
                        <div class="excel-table-wrapper">
                            ${htmlTable}
                        </div>
                        <div class="excel-table-info">
                            <span>工作表: ${sheetName}</span>
                            <span>数据范围: ${worksheet['!ref'] || 'A1'}</span>
                        </div>
                    </div>
                `;

                // 添加表格增强功能
                this.enhanceExcelTable();

            } catch (error) {
                console.error('Excel工作表渲染错误:', error);
                console.error('错误堆栈:', error.stack);

                // 特别处理indexOf错误
                let errorMessage = error.message || '未知错误';
                if (errorMessage.includes('indexOf')) {
                    errorMessage = '数据格式错误，可能包含无效的单元格数据';
                } else if (errorMessage.includes('Cannot read properties')) {
                    errorMessage = '数据结构错误，无法读取单元格属性';
                }

                contentContainer.innerHTML = `
                    <div class="document-error">
                        <i class="bi bi-exclamation-triangle icon"></i>
                        <div class="message">工作表渲染失败</div>
                        <div class="description">${this.escapeHtml(errorMessage)}</div>
                        <div class="suggestion">
                            <small>建议：<a href="${this.currentConfig ? this.currentConfig.fileUrl : '#'}" download>下载原文件</a> 查看完整内容</small>
                        </div>
                    </div>
                `;
            }
        }, 10);
    }

    /**
     * 渲染大数据量Excel表格（分页模式）
     */
    renderLargeExcelTable(worksheet, totalRows, totalCols) {
        const pageSize = 100; // 每页显示100行
        const maxCols = Math.min(totalCols, 20); // 最多显示20列

        let html = `
            <div class="excel-large-table-container">
                <div class="excel-pagination-info">
                    <span>数据量较大 (${totalRows}行 x ${totalCols}列)，已启用分页模式</span>
                    <div class="excel-pagination-controls">
                        <button onclick="documentViewer.loadExcelPage(0)" class="excel-page-btn active" data-page="0">第1页</button>
                        <button onclick="documentViewer.loadExcelPage(1)" class="excel-page-btn" data-page="1">第2页</button>
                        <button onclick="documentViewer.loadExcelPage(2)" class="excel-page-btn" data-page="2">第3页</button>
                        <span>...</span>
                        <span>共${Math.ceil(totalRows / pageSize)}页</span>
                    </div>
                </div>
                <div class="excel-table-wrapper" id="excelTableContent">
                    ${this.renderExcelPage(worksheet, 0, pageSize, maxCols)}
                </div>
            </div>
        `;

        // 保存当前工作表信息用于分页
        this.currentWorksheet = worksheet;
        this.currentPageSize = pageSize;
        this.currentMaxCols = maxCols;
        this.currentTotalRows = totalRows;

        return html;
    }

    /**
     * 安全渲染Excel表格（当XLSX.utils.sheet_to_html失败时使用）
     */
    renderExcelTableSafely(worksheet, totalRows, totalCols) {
        if (!worksheet || typeof worksheet !== 'object') {
            return '<div class="excel-error">工作表数据无效</div>';
        }

        const maxRows = Math.min(totalRows || 100, 500); // 限制最大行数
        const maxCols = Math.min(totalCols || 20, 30);   // 限制最大列数

        let html = '<table class="excel-enhanced-table">';

        // 渲染表头
        html += '<thead><tr>';
        for (let col = 0; col < maxCols; col++) {
            const cellAddress = XLSX.utils.encode_cell({r: 0, c: col});
            const cell = worksheet[cellAddress];
            let cellValue = '';

            try {
                if (cell && cell.v !== undefined && cell.v !== null) {
                    cellValue = String(cell.v);
                }
            } catch (e) {
                cellValue = '';
            }

            html += `<th class="excel-header-cell">${this.escapeHtml(cellValue)}</th>`;
        }
        html += '</tr></thead>';

        // 渲染数据行
        html += '<tbody>';
        for (let row = 1; row < maxRows; row++) {
            html += '<tr>';
            for (let col = 0; col < maxCols; col++) {
                const cellAddress = XLSX.utils.encode_cell({r: row, c: col});
                const cell = worksheet[cellAddress];
                let cellValue = '';

                try {
                    if (cell && cell.v !== undefined && cell.v !== null) {
                        cellValue = String(cell.v);
                    }
                } catch (e) {
                    cellValue = '';
                }

                html += `<td>${this.escapeHtml(cellValue)}</td>`;
            }
            html += '</tr>';
        }
        html += '</tbody></table>';

        return html;
    }

    /**
     * 渲染Excel指定页面
     */
    renderExcelPage(worksheet, page, pageSize, maxCols) {
        const startRow = page * pageSize;
        const endRow = Math.min(startRow + pageSize, this.currentTotalRows || 1000);

        let html = '<table class="excel-enhanced-table">';

        // 渲染表头（如果是第一页或者需要显示表头）
        if (page === 0) {
            html += '<thead><tr>';
            for (let col = 0; col < maxCols; col++) {
                const cellAddress = XLSX.utils.encode_cell({r: 0, c: col});
                const cell = worksheet[cellAddress];
                let cellValue = '';

                try {
                    if (cell && cell.v !== undefined && cell.v !== null) {
                        cellValue = String(cell.v);
                    }
                } catch (e) {
                    cellValue = '';
                }

                html += `<th class="excel-header-cell">${this.escapeHtml(cellValue)}</th>`;
            }
            html += '</tr></thead>';
        }

        // 渲染数据行
        html += '<tbody>';
        for (let row = Math.max(startRow, 1); row < endRow; row++) {
            html += '<tr>';
            for (let col = 0; col < maxCols; col++) {
                const cellAddress = XLSX.utils.encode_cell({r: row, c: col});
                const cell = worksheet[cellAddress];
                let cellValue = '';

                try {
                    if (cell && cell.v !== undefined && cell.v !== null) {
                        cellValue = String(cell.v);
                    }
                } catch (e) {
                    cellValue = '';
                }

                html += `<td>${this.escapeHtml(cellValue)}</td>`;
            }
            html += '</tr>';
        }
        html += '</tbody></table>';

        return html;
    }

    /**
     * 加载Excel指定页面
     */
    loadExcelPage(page) {
        if (!this.currentWorksheet) return;

        // 更新分页按钮状态
        document.querySelectorAll('.excel-page-btn').forEach(btn => {
            btn.classList.toggle('active', parseInt(btn.dataset.page) === page);
        });

        // 渲染新页面
        const content = this.renderExcelPage(this.currentWorksheet, page, this.currentPageSize, this.currentMaxCols);
        document.getElementById('excelTableContent').innerHTML = content;

        // 添加表格增强功能
        this.enhanceExcelTable();
    }

    /**
     * 增强Excel表格功能
     */
    enhanceExcelTable() {
        const table = document.querySelector('.excel-table-wrapper table');
        if (!table) return;

        // 添加表格样式类
        table.classList.add('excel-enhanced-table');

        // 为表头添加固定样式
        const headerRows = table.querySelectorAll('tr:first-child th, tr:first-child td');
        headerRows.forEach(cell => {
            cell.classList.add('excel-header-cell');
        });

        // 为数据行添加斑马纹效果
        const dataRows = table.querySelectorAll('tr:not(:first-child)');
        dataRows.forEach((row, index) => {
            if (index % 2 === 1) {
                row.classList.add('excel-even-row');
            }
        });

        // 添加单元格悬停效果
        const cells = table.querySelectorAll('td, th');
        cells.forEach(cell => {
            cell.addEventListener('mouseenter', () => {
                cell.classList.add('excel-cell-hover');
            });
            cell.addEventListener('mouseleave', () => {
                cell.classList.remove('excel-cell-hover');
            });
        });
    }

    /**
     * 加载Markdown查看器
     */
    async loadMarkdownViewer(config) {
        // 确保marked.js已加载
        if (typeof marked === 'undefined') {
            await this.loadScript('/wkg/js/vendor/marked.min.js');
        }
        
        const html = `
            <div class="document-viewer-container">
                <div class="document-viewer-content">
                    <div class="markdown-preview-only" id="markdownPreview"></div>
                    <div class="markdown-viewer" id="markdownEditor" style="display: none;">
                        <div class="markdown-editor" id="markdownTextarea"></div>
                        <div class="markdown-preview" id="markdownPreviewEdit"></div>
                    </div>
                </div>
            </div>
        `;
        
        this.container.innerHTML = html;
        
        try {
            const response = await fetch(config.fileUrl);
            const content = await response.text();
            
            // 渲染预览
            const html = marked.parse(content);
            document.getElementById('markdownPreview').innerHTML = html;
            
            if (config.isEditable) {
                // 如果可编辑，准备编辑器
                this.markdownContent = content;
            }
        } catch (error) {
            document.getElementById('markdownPreview').innerHTML = `
                <div class="document-error">
                    <i class="bi bi-exclamation-triangle icon"></i>
                    <div class="message">Markdown文件加载失败</div>
                    <div class="description">${error.message}</div>
                </div>
            `;
        }
    }
    
    /**
     * 加载图片查看器
     */
    async loadImageViewer(config) {
        const html = `
            <div class="document-viewer-container">
                <div class="document-viewer-content">
                    <div class="image-viewer">
                        <img id="imageContent" src="${config.fileUrl}" alt="${this.escapeHtml(config.fileName)}"
                             style="transform: scale(${this.currentZoom}) rotate(${this.currentRotation}deg);">
                        <div class="image-controls">
                            <button onclick="documentViewer.zoomOut()">
                                <i class="bi bi-zoom-out"></i>
                            </button>
                            <button onclick="documentViewer.resetZoom()">
                                <i class="bi bi-aspect-ratio"></i>
                            </button>
                            <button onclick="documentViewer.zoomIn()">
                                <i class="bi bi-zoom-in"></i>
                            </button>
                            <button onclick="documentViewer.rotateImage()">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.container.innerHTML = html;
    }

    /**
     * 加载文本查看器
     */
    async loadTextViewer(config) {
        const html = `
            <div class="document-viewer-container">
                <div class="document-viewer-content">
                    <div class="text-viewer">
                        <div class="text-content" id="textContent"></div>
                        <textarea class="form-control" id="textEditor" style="display: none; width: 100%; height: 100%; border: none; resize: none; font-family: 'Courier New', monospace;"></textarea>
                    </div>
                </div>
            </div>
        `;

        this.container.innerHTML = html;

        try {
            const response = await fetch(config.fileUrl);
            const content = await response.text();

            document.getElementById('textContent').textContent = content;
            if (config.isEditable) {
                document.getElementById('textEditor').value = content;
                this.textContent = content;
            }
        } catch (error) {
            document.getElementById('textContent').innerHTML = `
                <div class="document-error">
                    <i class="bi bi-exclamation-triangle icon"></i>
                    <div class="message">文本文件加载失败</div>
                    <div class="description">${error.message}</div>
                </div>
            `;
        }
    }

    /**
     * 显示不支持的文件类型
     */
    showUnsupported(config) {
        const extension = this.getFileExtension(config.fileName).toUpperCase();
        const html = `
            <div class="document-viewer-container">
                <div class="document-viewer-content">
                    <div class="unsupported-viewer">
                        <i class="bi bi-file-earmark icon"></i>
                        <div class="message">不支持的文件类型</div>
                        <div class="description">
                            此文件类型暂不支持在线预览。
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.container.innerHTML = html;
    }
    
    /**
     * 工具方法
     */
    getFileExtension(fileName) {
        return fileName.split('.').pop() || '';
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    showLoading(message = '正在加载...') {
        this.container.innerHTML = this.getLoadingHTML(message);
    }
    
    showError(message) {
        this.container.innerHTML = `
            <div class="document-error">
                <i class="bi bi-exclamation-triangle icon"></i>
                <div class="message">加载失败</div>
                <div class="description">${this.escapeHtml(message)}</div>
            </div>
        `;
    }
    
    getLoadingHTML(message = '正在加载...') {
        return `
            <div class="document-loading">
                <div class="spinner"></div>
                <div class="message">${this.escapeHtml(message)}</div>
            </div>
        `;
    }
    
    async loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
    
    async loadMonacoEditor() {
        // 加载Monaco Editor
        if (!window.require) {
            await this.loadScript('/wkg/js/vendor/monaco-editor/min/vs/loader.js');
        }

        return new Promise((resolve) => {
            require.config({ paths: { vs: '/wkg/js/vendor/monaco-editor/min/vs' } });
            require(['vs/editor/editor.main'], resolve);
        });
    }
    
    // 操作方法
    downloadFile() {
        if (this.currentConfig && this.currentConfig.fileUrl) {
            const link = document.createElement('a');
            link.href = this.currentConfig.fileUrl;
            link.download = this.currentConfig.fileName;
            link.click();
        }
    }

    printFile() {
        window.print();
    }

    // 图片查看器操作
    zoomIn() {
        this.currentZoom = Math.min(this.currentZoom * 1.2, 5);
        this.updateImageTransform();
    }

    zoomOut() {
        this.currentZoom = Math.max(this.currentZoom / 1.2, 0.1);
        this.updateImageTransform();
    }

    resetZoom() {
        this.currentZoom = 1;
        this.currentRotation = 0;
        this.updateImageTransform();
    }

    rotateImage() {
        this.currentRotation = (this.currentRotation + 90) % 360;
        this.updateImageTransform();
    }

    updateImageTransform() {
        const img = document.getElementById('imageContent');
        if (img) {
            img.style.transform = `scale(${this.currentZoom}) rotate(${this.currentRotation}deg)`;
        }
    }

    // 代码编辑器操作
    async saveCode() {
        if (this.currentEditor && this.currentConfig) {
            const content = this.currentEditor.getValue();
            try {
                await this.saveFileContent(content);
                this.showToast('代码保存成功', 'success');
            } catch (error) {
                this.showToast('代码保存失败: ' + error.message, 'error');
            }
        }
    }

    // Markdown编辑器操作
    toggleMarkdownMode() {
        const previewOnly = document.getElementById('markdownPreview');
        const editorView = document.getElementById('markdownEditor');
        const editBtn = document.getElementById('editModeBtn');
        const saveBtn = document.getElementById('saveMarkdownBtn');

        if (previewOnly.style.display !== 'none') {
            // 切换到编辑模式
            previewOnly.style.display = 'none';
            editorView.style.display = 'flex';
            editBtn.innerHTML = '<i class="bi bi-eye"></i> 预览';
            saveBtn.style.display = 'inline-block';

            // 初始化编辑器
            if (!this.markdownEditor) {
                this.initMarkdownEditor();
            }
        } else {
            // 切换到预览模式
            previewOnly.style.display = 'block';
            editorView.style.display = 'none';
            editBtn.innerHTML = '<i class="bi bi-pencil"></i> 编辑';
            saveBtn.style.display = 'none';
        }
    }

    async initMarkdownEditor() {
        if (typeof monaco === 'undefined') {
            await this.loadMonacoEditor();
        }

        this.markdownEditor = monaco.editor.create(document.getElementById('markdownTextarea'), {
            value: this.markdownContent,
            language: 'markdown',
            theme: 'vs-light',
            automaticLayout: true,
            wordWrap: 'on',
            minimap: { enabled: false }
        });

        // 实时预览
        this.markdownEditor.onDidChangeModelContent(() => {
            const content = this.markdownEditor.getValue();
            const html = marked.parse(content);
            document.getElementById('markdownPreviewEdit').innerHTML = html;
        });

        // 初始预览
        const html = marked.parse(this.markdownContent);
        document.getElementById('markdownPreviewEdit').innerHTML = html;
    }

    async saveMarkdown() {
        if (this.markdownEditor && this.currentConfig) {
            const content = this.markdownEditor.getValue();
            try {
                await this.saveFileContent(content);
                this.markdownContent = content;
                // 更新只读预览
                const html = marked.parse(content);
                document.getElementById('markdownPreview').innerHTML = html;
                this.showToast('Markdown保存成功', 'success');
            } catch (error) {
                this.showToast('Markdown保存失败: ' + error.message, 'error');
            }
        }
    }

    // 文本编辑器操作
    toggleTextEdit() {
        const textContent = document.getElementById('textContent');
        const textEditor = document.getElementById('textEditor');
        const editBtn = document.getElementById('editTextBtn');
        const saveBtn = document.getElementById('saveTextBtn');

        if (textContent.style.display !== 'none') {
            // 切换到编辑模式
            textContent.style.display = 'none';
            textEditor.style.display = 'block';
            editBtn.innerHTML = '<i class="bi bi-eye"></i> 预览';
            saveBtn.style.display = 'inline-block';
            textEditor.focus();
        } else {
            // 切换到预览模式
            textContent.style.display = 'block';
            textEditor.style.display = 'none';
            editBtn.innerHTML = '<i class="bi bi-pencil"></i> 编辑';
            saveBtn.style.display = 'none';
        }
    }

    async saveText() {
        const textEditor = document.getElementById('textEditor');
        if (textEditor && this.currentConfig) {
            const content = textEditor.value;
            try {
                await this.saveFileContent(content);
                this.textContent = content;
                document.getElementById('textContent').textContent = content;
                this.showToast('文本保存成功', 'success');
            } catch (error) {
                this.showToast('文本保存失败: ' + error.message, 'error');
            }
        }
    }

    // 通用保存方法
    async saveFileContent(content) {
        const response = await fetch(`/api/knowledgebase/nodes/${this.currentConfig.nodeId}/content`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'text/plain',
            },
            body: content
        });

        if (!response.ok) {
            throw new Error('保存失败: ' + response.statusText);
        }
    }

    // 显示提示消息
    showToast(message, type = 'info') {
        // 这里可以集成现有的toast组件
        if (typeof showToast === 'function') {
            showToast(message, type);
        } else {
            alert(message);
        }
    }
    
    destroy() {
        if (this.currentEditor) {
            this.currentEditor.dispose();
            this.currentEditor = null;
        }
        this.container.innerHTML = '';
    }
}

// 初始化函数
function initDocumentViewer(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        return new DocumentViewer(container);
    }
    return null;
}
