/**
 * 知识库空间管理 JavaScript
 * 专注于团队知识库空间的管理，不包含个人空间
 */

// 全局变量
const CONTEXT_PATH = '/wkg'; // 应用上下文路径
let availableRoles = [];
let currentEditSpaceId = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadSpaces();
    loadAvailableRoles();
});

/**
 * 加载知识空间列表（用于管理页面）
 */
function loadSpaces() {
    // 显示加载状态
    const container = document.getElementById('spaceCardsContainer');
    if (container) {
        container.innerHTML = '<div class="col-12"><div class="text-center py-5"><i class="bi bi-hourglass-split text-muted"></i> 加载中...</div></div>';
    }

    fetch(`${CONTEXT_PATH}/api/knowledge/spaces/all`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success || data.code === 200) {
                renderSpaceCards(data.data || []);
            } else {
                showToast('加载知识空间失败: ' + (data.message || '未知错误'), 'danger');
            }
        })
        .catch(error => {
            console.error('Error loading spaces:', error);
            showToast('网络错误，请稍后重试', 'danger');
        });
}

/**
 * 加载可用角色列表
 */
function loadAvailableRoles() {
    fetch(`${CONTEXT_PATH}/api/knowledge/spaces/available-roles`)
        .then(response => response.json())
        .then(data => {
            if (data.success || data.code === 200) {
                availableRoles = data.data || [];
                renderRoleCheckboxes();
            } else {
                console.warn('加载角色列表失败:', data.message);
            }
        })
        .catch(error => {
            console.error('Error loading roles:', error);
        });
}

/**
 * 渲染角色复选框列表
 */
function renderRoleCheckboxes() {
    const container = document.getElementById('roleCheckboxList');
    if (!container || !availableRoles) return;

    container.innerHTML = availableRoles.map(role => `
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" value="${role.id}" id="role_${role.id}">
            <label class="form-check-label" for="role_${role.id}">
                <strong>${role.roleName || role.name}</strong>
                ${role.description ? `<br><small class="text-muted">${role.description}</small>` : ''}
            </label>
        </div>
    `).join('');
}

/**
 * 渲染空间卡片
 */
function renderSpaceCards(spaces) {
    const container = document.getElementById('spaceCardsContainer');

    if (!spaces || spaces.length === 0) {
        container.innerHTML = `
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="bi bi-folder-x text-muted" style="font-size: 3rem;"></i>
                    <h5 class="text-muted mt-3">暂无知识库空间</h5>
                    <p class="text-muted">点击"创建知识库空间"按钮开始创建团队知识库</p>
                </div>
            </div>
        `;
        return;
    }
    
    container.innerHTML = spaces.map(space => `
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h5 class="card-title mb-0">${space.name}</h5>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary" type="button"
                                    data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="editSpacePermissions('${space.spaceId}')">
                                    <i class="bi bi-shield-check me-2"></i>编辑权限
                                </a></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteSpace('${space.spaceId}')">
                                    <i class="bi bi-trash me-2"></i>删除空间
                                </a></li>
                            </ul>
                        </div>
                    </div>

                    <p class="card-text text-muted">${space.description || '暂无描述'}</p>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="bi bi-${getAccessTypeIcon(space.accessType)} me-1"></i>
                            ${getAccessTypeText(space.accessType)}
                        </small>
                        <small class="text-muted">
                            ${formatDate(space.createTime)}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

/**
 * 获取访问类型图标
 */
function getAccessTypeIcon(accessType) {
    switch (accessType) {
        case 'PUBLIC': return 'globe';
        case 'PRIVATE': return 'lock';
        case 'ROLE_BASED': return 'people';
        default: return 'question-circle';
    }
}

/**
 * 获取访问类型文本
 */
function getAccessTypeText(accessType) {
    switch (accessType) {
        case 'PUBLIC': return '公开';
        case 'PRIVATE': return '私有';
        case 'ROLE_BASED': return '基于角色';
        default: return '未知';
    }
}

/**
 * 格式化日期
 */
function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

/**
 * 显示创建空间模态框
 */
function showCreateSpaceModal() {
    const modal = new bootstrap.Modal(document.getElementById('createSpaceModal'));
    
    // 重置表单
    document.getElementById('createSpaceForm').reset();

    // 清空角色选择
    const checkboxes = document.querySelectorAll('#roleCheckboxList input[type="checkbox"]');
    checkboxes.forEach(checkbox => checkbox.checked = false);

    // 显示角色权限配置区域（默认为基于角色）
    toggleRolePermissions('ROLE_BASED');

    // 确保必填标记正确显示
    const accessTypeSelect = document.querySelector('select[name="accessType"]');
    if (accessTypeSelect) {
        accessTypeSelect.value = 'ROLE_BASED';
    }

    // 初始化字符计数
    initCharacterCount();
    
    modal.show();
}

/**
 * 切换角色权限配置显示
 */
function toggleRolePermissions(accessType) {
    const section = document.getElementById('rolePermissionsSection');
    if (section) {
        section.style.display = accessType === 'ROLE_BASED' ? 'block' : 'none';
    }

    // 动态更新必填标记
    const roleLabel = section.querySelector('.form-label');
    if (roleLabel) {
        if (accessType === 'ROLE_BASED') {
            if (!roleLabel.innerHTML.includes('text-danger')) {
                roleLabel.innerHTML = '可访问角色 <span class="text-danger">*</span>';
            }
        } else {
            roleLabel.innerHTML = '可访问角色';
        }
    }
}

/**
 * 创建知识空间
 */
function createSpace() {
    const form = document.getElementById('createSpaceForm');
    const formData = new FormData(form);
    
    // 构建请求数据
    const requestData = {
        name: formData.get('name'),
        description: formData.get('description'),
        accessType: formData.get('accessType'),
        rolePermissions: []
    };

    // 收集选中的角色（默认给予完整权限）
    const checkedRoles = document.querySelectorAll('#roleCheckboxList input[type="checkbox"]:checked');
    checkedRoles.forEach(checkbox => {
        requestData.rolePermissions.push({
            roleId: parseInt(checkbox.value),
            permissionLevel: 'admin' // 默认给予完整权限
        });
    });

    // 验证必填字段
    if (!requestData.name) {
        showToast('请输入知识库空间名称', 'warning');
        return;
    }

    if (!requestData.accessType) {
        showToast('请选择访问类型', 'warning');
        return;
    }

    if (requestData.accessType === 'ROLE_BASED' && requestData.rolePermissions.length === 0) {
        showToast('基于角色的访问类型需要至少选择一个角色', 'warning');
        return;
    }
    
    // 发送创建请求
    fetch(`${CONTEXT_PATH}/api/knowledge/spaces`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success || data.code === 200) {
            showToast('知识库空间创建成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('createSpaceModal')).hide();
            loadSpaces(); // 重新加载空间列表
        } else {
            showToast('创建失败: ' + (data.message || '未知错误'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error creating space:', error);
        showToast('网络错误，请稍后重试', 'danger');
    });
}

/**
 * 编辑空间权限
 */
function editSpacePermissions(spaceId) {
    currentEditSpaceId = spaceId;
    document.getElementById('editSpaceId').value = spaceId;

    // 加载当前权限配置
    fetch(`${CONTEXT_PATH}/api/knowledge/spaces/${spaceId}/role-permissions`)
        .then(response => response.json())
        .then(data => {
            if (data.success || data.code === 200) {
                renderEditRoleCheckboxes(data.data || []);
                const modal = new bootstrap.Modal(document.getElementById('editSpacePermissionsModal'));
                modal.show();
            } else {
                showToast('加载权限配置失败: ' + (data.message || '未知错误'), 'danger');
            }
        })
        .catch(error => {
            console.error('Error loading permissions:', error);
            showToast('网络错误，请稍后重试', 'danger');
        });
}

/**
 * 渲染编辑权限的角色复选框
 */
function renderEditRoleCheckboxes(permissions) {
    const container = document.getElementById('editRoleCheckboxList');
    if (!container || !availableRoles) return;

    // 获取当前已有权限的角色ID列表
    const currentRoleIds = permissions.map(p => p.roleId);

    container.innerHTML = availableRoles.map(role => `
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" value="${role.id}" id="edit_role_${role.id}"
                   ${currentRoleIds.includes(role.id) ? 'checked' : ''}>
            <label class="form-check-label" for="edit_role_${role.id}">
                <strong>${role.roleName || role.name}</strong>
                ${role.description ? `<br><small class="text-muted">${role.description}</small>` : ''}
            </label>
        </div>
    `).join('');
}

// 移除了复杂的编辑角色权限配置函数，改为简单的复选框

/**
 * 更新空间权限
 */
function updateSpacePermissions() {
    if (!currentEditSpaceId) return;

    // 收集选中的角色（默认给予完整权限）
    const rolePermissions = [];
    const checkedRoles = document.querySelectorAll('#editRoleCheckboxList input[type="checkbox"]:checked');

    checkedRoles.forEach(checkbox => {
        rolePermissions.push({
            roleId: parseInt(checkbox.value),
            permissionLevel: 'admin' // 默认给予完整权限
        });
    });

    // 发送更新请求
    fetch(`${CONTEXT_PATH}/api/knowledge/spaces/${currentEditSpaceId}/role-permissions`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(rolePermissions)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success || data.code === 200) {
            showToast('权限配置更新成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('editSpacePermissionsModal')).hide();
            loadSpaces(); // 重新加载空间列表
        } else {
            showToast('更新失败: ' + (data.message || '未知错误'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error updating permissions:', error);
        showToast('网络错误，请稍后重试', 'danger');
    });
}

/**
 * 删除知识库空间
 */
function deleteSpace(spaceId) {
    if (!confirm('确定要删除这个知识库空间吗？此操作不可撤销，空间内的所有文档也将被删除。')) {
        return;
    }

    fetch(`${CONTEXT_PATH}/api/knowledge/spaces/${spaceId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success || data.code === 200) {
            showToast('知识库空间删除成功', 'success');
            loadSpaces(); // 重新加载空间列表
        } else {
            showToast('删除失败: ' + (data.message || '未知错误'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error deleting space:', error);
        showToast('网络错误，请稍后重试', 'danger');
    });
}

/**
 * 显示Toast提示 - 使用EdgeMind组件
 */
function showToast(message, type = 'info') {
    // 检查是否有全局的showToast函数，避免递归调用
    if (typeof window.showToast === 'function' && window.showToast !== showToast) {
        window.showToast(message, type);
    } else {
        // 回退到console.log和简单的页面提示
        console.log(`${type.toUpperCase()}: ${message}`);

        // 创建简单的页面提示
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        document.body.appendChild(alertDiv);

        // 自动移除提示
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }
}

/**
 * 初始化字符计数功能
 */
function initCharacterCount() {
    const nameInput = document.querySelector('input[name="name"]');
    const descInput = document.querySelector('textarea[name="description"]');

    if (nameInput) {
        const nameHelpText = nameInput.nextElementSibling;
        nameInput.addEventListener('input', function() {
            const length = this.value.length;
            const maxLength = this.getAttribute('maxlength') || 20;
            nameHelpText.textContent = `${length}/${maxLength} 个字符`;

            if (length > maxLength * 0.8) {
                nameHelpText.className = 'form-text text-warning';
            } else {
                nameHelpText.className = 'form-text text-muted';
            }
        });
    }

    if (descInput) {
        const descHelpText = descInput.nextElementSibling;
        descInput.addEventListener('input', function() {
            const length = this.value.length;
            const maxLength = this.getAttribute('maxlength') || 100;
            descHelpText.textContent = `${length}/${maxLength} 个字符`;

            if (length > maxLength * 0.8) {
                descHelpText.className = 'form-text text-warning';
            } else {
                descHelpText.className = 'form-text text-muted';
            }
        });
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    loadSpaces();
    loadAvailableRoles();
});
