// src/main/resources/static/js/features/permission/departments.js

document.addEventListener('DOMContentLoaded', () => {
    

    const departmentTreeContainer = document.getElementById('departmentTreeContainer');
    const departmentDetailsContainer = document.getElementById('departmentDetailsContainer');
    const addDepartmentModalElement = document.getElementById('addDepartmentModal');
    const addDepartmentModal = new bootstrap.Modal(addDepartmentModalElement);
    const editDepartmentModalElement = document.getElementById('editDepartmentModal');
    const editDepartmentModal = new bootstrap.Modal(editDepartmentModalElement);

    const addDepartmentForm = document.getElementById('addDepartmentForm');
    const editDepartmentForm = document.getElementById('editDepartmentForm');

    let selectedDepartmentId = null;

    // --- Function to fetch department tree data --- 
    async function fetchDepartmentTree() {
        departmentTreeContainer.innerHTML = `<div class="text-center p-3"><div class="spinner-border spinner-border-sm text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>`;
        departmentDetailsContainer.innerHTML = '';
        try {
            const response = await fetch('/wkg/api/permission/departments/tree');
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const treeData = await response.json();
            renderDepartmentTree(treeData || []);
            
            // 查找当前选中的部门节点
            let nodeToSelect = null;
            
            // 如果已有选中的部门ID并且它仍存在于树中
            if (selectedDepartmentId) {
                const selectedNodeElement = departmentTreeContainer.querySelector(`.tree-node[data-dept-id="${selectedDepartmentId}"] > .node-content`);
                if (selectedNodeElement) {
                    selectedNodeElement.classList.add('active');
                    nodeToSelect = selectedDepartmentId;
                }
            }
            
            // 如果没有选中的部门或者它不存在于树中，选择第一个部门
            if (!nodeToSelect && treeData && treeData.length > 0) {
                const firstNodeId = treeData[0].id;
                const firstNodeElement = departmentTreeContainer.querySelector(`.tree-node[data-dept-id="${firstNodeId}"] > .node-content`);
                if (firstNodeElement) {
                    firstNodeElement.classList.add('active');
                    selectedDepartmentId = firstNodeId;
                    nodeToSelect = firstNodeId;
                }
            }
            
            // 显示选中部门的详情
            if (nodeToSelect) {
                fetchAndRenderDepartmentDetails(nodeToSelect);
            } else {
                // 如果没有找到任何部门，显示提示信息
                departmentDetailsContainer.innerHTML = '<div class="text-center text-muted p-5">未找到任何部门信息</div>';
            }
        } catch (error) {
            console.error('Error fetching department tree:', error);
            showToast('加载部门树错误', 'error');
            departmentTreeContainer.innerHTML = '<div class="alert alert-danger">加载部门失败。</div>';
            departmentDetailsContainer.innerHTML = '<div class="text-center text-muted p-5">加载部门信息失败</div>';
        }
    }

    // --- Function to render the department tree --- 
    function renderDepartmentTree(nodes) {
        departmentTreeContainer.innerHTML = ''; 
        if (!nodes || nodes.length === 0) {
            departmentTreeContainer.innerHTML = '<div class="text-center text-muted p-3">No departments found.</div>';
            return;
        }
        function buildNodeHtml(node, level = 0) {
            const hasChildren = node.children && node.children.length > 0;
            const toggleIcon = hasChildren ? 'bi-chevron-right' : 'bi-dot';
            let nodeIconClass = 'bi-folder';
            let childrenHtml = '';
            if (hasChildren) {
                childrenHtml = `<div class="children" style="display: none;">${node.children.map(child => buildNodeHtml(child, level + 1)).join('')}</div>`;
            }
            return `
                <div class="tree-node" data-dept-id="${node.id}">
                    <div class="node-content d-flex align-items-center" role="button" tabindex="0">
                        <div class="node-toggle me-1 ${!hasChildren ? 'invisible' : ''}" style="min-width: 16px; display: flex; justify-content: center;"><i class="bi ${toggleIcon}"></i></div>
                        <div class="node-icon me-2" style="min-width: 20px; display: flex; justify-content: center;"><i class="bi ${nodeIconClass}"></i></div>
                        <div class="node-text flex-grow-1 text-truncate ${level === 0 ? 'fw-bold' : ''}" style="max-width: 150px;">${node.deptName || 'Unnamed Dept'}</div>
                        <div class="node-actions" style="opacity: 0; transition: opacity 0.2s ease; margin-left: auto;">
                            <div class="btn-group btn-group-sm">
                                ${level >= 0 ? `<button class="btn btn-outline-primary btn-sm btn-add-child" title="添加子部门" data-parent-id="${node.id}" data-parent-name="${node.deptName}"><i class="bi bi-plus"></i></button>` : '' } 
                                <button class="btn btn-outline-secondary btn-sm btn-edit" title="编辑" data-dept-id="${node.id}"><i class="bi bi-pencil"></i></button>
                                ${level >= 0 ? `<button class="btn btn-outline-danger btn-sm btn-delete" title="删除" data-dept-id="${node.id}" data-dept-name="${node.deptName}"><i class="bi bi-trash"></i></button>` : ''} 
                            </div>
                        </div>
                    </div>
                    ${childrenHtml}
                </div>
            `;
        }
        departmentTreeContainer.innerHTML = nodes.map(node => buildNodeHtml(node)).join('');
        
        // 防止多次添加样式
        const existingStyle = document.getElementById('department-tree-style');
        if (existingStyle) {
            existingStyle.remove();
        }
        
        // 添加CSS让按钮在hover时显示
        const style = document.createElement('style');
        style.id = 'department-tree-style';
        style.textContent = `
            .tree-node .node-content:hover .node-actions {
                opacity: 1 !important;
            }
            .tree-node .node-content {
                height: 38px;
                padding: 0.25rem 0.5rem;
                border-radius: 0.25rem;
                margin-bottom: 2px;
                transition: background-color 0.2s ease;
                user-select: none;
            }
            .tree-node .node-content:hover {
                background-color: rgba(0,0,0,0.05);
            }
            .tree-node .node-content.active {
                background-color: rgba(13,110,253,0.1) !important;
            }
            .tree-node .node-toggle {
                cursor: pointer;
            }
            .tree-node .children {
                padding-left: 1.5rem;
            }
        `;
        document.head.appendChild(style);
        
        addTreeEventListeners();
        if (selectedDepartmentId) {
            const selectedNodeElement = departmentTreeContainer.querySelector(`.tree-node[data-dept-id="${selectedDepartmentId}"] > .node-content`);
            selectedNodeElement?.classList.add('active');
        }
    }
    
    // --- Add Event Listeners for Tree Nodes --- 
    function addTreeEventListeners() {
        // 给节点内容添加点击事件（选择节点）
        departmentTreeContainer.querySelectorAll('.node-content').forEach(el => {
            el.addEventListener('click', handleNodeClick);
            el.addEventListener('keydown', (e) => { if (e.key === 'Enter') handleNodeClick(e); });
        });
        
        // 给展开/折叠按钮添加单独的点击事件
        departmentTreeContainer.querySelectorAll('.node-toggle').forEach(el => {
            el.addEventListener('click', handleToggleClick);
        });
        
        // 操作按钮的点击事件
        departmentTreeContainer.querySelectorAll('.btn-add-child').forEach(button => {
            button.addEventListener('click', handleAddChildClick);
        });
        departmentTreeContainer.querySelectorAll('.btn-edit').forEach(button => {
            button.addEventListener('click', handleEditClick);
        });
        departmentTreeContainer.querySelectorAll('.btn-delete').forEach(button => {
            button.addEventListener('click', handleDeleteClick);
        });
    }

    // --- Tree Node Click Handler --- 
    function handleNodeClick(event) {
        // 忽略来自操作按钮和展开图标的点击
        if (event.target.closest('.btn-add-child, .btn-edit, .btn-delete, .node-toggle')) {
            event.stopPropagation();
            return;
        }
        
        // 阻止默认行为，防止闪烁
        event.preventDefault();
        
        const nodeContent = event.currentTarget;
        const treeNode = nodeContent.closest('.tree-node');
        const deptId = treeNode.dataset.deptId;
        
        // 防止重复点击同一个节点触发数据加载
        if (selectedDepartmentId === parseInt(deptId)) {
            return;
        }
        
        // 移除之前选中的活动状态
        const currentActive = departmentTreeContainer.querySelector('.node-content.active');
        if (currentActive) {
            currentActive.classList.remove('active');
        }
        
        // 先设置选中状态，再加载数据
        nodeContent.classList.add('active');
        selectedDepartmentId = parseInt(deptId);
        
        // 在不影响UI的情况下加载详情数据
        setTimeout(() => {
            fetchAndRenderDepartmentDetails(deptId);
        }, 10);
    }

    // --- Tree Toggle Click Handler --- 
    function handleToggleClick(event) {
        event.stopPropagation(); 
        event.preventDefault();
        
        const toggleElement = event.currentTarget;
        const icon = toggleElement.querySelector('i');
        const treeNode = toggleElement.closest('.tree-node');
        const childrenDiv = treeNode?.querySelector('.children');
        
        if (childrenDiv) {
            const isHidden = childrenDiv.style.display === 'none';
            childrenDiv.style.display = isHidden ? 'block' : 'none';
            
            // 更新图标
            if (icon) {
                if (isHidden) {
                    icon.classList.remove('bi-chevron-right');
                    icon.classList.add('bi-chevron-down');
                } else {
                    icon.classList.remove('bi-chevron-down');
                    icon.classList.add('bi-chevron-right');
                }
            }
        }
    }

     // --- Fetch and Render Department Details --- 
     async function fetchAndRenderDepartmentDetails(deptId) {
        if (!deptId) {
             departmentDetailsContainer.innerHTML = '<div class="text-center text-muted p-5">请在左侧选择一个部门查看详情</div>';
             return;
         }
         
         // 不改变原有DOM结构，只在内部显示加载状态
         if (departmentDetailsContainer.innerHTML === '' || !departmentDetailsContainer.querySelector('.card')) {
             departmentDetailsContainer.innerHTML = `<div class="card h-100 d-flex justify-content-center align-items-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>`;
         } else {
             const cardBody = departmentDetailsContainer.querySelector('.card-body');
             if (cardBody) {
                 const originalContent = cardBody.innerHTML;
                 cardBody.innerHTML = `<div class="text-center p-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>`;
             }
         }
         
         try {
             const response = await fetch(`/wkg/api/permission/departments/${deptId}`);
             if (!response.ok) throw new Error(`Failed to fetch details: ${response.status}`);
             const deptData = await response.json();
             renderDepartmentDetails(deptData);
         } catch (error) {
            console.error("Error fetching department details:", error);
            showToast("Error loading department details.", "error");
             departmentDetailsContainer.innerHTML = '<div class="alert alert-danger">Failed to load details.</div>';
         }
     }

     // --- Render Department Details --- 
     function renderDepartmentDetails(dept) {
         if (!dept) {
             departmentDetailsContainer.innerHTML = '<div class="text-center text-muted p-5">部门信息未找到</div>';
             return;
         }
         const statusBadgeClass = dept.status === 1 ? 'bg-success-subtle text-success-emphasis' : 'bg-danger-subtle text-danger-emphasis';
         const statusText = dept.status === 1 ? '正常' : '停用';
         const managerName = dept.manager ? `${dept.manager.nickname || dept.manager.username || '?'}` : '未指定'; 
         const parentNameText = dept.parentName || (dept.parentId ? `(ID: ${dept.parentId})` : '无'); 
         departmentDetailsContainer.innerHTML = `
             <div class="card h-100">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                         <div><i class="bi bi-info-circle me-2"></i><span class="fw-bold">部门详情: <span class="text-primary">${dept.deptName || 'N/A'}</span></span></div>
                         <div><button class="btn btn-sm btn-outline-primary btn-edit-detail" data-dept-id="${dept.id}" title="编辑当前部门"><i class="bi bi-pencil me-1"></i> 编辑</button></div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="card mb-4 dept-info-card shadow-sm">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-2"><strong>部门编码:</strong> ${dept.deptCode || '-'}</p>
                                    <p class="mb-2"><strong>上级部门:</strong> ${parentNameText}</p>
                                    <p class="mb-2"><strong>负责人:</strong> ${managerName}</p>
                                </div>
                                <div class="col-md-6">
                                     <p class="mb-2"><strong>显示顺序:</strong> ${dept.sortOrder !== null ? dept.sortOrder : '-'}</p>
                                    <p class="mb-2"><strong>状态:</strong> <span class="badge ${statusBadgeClass}">${statusText}</span></p>
                                    <p class="mb-2"><strong>创建时间:</strong> <small>${dept.createTime ? new Date(dept.createTime).toLocaleString() : '-'}</small></p>
                                </div>
                            </div>
                            <div class="mt-3">
                                <p class="mb-1"><strong>部门职责:</strong></p>
                                <p class="text-muted small">${dept.description || '无'}</p>
                            </div>
                        </div>
                    </div>
             </div>
         `;
         const editDetailButton = departmentDetailsContainer.querySelector('.btn-edit-detail');
         editDetailButton?.addEventListener('click', handleEditClick);
     }
     


    // --- Event Handlers --- 
    async function handleAddChildClick(event) { 
        event.stopPropagation(); 
        const parentId = event.currentTarget.dataset.parentId;
        const parentName = event.currentTarget.dataset.parentName;

        addDepartmentForm.reset();
        addDepartmentForm.classList.remove('was-validated');
        const parentSelect = document.getElementById('addParentDepartment');
        const managerSelect = document.getElementById('addDepartmentManager');
        const [parentOptions, users] = await Promise.all([
            fetchParentDeptOptions(),
            fetchUsersForSelect() 
        ]);
        populateSelect(parentSelect.id, parentOptions, 'id', 'deptName');
        populateSelect(managerSelect.id, users, 'id', 'nickname', 'username');
        parentSelect.value = parentId;
        addDepartmentModal.show();
    }

    async function handleEditClick(event) {
         event.stopPropagation();
        const deptId = event.currentTarget.dataset.deptId;
        if (!deptId) return;

        editDepartmentForm.reset();
        editDepartmentForm.classList.remove('was-validated');
        editDepartmentModal.show(); 
        try {
            const [deptResponse, parentOptions, users] = await Promise.all([
                fetch(`/wkg/api/permission/departments/${deptId}`),
                fetchParentDeptOptions(parseInt(deptId)),
                fetchUsersForSelect()
            ]);
            if (!deptResponse.ok) throw new Error(`获取部门详情失败: ${deptResponse.status}`);
             if (!parentOptions) throw new Error('加载上级部门列表失败');
             if (!users) throw new Error('加载负责人列表失败');
            const deptData = await deptResponse.json();
             const parentSelect = document.getElementById('editParentDepartment');
             const managerSelect = document.getElementById('editDepartmentManager');
            document.getElementById('editDepartmentId').value = deptData.id;
            document.getElementById('editDepartmentName').value = deptData.deptName || '';
            document.getElementById('editDepartmentCode').value = deptData.deptCode || '';
            document.getElementById('editDepartmentDescription').value = deptData.description || '';
            document.getElementById('editDepartmentSort').value = deptData.sortOrder !== null ? deptData.sortOrder : '';
            document.getElementById('editDepartmentStatus').value = deptData.status !== null ? deptData.status : '1';
            populateSelect(parentSelect.id, parentOptions, 'id', 'deptName');
            parentSelect.value = deptData.parentId || ''; 
            populateSelect(managerSelect.id, users, 'id', 'nickname', 'username');
            managerSelect.value = deptData.managerId || ''; 
        } catch (error) {
            console.error('Error opening edit department modal:', error);
            showToast(`加载编辑数据出错: ${error.message}`, 'error');
            editDepartmentModal.hide();
        }
    }

    async function handleDeleteClick(event) {
         event.stopPropagation();
        const deptId = event.currentTarget.dataset.deptId;
        const deptName = event.currentTarget.dataset.deptName;

        if (!confirm(`确定要删除部门 "${deptName}" 吗？\n注意：删除将同时移除所有子部门（如果存在），且无法恢复。`)) {
            return;
        }
        event.currentTarget.disabled = true;
        event.currentTarget.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
        try {
            const response = await fetch(`/wkg/api/permission/departments/${deptId}`, {
                method: 'DELETE',
                headers: { /* 'Sa-Token': ... */ }
            });
            if (response.ok) {
                showToast(`部门 "${deptName}" 删除成功!`, 'success');
                fetchDepartmentTree(); 
                 if (selectedDepartmentId === parseInt(deptId)) { 
                     departmentDetailsContainer.innerHTML = '<div class="text-center text-muted p-5">部门已被删除</div>';
                     selectedDepartmentId = null;
                 }
            } else {
                 const errorData = await response.json().catch(() => ({ message: '删除部门失败，请检查是否有关联用户' }));
                 showToast(`错误: ${errorData.message || response.statusText}`, 'error');
                 event.currentTarget.disabled = false;
                 event.currentTarget.innerHTML = '<i class="bi bi-trash"></i>';
            }
        } catch (error) {
             console.error("Error deleting department:", error);
             showToast('发生意外错误', 'error');
             event.currentTarget.disabled = false;
             event.currentTarget.innerHTML = '<i class="bi bi-trash"></i>';
        }
    }

    // --- Function to fetch users for manager dropdown ---
    async function fetchUsersForSelect() {
        try {
           const response = await fetch('/wkg/api/permission/users?size=1000');
           if (!response.ok) throw new Error('获取用户列表失败');
           const pageData = await response.json();
           return pageData.records || [];
       } catch (error) {
           console.error('Error fetching users for select:', error);
           showToast('加载负责人选项出错', 'error');
           return [];
       }
   }

    // --- Function to fetch parent departments (for modal dropdown) ---
    async function fetchParentDeptOptions(excludeId = null) {
         try {
            const response = await fetch('/wkg/api/permission/departments');
            if (!response.ok) throw new Error('获取部门列表失败');
            let departments = await response.json();
            if (excludeId) {
                 departments = departments.filter(d => d.id !== excludeId);
                 // TODO: Implement recursive filtering to exclude children
            }
            return departments;
        } catch (error) {
            console.error('Error fetching parent departments:', error);
            showToast('加载上级部门选项出错', 'error');
            return [];
        }
    }

    // --- Function to populate select options --- 
    function populateSelect(selectElementId, options, valueField = 'id', textField = 'name', descriptionField = null) {
        const selectElement = document.getElementById(selectElementId);
        if (!selectElement) {
            console.error(`Select element with ID '${selectElementId}' not found.`);
            return; // Add safety check
        } 
        const label = document.querySelector(`label[for='${selectElementId}']`);
        const placeholderText = label ? `请选择${label.innerText.replace('*','').trim()}` : '请选择';
        selectElement.innerHTML = `<option value="">${placeholderText}</option>`;
        if (options && options.length > 0) {
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option[valueField];
                // Improved text fallback: Use textField, then descriptionField, then ID
                let text = option[textField] || option[descriptionField] || `ID: ${option[valueField]}`; 
                if (descriptionField && option[descriptionField] && option[textField] && option[textField] !== option[descriptionField]) {
                     text += ` (${option[descriptionField]})`; // Add description if available and different
                }
                optionElement.textContent = text;
                selectElement.appendChild(optionElement);
            });
        }
    }

    // --- Handle Add Modal Show --- 
     addDepartmentModalElement?.addEventListener('show.bs.modal', async (event) => {
         if (!event.relatedTarget || !event.relatedTarget.classList.contains('btn-add-child')) {
             addDepartmentForm.reset();
             addDepartmentForm.classList.remove('was-validated');
             const parentSelect = document.getElementById('addParentDepartment');
             const managerSelect = document.getElementById('addDepartmentManager');
             if (parentSelect.options.length <= 1) { 
                 const [parentOptions, users] = await Promise.all([
                    fetchParentDeptOptions(),
                    fetchUsersForSelect() 
                 ]);
                 populateSelect(parentSelect.id, parentOptions, 'id', 'deptName');
                 populateSelect(managerSelect.id, users, 'id', 'nickname', 'username');
             }
             parentSelect.value = ''; 
         }
     });

    // --- Add Department Form Submission --- 
    addDepartmentForm?.addEventListener('submit', async (event) => {
        event.preventDefault();
        event.stopPropagation();
        if (!addDepartmentForm.checkValidity()) {
            addDepartmentForm.classList.add('was-validated');
            addDepartmentForm.querySelector(':invalid')?.focus();
            return;
        }
        addDepartmentForm.classList.add('was-validated');
        const formData = new FormData(addDepartmentForm);
        const deptData = Object.fromEntries(formData.entries());
        deptData.status = parseInt(deptData.status || '1');
        deptData.parentId = deptData.parentId ? parseInt(deptData.parentId) : null;
        deptData.managerId = deptData.managerId ? parseInt(deptData.managerId) : null;
        deptData.sortOrder = deptData.sortOrder ? parseInt(deptData.sortOrder) : 0; 
        
        // Select the submit button from the MODAL element, not the form
        const submitButton = addDepartmentModalElement.querySelector('button[type="submit"][form="addDepartmentForm"]'); 
        
        if(submitButton) submitButton.disabled = true;
        if(submitButton) submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
        try {
            const response = await fetch('/wkg/api/permission/departments', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' /*, 'Sa-Token': ... */ },
                body: JSON.stringify(deptData),
            });
            if (response.ok) {
                // 检查响应内容类型和长度
                const contentType = response.headers.get('content-type');
                let newDept = null;
                
                // 只在响应确实包含JSON数据时才尝试解析
                if (contentType && contentType.includes('application/json')) {
                    try {
                        newDept = await response.json();
                    } catch (parseError) {
                        console.warn('无法解析服务器返回的JSON数据:', parseError);
                    }
                }
                
                addDepartmentModal.hide();
                showToast('部门添加成功!', 'success');
                
                // 保存新部门ID，用于稍后选择
                if (newDept && newDept.id) {
                    selectedDepartmentId = newDept.id;
                } else if (deptData.parentId) {
                    // 如果没有返回新部门ID，则选择父部门
                    selectedDepartmentId = deptData.parentId;
                }
                
                await fetchDepartmentTree();
                
                // 如果新部门ID有效，则显示其详情
                if (selectedDepartmentId) {
                    fetchAndRenderDepartmentDetails(selectedDepartmentId);
                }
            } else {
                 const errorData = await response.json().catch(() => ({ message: '添加部门失败，请检查输入' }));
                 showToast(`错误: ${errorData.message || response.statusText}`, 'error');
            }
        } catch(error) {
             console.error("Error adding department:", error);
             showToast('发生意外错误', 'error');
        } finally {
            if(submitButton) submitButton.disabled = false;
            if(submitButton) submitButton.innerHTML = '保存';
        }
    });
    
     // --- Edit Department Form Submission --- 
     editDepartmentForm?.addEventListener('submit', async (event) => {
         event.preventDefault();
         event.stopPropagation();
         if (!editDepartmentForm.checkValidity()) {
              editDepartmentForm.classList.add('was-validated');
              editDepartmentForm.querySelector(':invalid')?.focus();
              return;
         }
         editDepartmentForm.classList.add('was-validated');
         const formData = new FormData(editDepartmentForm);
         const deptData = Object.fromEntries(formData.entries());
         deptData.id = parseInt(deptData.id);
         deptData.status = parseInt(deptData.status || '1');
         deptData.parentId = deptData.parentId ? parseInt(deptData.parentId) : null;
         deptData.managerId = deptData.managerId ? parseInt(deptData.managerId) : null;
         deptData.sortOrder = deptData.sortOrder ? parseInt(deptData.sortOrder) : 0; 
         
         // Select the submit button from the MODAL element, not the form
         const submitButton = editDepartmentModalElement.querySelector('button[type="submit"][form="editDepartmentForm"]'); 
         
         if(submitButton) submitButton.disabled = true;
         if(submitButton) submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
         try {
             const response = await fetch(`/wkg/api/permission/departments/${deptData.id}`, {
                 method: 'PUT',
                 headers: { 'Content-Type': 'application/json' /*, 'Sa-Token': ... */ },
                 body: JSON.stringify(deptData),
             });
             if (response.ok) {
                 editDepartmentModal.hide();
                 showToast('部门信息更新成功!', 'success');
                 selectedDepartmentId = deptData.id;
                 await fetchDepartmentTree(); 
                 fetchAndRenderDepartmentDetails(selectedDepartmentId);
             } else {
                  const errorData = await response.json().catch(() => ({ message: '更新部门失败，请检查输入' }));
                  showToast(`错误: ${errorData.message || response.statusText}`, 'error');
             }
         } catch (error) {
              console.error("Error updating department:", error);
              showToast('发生意外错误', 'error');
         } finally {
             if(submitButton) submitButton.disabled = false;
             if(submitButton) submitButton.innerHTML = '保存更改';
         }
     });

    // --- Toast Notification Helper - 使用全局组件 ---
    function showToast(message, type = 'info') {
        // 使用全局Toast组件显示消息
        window.parent?.postMessage({
            type: 'SHOW_TOAST',
            payload: { message: message, type: type }
        }, '*');
    }

    // --- Initial Load --- 
    fetchDepartmentTree();

});