/**
 * 模型中心
 * 用于展示和管理本地推理模型和云端API模型
 */

/**
 * 获取兼容性颜色相关的类名（用于文本和背景）
 * @param {number} score 兼容性分数
 * @returns {string} 简化颜色类名 (e.g., 'perfect', 'good')
 */
function getCompatibilityClassSimple(score) {
    if (score >= 98) return 'perfect';    // 100-98
    else if (score >= 90) return 'excellent';  // 97-90
    else if (score >= 80) return 'great';      // 89-80
    else if (score >= 70) return 'good';       // 79-70
    else if (score >= 60) return 'decent';     // 69-60
    else if (score >= 50) return 'moderate';   // 59-50
    else if (score >= 40) return 'fair';       // 49-40
    else if (score >= 30) return 'limited';    // 39-30
    else if (score >= 15) return 'poor';       // 29-15
    else if (score >= 5) return 'terrible';   // 14-5
    else return 'impossible';  // 4-0
}


// getRotationDegree and getCompatibilityRating are no longer needed for the card display

document.addEventListener('DOMContentLoaded', function() {
    // 标签页相关元素
    const localModelsTab = document.getElementById('local-models-tab');
    const remoteModelsTab = document.getElementById('remote-models-tab');

    // 本地模型相关元素
    const localModelGrid = document.getElementById('local-model-grid');
    const localLoadingContainer = document.getElementById('local-loading-container');
    const noLocalModelsMessage = document.getElementById('no-local-models-message');

    // 远程模型相关元素
    const remoteModelGrid = document.getElementById('remote-model-grid');
    const noRemoteModelsMessage = document.getElementById('no-remote-models-message');

    // 通用元素
    const systemInfoContainer = document.getElementById('system-info');
    const searchInput = document.getElementById('search-model');
    const sizeFilter = document.getElementById('size-filter');
    const showRecommendedCheckbox = document.getElementById('show-recommended');
    const refreshBtn = document.getElementById('refresh-btn');
    const refreshRemoteBtn = document.getElementById('refresh-remote-btn');
    const remoteModelConfigModal = new bootstrap.Modal(document.getElementById('remoteModelConfigModal'));

    const modelDetailsModalEl = document.getElementById('model-details-modal');
    const modelDetailsModal = new bootstrap.Modal(modelDetailsModalEl);
    const modalModelName = document.getElementById('modal-model-name');
    const modalModelSize = document.getElementById('modal-model-size');
    const modalModelType = document.getElementById('modal-model-type');
    const modalModelCompatibility = document.getElementById('modal-model-compatibility');
    const modalMinRam = document.getElementById('modal-min-ram');
    const modalGpuReq = document.getElementById('modal-gpu-req');
    const modalDiskSpace = document.getElementById('modal-disk-space');
    const modalModelDescription = document.getElementById('modal-model-description');
    const downloadModelBtn = document.getElementById('download-model-btn');
    const downloadProgressContainer = document.getElementById('download-progress-container');
    const downloadProgressBar = document.getElementById('download-progress-bar');
    const downloadStatus = document.getElementById('download-status');

    let allLocalModels = [];
    let allRemoteModels = [];
    let userRemoteConfigs = [];
    let currentModelDetails = null;
    let systemSpecs = null;
    let currentActiveTab = 'local'; // 'local' 或 'remote'

    initPage();

    function initPage() {
        bindEvents();
        // 先加载系统信息，然后再获取模型列表
        loadSystemInfo().then(() => {
            Promise.all([fetchLocalModels(), fetchRemoteModels()]);
        }).catch(error => {
            console.error('Failed to load system info, fetching models anyway:', error);
            Promise.all([fetchLocalModels(), fetchRemoteModels()]);
        });
    }

    // calculateCompatibilityScore and parseModelSizeToParams remain the same
    function calculateCompatibilityScore(model) {

        if (!systemSpecs) {

            return 50; // Default if no system specs
        }
        if (model.size === undefined || model.size === null) {

            return 50; // Default for models with undefined size
        }

        let gpuAvailable = false;
        let gpuMemory = 0;
        let ramAvailable = 8; // Default RAM

        if (systemSpecs && systemSpecs.gpu) {
            gpuAvailable = systemSpecs.gpu.available === true;
            if (systemSpecs.gpu.memory) {
                gpuMemory = parseFloat(systemSpecs.gpu.memory) || 0;
            }
        }

        if (systemSpecs && systemSpecs.memory && systemSpecs.memory.available) {
            ramAvailable = parseFloat(systemSpecs.memory.available) || 0;
        }



        const modelParams = parseModelSizeToParams(model); // Gets estimated B params

        if (isNaN(modelParams) || modelParams <= 0) {

            return 30; // Default for invalid param sizes
        }

        let score = 10; // Base score, can't go lower than this

        // Helper function for smooth scoring
        // value: current model param size
        // optimalLower, optimalUpper: ideal param range for 100% score
        // lowerBound, upperBound: min/max params considered, score outside this drops cottura 10% or very low
        // lowUtilPenaltyFactor: how much to penalize models smaller than optimalLower
        // highDemandPenaltyFactor: how much to penalize models larger than optimalUpper
        function getSmoothScore(value, optimalLower, optimalUpper, lowerBound, upperBound, lowUtilPenaltyFactor = 0.5, highDemandPenaltyFactor = 2.0) {
            let currentScore;
            if (value >= optimalLower && value <= optimalUpper) {
                currentScore = 100; // Max score for optimal range
            } else if (value < optimalLower) {
                if (value < lowerBound) return 10; // Below absolute lower bound
                // Penalize for being smaller than optimal
                // Score decreases from 100 as it moves away from optimalLower
                const diff = optimalLower - value;
                currentScore = 100 - (diff / (optimalLower - lowerBound + 1e-6)) * (100 - 60) * lowUtilPenaltyFactor; // Penalize down to ~60% for very small
                currentScore = Math.max(50, currentScore); // Small models are still usable
            } else { // value > optimalUpper
                if (value > upperBound) return 10; // Above absolute upper bound
                // Penalize for being larger than optimal
                // Score decreases from 100 as it moves away from optimalUpper
                const diff = value - optimalUpper;
                currentScore = 100 - (diff / (upperBound - optimalUpper + 1e-6)) * (100 - 10) * highDemandPenaltyFactor; // Penalize more sharply
            }
            return Math.max(10, Math.min(100, currentScore));
        }



        if (gpuAvailable && gpuMemory > 1.0) { // GPU Mode (min 1GB VRAM to be considered viable for GPU, aligned with backend)

            // 重新定义推荐度分层：适合80-100%，偏小50-70%，偏大10-50%
            let optimalLower, optimalUpper, smallLower, smallUpper, largeLower, largeUpper;

            if (gpuMemory >= 22) { // Backend: 22GB+ VRAM: 13B to 30B
                optimalLower = 13; optimalUpper = 30;  // 80-100%
                smallLower = 7; smallUpper = 12;       // 50-70%
                largeLower = 31; largeUpper = 70;      // 10-50%
            } else if (gpuMemory >= 14) { // Backend: 14GB-21GB VRAM: 7B to 22B
                optimalLower = 7; optimalUpper = 22;   // 80-100%
                smallLower = 3; smallUpper = 6;        // 50-70%
                largeLower = 23; largeUpper = 45;      // 10-50%
            } else if (gpuMemory >= 9) {  // Backend: 9GB-13GB VRAM: 7B to 9B (e.g., 12GB VRAM)
                optimalLower = 7; optimalUpper = 9;    // 100% 完美适配
                smallLower = 4; smallUpper = 6;        // 90-95% 优秀适配 (小)
                largeLower = 10; largeUpper = 12;      // 90-95% 优秀适配 (大)
            } else if (gpuMemory >= 7) {  // Backend: 7GB-8GB VRAM: 3B to 8B
                optimalLower = 3; optimalUpper = 8;    // 80-100%
                smallLower = 1; smallUpper = 2;        // 50-70%
                largeLower = 9; largeUpper = 20;       // 10-50%
            } else if (gpuMemory >= 5) {  // Backend: 5GB-6GB VRAM: 1.5B to 4B
                optimalLower = 1.5; optimalUpper = 4;  // 80-100%
                smallLower = 0.7; smallUpper = 1.4;    // 50-70%
                largeLower = 5; largeUpper = 15;       // 10-50%
            } else { // Backend: 2GB-4GB VRAM: 0.7B to 1.5B
                optimalLower = 0.7; optimalUpper = 1.5; // 80-100%
                smallLower = 0.5; smallUpper = 0.6;     // 50-70%
                largeLower = 2; largeUpper = 8;         // 10-50%
            }
            
            // 针对12GB显存优化的推荐度计算
            if (gpuMemory >= 9 && gpuMemory <= 13) {
                // 12GB显存专用算法
                if (modelParams >= 7 && modelParams <= 9) {
                    // 完美适配区间：7B-9B → 100%
                    score = 100;
                } else if ((modelParams >= 4 && modelParams < 7) || (modelParams > 9 && modelParams <= 12)) {
                    // 优秀适配区间：4B-6B, 10B-12B → 90-95%
                    if (modelParams >= 4 && modelParams < 7) {
                        score = Math.round(90 + (modelParams - 4) * 5 / 3); // 90% 到 95%
                    } else {
                        score = Math.round(95 - (modelParams - 9) * 5 / 3); // 95% 到 90%
                    }
                } else if (modelParams >= 13 && modelParams <= 15) {
                    // 良好适配区间：13B-15B → 75-85%
                    score = Math.round(85 - (modelParams - 13) * 10 / 2); // 85% 到 75%
                } else if (modelParams >= 16 && modelParams <= 22) {
                    // 勉强适配区间：16B-22B → 50-70%
                    score = Math.round(70 - (modelParams - 16) * 20 / 6); // 70% 到 50%
                } else if (modelParams >= 1 && modelParams < 4) {
                    // 小模型区间：1B-3B → 60-80%
                    score = Math.round(60 + (modelParams - 1) * 20 / 3); // 60% 到 80%
                } else if (modelParams < 1) {
                    // 超小模型：<1B → 40-60%
                    score = Math.round(40 + modelParams * 20); // 40% 到 60%
                } else {
                    // 不适配区间：23B+ → 10-40%
                    const excess = modelParams - 22;
                    score = Math.round(Math.max(10, 40 - excess * 2)); // 40% 递减到 10%
                }
            } else {
                // 其他显存配置使用原算法
                const optimalCenter = (optimalLower + optimalUpper) / 2;
                const optimalRange = optimalUpper - optimalLower;
                
                if (modelParams >= optimalLower && modelParams <= optimalUpper) {
                    const distanceFromCenter = Math.abs(modelParams - optimalCenter);
                    const normalizedDistance = distanceFromCenter / (optimalRange / 2);
                    score = Math.round(100 - normalizedDistance * 15);
                } else if (modelParams < optimalLower) {
                    const distance = optimalLower - modelParams;
                    if (distance <= optimalRange) {
                        const normalizedDistance = distance / optimalRange;
                        score = Math.round(85 - normalizedDistance * 25);
                    } else if (distance <= optimalRange * 2) {
                        const normalizedDistance = (distance - optimalRange) / optimalRange;
                        score = Math.round(60 - normalizedDistance * 30);
                    } else {
                        const normalizedDistance = Math.min((distance - optimalRange * 2) / (optimalRange * 2), 1);
                        score = Math.round(30 - normalizedDistance * 20);
                    }
                } else {
                    const distance = modelParams - optimalUpper;
                    const memoryFactor = Math.max(0.5, gpuMemory / 24);
                    
                    if (distance <= optimalRange * memoryFactor) {
                        const normalizedDistance = distance / (optimalRange * memoryFactor);
                        score = Math.round(85 - normalizedDistance * 35);
                    } else if (distance <= optimalRange * memoryFactor * 2) {
                        const normalizedDistance = (distance - optimalRange * memoryFactor) / (optimalRange * memoryFactor);
                        score = Math.round(50 - normalizedDistance * 25);
                    } else {
                        const normalizedDistance = Math.min((distance - optimalRange * memoryFactor * 2) / (optimalRange * memoryFactor * 2), 1);
                        score = Math.round(25 - normalizedDistance * 15);
                    }
                }
            }
            
            score = Math.max(10, Math.min(100, score)); // 确保在10-100范围内

        } else { // CPU Mode or very low VRAM (<2GB)

            // CPU模式也采用分层计算：适合80-100%，偏小50-70%，偏大10-50%
            let optimalLower, optimalUpper, smallLower, smallUpper, largeLower, largeUpper;
            
            if (ramAvailable >= 48) { // Backend: 7B to 13B (CPU)
                optimalLower = 7; optimalUpper = 13;   // 80-100%
                smallLower = 3; smallUpper = 6;        // 50-70%
                largeLower = 14; largeUpper = 30;      // 10-50%
            } else if (ramAvailable >= 28) { // Backend: 3B to 7B (CPU)
                optimalLower = 3; optimalUpper = 7;    // 80-100%
                smallLower = 1; smallUpper = 2;        // 50-70%
                largeLower = 8; largeUpper = 15;       // 10-50%
            } else if (ramAvailable >= 12) { // Backend: 1B to 3B (CPU)
                optimalLower = 1; optimalUpper = 3;    // 80-100%
                smallLower = 0.5; smallUpper = 0.9;    // 50-70%
                largeLower = 4; largeUpper = 7;        // 10-50%
            } else { // Backend: "运行当前AI模型可能较为吃力"
                optimalLower = 0.5; optimalUpper = 1;  // 80-100%
                smallLower = 0.3; smallUpper = 0.4;    // 50-70%
                largeLower = 1.5; largeUpper = 3;      // 10-50%
            }
            

            
            // CPU模式：基于距离的平滑渐变推荐度计算
            const optimalCenter = (optimalLower + optimalUpper) / 2;
            const optimalRange = optimalUpper - optimalLower;
            
            if (modelParams >= optimalLower && modelParams <= optimalUpper) {
                // 最优范围内：85-100%的平滑渐变
                const distanceFromCenter = Math.abs(modelParams - optimalCenter);
                const normalizedDistance = distanceFromCenter / (optimalRange / 2);
                score = Math.round(100 - normalizedDistance * 15); // 100% 到 85%
            } else if (modelParams < optimalLower) {
                // 小于最优范围：平滑衰减
                const distance = optimalLower - modelParams;
                if (distance <= optimalRange) {
                    // 接近最优范围：85% 到 65%
                    const normalizedDistance = distance / optimalRange;
                    score = Math.round(85 - normalizedDistance * 20);
                } else if (distance <= optimalRange * 2) {
                    // 中等距离：65% 到 35%
                    const normalizedDistance = (distance - optimalRange) / optimalRange;
                    score = Math.round(65 - normalizedDistance * 30);
                } else {
                    // 远距离：35% 到 15%
                    const normalizedDistance = Math.min((distance - optimalRange * 2) / (optimalRange * 2), 1);
                    score = Math.round(35 - normalizedDistance * 20);
                }
            } else {
                // 大于最优范围：根据内存限制平滑衰减
                const distance = modelParams - optimalUpper;
                const memoryFactor = Math.max(0.3, ramAvailable / 64); // 内存越大，对大模型越宽容
                
                if (distance <= optimalRange * memoryFactor) {
                    // 接近最优范围：85% 到 45%
                    const normalizedDistance = distance / (optimalRange * memoryFactor);
                    score = Math.round(85 - normalizedDistance * 40);
                } else if (distance <= optimalRange * memoryFactor * 2) {
                    // 中等距离：45% 到 20%
                    const normalizedDistance = (distance - optimalRange * memoryFactor) / (optimalRange * memoryFactor);
                    score = Math.round(45 - normalizedDistance * 25);
                } else {
                    // 远距离：20% 到 10%
                    const normalizedDistance = Math.min((distance - optimalRange * memoryFactor * 2) / (optimalRange * memoryFactor * 2), 1);
                    score = Math.round(20 - normalizedDistance * 10);
                }
            }
            
            score = Math.max(10, Math.min(100, score)); // 确保在10-100范围内
        }
        const finalScore = Math.max(10, Math.min(100, Math.round(score)));

        return finalScore; // Ensure score is 10-100
    }

    function parseModelSizeToParams(model) {
        const modelName = model.name ? model.name.toLowerCase() : '';
        const modelId = model.id ? model.id.toLowerCase() : '';
        const modelSize = parseFloat(model.size || 0);

        const paramRegex = /(\d+(\.\d+)?)b\b/i; // Matches 7b, 13b, 3.5b etc.
        const nameMatch = modelName.match(paramRegex);
        if (nameMatch && nameMatch[1]) return parseFloat(nameMatch[1]);

        const idMatch = modelId.match(paramRegex);
        if (idMatch && idMatch[1]) return parseFloat(idMatch[1]);

        // Guesstimation based on common GGUF sizes (approx. 0.6-0.8 GB per B params for Q4_K_M)
        if (modelSize <= 0.8) return 0.7; // ~700M params
        if (modelSize <= 1.8) return 1.5; // ~1.5B
        if (modelSize <= 2.8) return 3;   // ~3B
        if (modelSize <= 5.0) return 7;   // ~7B
        if (modelSize <= 8.0) return 10;  // ~10B
        if (modelSize <= 10.0) return 13; // ~13B
        if (modelSize <= 15.0) return 20; // ~20B
        if (modelSize <= 25.0) return 30; // ~30B
        if (modelSize <= 35.0) return 40; // ~40B
        if (modelSize <= 50.0) return 65; // ~65-70B
        if (modelSize > 50.0) return 70;  // Capped at 70B for guesstimation

        return Math.max(0.5, Math.round(modelSize / 0.7)); // Fallback, assumes ~0.7GB per B
    }

    function bindEvents() {
        // 标签页切换事件
        localModelsTab.addEventListener('click', () => {
            currentActiveTab = 'local';
            filterModels();
        });

        remoteModelsTab.addEventListener('click', () => {
            currentActiveTab = 'remote';
            filterModels();
        });

        // 刷新按钮事件
        refreshBtn.addEventListener('click', () => {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
            Promise.all([fetchLocalModels(), fetchRemoteModels()]).finally(() => {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = '<i class="bi bi-arrow-repeat"></i>';
            });
        });

        // 搜索和过滤事件
        searchInput.addEventListener('input', filterModels);
        if (sizeFilter) sizeFilter.addEventListener('change', filterModels);
        if (showRecommendedCheckbox) showRecommendedCheckbox.addEventListener('change', filterModels);

        // 远程模型相关事件
        refreshRemoteBtn.addEventListener('click', refreshRemoteModels);
        if (downloadModelBtn) downloadModelBtn.addEventListener('click', startModelDownload);

        // 模态框键盘事件
        if (modelDetailsModalEl) {
            modelDetailsModalEl.addEventListener('keydown', (event) => {
                if (event.key === 'Escape') {
                    modelDetailsModal.hide();
                }
            });
        }
    }

    function loadSystemInfo() {
    
        return fetch('/wkg/api/models/system-info')
            .then(response => {
        
                return response.ok ? response.json() : Promise.reject(response.status);
            })
            .then(data => {
        
                if (data.code === 200 && data.data) {
                    systemSpecs = data.data;
            
                    updateSystemInfoUI(systemSpecs);
                    return systemSpecs; // 返回系统信息
                } else {
                    console.error('Invalid response format:', data);
                    throw new Error(data.message || data.msg || '获取系统信息格式不正确');
                }
            })
            .catch(error => {
                console.error('获取系统信息失败:', error);
                systemInfoContainer.innerHTML = `<div class="alert alert-warning p-2 text-center small">无法加载系统信息。</div>`;
                throw error; // 重新抛出错误，让调用者知道失败了
            });
    }

    function updateSystemInfoUI(specs) {
        const gpuMemory = (specs.gpu && specs.gpu.memory !== undefined) ? `${parseFloat(specs.gpu.memory).toFixed(1)}GB` : 'N/A';
        const cpuModel = specs.cpu.model ? specs.cpu.model.replace(/\(R\)/g, '®').replace(/\(TM\)/g, '™').split('@')[0].trim() : '未知CPU'; // Shorter CPU name

        systemInfoContainer.innerHTML = `
            <div class="system-info-item">
                <span class="system-info-key"><i class="bi bi-cpu"></i>处理器:</span>
                <span class="system-info-value">${cpuModel}</span>
            </div>
            <div class="system-info-item">
                <span class="system-info-key"><i class="bi bi-hdd-stack"></i>核心/线程:</span>
                <span class="system-info-value">${specs.cpu.cores || '-'}核 / ${specs.cpu.threads || '-'}线程</span>
            </div>
            <div class="system-info-item">
                <span class="system-info-key"><i class="bi bi-memory"></i>内存:</span>
                <span class="system-info-value">${specs.memory.total || '-'}GB (可用: ${specs.memory.available || '-'}GB)</span>
            </div>
            <div class="system-info-item">
                <span class="system-info-key"><i class="bi bi-gpu-card"></i>显卡:</span>
                <span class="system-info-value">${specs.gpu.model || '未检测到'}</span>
            </div>
            <div class="system-info-item">
                <span class="system-info-key"><i class="bi bi-sd-card"></i>显存:</span>
                <span class="system-info-value">${gpuMemory}</span>
            </div>
            <div class="system-info-item">
                <span class="system-info-key"><i class="bi bi-device-hdd"></i>可用存储:</span>
                <span class="system-info-value">${specs.storage.available || '-'}GB</span>
            </div>
            <div class="alert alert-info mt-3 small">
                <i class="bi bi-lightbulb me-1"></i>
                根据您的系统配置，我们推荐使用${specs.recommendedModelSize || '小型模型'}
            </div>
        `;
    }

    async function fetchLocalModels() {
        localLoadingContainer.classList.remove('d-none');
        noLocalModelsMessage.classList.add('d-none');
        localModelGrid.innerHTML = '';

        try {
            const [availableRes, installedRes] = await Promise.all([
                fetch('/wkg/api/models/available'),
                fetch('/wkg/api/models/installed')
            ]);

            if (!availableRes.ok) throw new Error(`获取可用模型失败: ${availableRes.status}`);
            const availableData = await availableRes.json();
            if (availableData.code !== 200 || !Array.isArray(availableData.data)) {
                throw new Error('可用模型数据格式错误');
            }
            allLocalModels = availableData.data;

            let installedIds = [];
            if (installedRes.ok) {
                const installedData = await installedRes.json();
                if (installedData.code === 200 && Array.isArray(installedData.data)) {
                    installedIds = installedData.data.map(model => model.id);
                }
            }

            allLocalModels.forEach(model => {
                model.isLocal = installedIds.includes(model.id);
                model.isRemote = false;
                model.requirements = model.requirements || { minRam: 'N/A', minVRam: 'N/A', diskSpace: 'N/A' };
                model.description = model.description || '暂无详细描述。'; // Default description
            });

            renderLocalModels(allLocalModels);

            // 模型列表更新后，检查LLM状态
            checkLlmStatusAndShowGuide();

        } catch (error) {
            console.error('获取本地推理模型列表失败:', error);
            noLocalModelsMessage.innerHTML = `<i class="bi bi-exclamation-triangle-fill me-1"></i>无法加载本地推理模型列表: ${error.message}`;
            noLocalModelsMessage.classList.remove('d-none');
        } finally {
            localLoadingContainer.classList.add('d-none');
        }
    }

    function renderLocalModels(models) {
        localModelGrid.innerHTML = ''; // Clear previous models
        if (models.length === 0) {
            noLocalModelsMessage.classList.remove('d-none');
            return;
        }
        noLocalModelsMessage.classList.add('d-none');

        models.forEach(model => {
            // 为本地模型计算兼容性评分
            if (model.size !== undefined) {
                model._compatibilityScore = calculateCompatibilityScore(model);
            } else {
                model._compatibilityScore = 50; // 默认评分
            }
        });

        const sortedModels = [...models].sort((a, b) => {
            // 本地模型排序逻辑：必需模型优先，已安装模型优先，然后按兼容性评分和大小排序
            const aRequired = a.isRequired || false;
            const bRequired = b.isRequired || false;

            // 必需模型优先
            if (aRequired && !bRequired) return -1;
            if (!aRequired && bRequired) return 1;

            // 已安装模型优先
            if (a.isLocal && !b.isLocal) return -1;
            if (!a.isLocal && b.isLocal) return 1;

            // 按兼容性评分和大小排序
            const scoreDiff = b._compatibilityScore - a._compatibilityScore;
            if (scoreDiff !== 0) return scoreDiff;
            return (parseFloat(b.size) || 0) - (parseFloat(a.size) || 0);
        });

        sortedModels.forEach(model => {
            const cardEl = document.createElement('div');

            const compatibilityScore = model._compatibilityScore;
            const compatClass = getCompatibilityClassSimple(compatibilityScore);

            // 本地模型显示
            const modelSize = model.size ? `${parseFloat(model.size).toFixed(1)}GB` : '未知大小';
            const sizeIcon = 'bi-hdd-stack';

            let typeIcon = 'bi-box-seam';
            let typeText = model.type || '未知';
            if (model.type === 'llm') { typeIcon = 'bi-chat-left-text'; typeText = '语言模型'; }
            else if (model.type === 'embedding') { typeIcon = 'bi-arrows-fullscreen'; typeText = '嵌入模型'; }

            // 必需模型标识
            const isRequired = model.isRequired || false;
            const requiredBadge = isRequired ? '<span class="badge bg-danger ms-2">必需</span>' : '';
            const requiredClass = isRequired ? 'required-model' : '';

            // 本地模型状态标识
            const statusBadge = model.isLocal ?
                '<span class="badge bg-success ms-2"><i class="bi bi-check-circle me-1"></i>已安装</span>' :
                '<span class="badge bg-secondary ms-2"><i class="bi bi-download me-1"></i>可下载</span>';

            cardEl.innerHTML = `
                <div class="model-card ${model.isLocal ? 'is-installed-card' : ''} ${requiredClass}">
                    <div class="model-card-header">
                        <div class="model-name-container">
                            <h3 class="model-name">${model.name}${requiredBadge}${statusBadge}</h3>
                        </div>
                        <span class="model-id">${model.id}</span>
                    </div>
                    <div class="model-card-body">
                        <div class="model-meta-info">
                            <span><i class="bi ${sizeIcon} me-1"></i>${modelSize}</span>
                            <span><i class="bi ${typeIcon} me-1"></i>${typeText}</span>
                        </div>
                        <div class="model-compatibility-section">
                            <div class="compatibility-header">
                                ${isRequired ?
                                    `<div class="compatibility-score text-danger">必需</div>
                                     <span class="compatibility-label">系统要求</span>` :
                                    `<div class="compatibility-score text-compat-${compatClass}">${compatibilityScore}%</div>
                                     <span class="compatibility-label">推荐度</span>`
                                }
                            </div>
                            <div class="compatibility-bar-container">
                                ${isRequired ?
                                    `<div class="compatibility-bar bg-danger" style="width: 100%;"></div>` :
                                    `<div class="compatibility-bar bg-compat-${compatClass}" style="width: ${compatibilityScore}%;"></div>`
                                }
                            </div>
                        </div>
                        <p class="model-description">${model.description}</p>
                    </div>
                    <div class="model-card-footer">
                        ${model.isLocal ?
                            `<button class="btn btn-sm btn-outline-danger delete-btn w-100"><i class="bi bi-trash3 me-1"></i>卸载模型</button>` :
                            `<button class="btn btn-primary download-btn w-100"><i class="bi bi-download me-1"></i>下载模型</button>`
                        }
                    </div>
                </div>
            `;

            // 事件监听器
            const downloadBtn = cardEl.querySelector('.download-btn');
            if (downloadBtn) {
                downloadBtn.addEventListener('click', () => showModelDetails(model));
            }

            const deleteBtn = cardEl.querySelector('.delete-btn');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    confirmDeleteModel(model);
                });
            }

            // 卡片点击事件
            if (!model.isLocal) {
                cardEl.querySelector('.model-card').addEventListener('click', (event) => {
                    if (!event.target.closest('button')) {
                        showModelDetails(model);
                    }
                });
            }

            localModelGrid.appendChild(cardEl);
        });
    }

    async function fetchRemoteModels() {
        noRemoteModelsMessage.classList.add('d-none');
        remoteModelGrid.innerHTML = '';

        try {
            const [availableRes, configsRes] = await Promise.all([
                fetch('/wkg/api/models/remote/available'),
                fetch('/wkg/api/models/remote/configs')
            ]);

            if (!availableRes.ok) throw new Error(`获取云端API模型失败: ${availableRes.status}`);
            const availableData = await availableRes.json();
            if (availableData.code !== 200 || !Array.isArray(availableData.data)) {
                throw new Error('云端API模型数据格式错误');
            }
            allRemoteModels = availableData.data;

            let userConfigs = [];
            if (configsRes.ok) {
                const configsData = await configsRes.json();
                if (configsData.code === 200 && Array.isArray(configsData.data)) {
                    userConfigs = configsData.data;
                }
            }

            // 合并配置信息
            allRemoteModels.forEach(model => {
                model.isRemote = true;
                model.isLocal = false;
                const config = userConfigs.find(c => c.modelId === model.id);
                model.isConfigured = !!config;
                model.isEnabled = config ? config.enabled : false;
                model.configId = config ? config.id : null;
            });

            renderRemoteModels(allRemoteModels);

        } catch (error) {
            console.error('获取云端API模型列表失败:', error);
            noRemoteModelsMessage.innerHTML = `<i class="bi bi-exclamation-triangle-fill me-1"></i>无法加载云端API模型列表: ${error.message}`;
            noRemoteModelsMessage.classList.remove('d-none');
        }
    }

    function renderRemoteModels(models) {
        remoteModelGrid.innerHTML = ''; // Clear previous models
        if (models.length === 0) {
            noRemoteModelsMessage.classList.remove('d-none');
            return;
        }
        noRemoteModelsMessage.classList.add('d-none');

        // 远程模型排序：已配置且启用的优先，然后是已配置但未启用的，最后是未配置的
        const sortedModels = [...models].sort((a, b) => {
            if (a.isConfigured && a.isEnabled && !(b.isConfigured && b.isEnabled)) return -1;
            if (!(a.isConfigured && a.isEnabled) && b.isConfigured && b.isEnabled) return 1;
            if (a.isConfigured && !b.isConfigured) return -1;
            if (!a.isConfigured && b.isConfigured) return 1;
            return a.name.localeCompare(b.name);
        });

        sortedModels.forEach(model => {
            const cardEl = document.createElement('div');

            let typeIcon = 'bi-cloud';
            let typeText = model.type || '语言模型';
            if (model.type === 'llm') { typeIcon = 'bi-chat-left-text'; typeText = '语言模型'; }
            else if (model.type === 'embedding') { typeIcon = 'bi-arrows-fullscreen'; typeText = '嵌入模型'; }

            // 提供商标识
            const providerBadge = `<span class="badge bg-info ms-2">${model.provider}</span>`;

            // 配置状态标识
            let statusBadge = '';
            if (model.isConfigured && model.isEnabled) {
                statusBadge = '<span class="badge bg-success ms-2"><i class="bi bi-check-circle me-1"></i>已启用</span>';
            } else if (model.isConfigured) {
                statusBadge = '<span class="badge bg-warning ms-2"><i class="bi bi-pause-circle me-1"></i>已配置</span>';
            } else {
                statusBadge = '<span class="badge bg-secondary ms-2"><i class="bi bi-gear me-1"></i>待配置</span>';
            }

            const pricing = model.pricing || '按使用计费';

            cardEl.innerHTML = `
                <div class="model-card remote-model ${model.isConfigured ? 'is-configured-card' : ''}">
                    <div class="model-card-header">
                        <div class="model-name-container">
                            <h3 class="model-name">${model.name}${providerBadge}${statusBadge}</h3>
                        </div>
                    </div>
                    <div class="model-card-body">
                        <div class="model-meta-info">
                            <span><i class="bi ${typeIcon} me-1"></i>${typeText}</span>
                        </div>
                        <p class="model-description">${model.description || '强大的云端API模型，提供高质量的AI服务'}</p>
                    </div>
                    <div class="model-card-footer">
                        ${model.isConfigured ?
                            `<button class="btn btn-outline-primary config-btn w-100"><i class="bi bi-gear me-1"></i>管理配置</button>` :
                            `<button class="btn btn-primary config-btn w-100"><i class="bi bi-cloud-plus me-1"></i>配置模型</button>`
                        }
                    </div>
                </div>
            `;

            // 事件监听器
            const configBtn = cardEl.querySelector('.config-btn');
            if (configBtn) {
                configBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    showRemoteModelConfig(model);
                });
            }

            // 卡片点击事件
            cardEl.querySelector('.model-card').addEventListener('click', (event) => {
                if (!event.target.closest('button')) {
                    showRemoteModelConfig(model);
                }
            });

            remoteModelGrid.appendChild(cardEl);
        });
    }

    // 数量更新函数已移除，不再显示标签页数量

    function confirmDeleteModel(model) {
        const modalId = 'delete-confirm-modal-' + Date.now();
        const confirmDialog = document.createElement('div');
        // Added 'modal-delete-confirm' for specific styling
        confirmDialog.className = 'modal fade modal-delete-confirm';
        confirmDialog.id = modalId;
        confirmDialog.setAttribute('tabindex', '-1');
        confirmDialog.setAttribute('aria-hidden', 'true');

        confirmDialog.innerHTML = `
            <div class="modal-dialog modal-dialog-centered modal-sm"> <!-- modal-sm for smaller confirm dialog -->
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">确认操作</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                    </div>
                    <div class="modal-body">
                        <p>确定要卸载模型 <strong>${model.name}</strong> 吗？</p>
                        <p class="text-danger"><i class="bi bi-exclamation-triangle-fill me-1"></i>此操作不可恢复。</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-sm btn-danger" id="confirm-delete-btn-${modalId}">确认卸载</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(confirmDialog);
        const modal = new bootstrap.Modal(confirmDialog);

        const deleteBtnElement = document.getElementById(`confirm-delete-btn-${modalId}`);

        function handleDeleteClick() {
            deleteBtnElement.disabled = true;
            deleteBtnElement.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
            deleteBtnElement.removeEventListener('click', handleDeleteClick);
            deleteModel(model.id, modal, confirmDialog, deleteBtnElement);
        }

        deleteBtnElement.addEventListener('click', handleDeleteClick);

        confirmDialog.addEventListener('shown.bs.modal', () => {
            deleteBtnElement.focus();
        });

        confirmDialog.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(confirmDialog);
        });

        modal.show();
    }

    function deleteModel(modelId, modalInstance, dialogEl, deleteBtnElement) {
        fetch('/wkg/api/models/delete', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ modelName: modelId })
        })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    modalInstance.hide();
                    fetchLocalModels();
                } else {
                    throw new Error(data.msg || '卸载失败');
                }
            })
            .catch(error => {
                console.error('卸载模型时发生错误:', error);
                const modalBody = dialogEl.querySelector('.modal-body');
                let errorDiv = modalBody.querySelector('.alert-danger.mt-2'); // Specific selector
                if (!errorDiv) {
                    errorDiv = document.createElement('div');
                    errorDiv.className = 'alert alert-danger mt-2'; // Added mt-2 for spacing
                    modalBody.appendChild(errorDiv); // Append error message
                }
                errorDiv.innerHTML = `<i class="bi bi-exclamation-triangle-fill me-1"></i>卸载失败: ${error.message}`;

                deleteBtnElement.disabled = false;
                deleteBtnElement.textContent = '重试卸载';
                // Re-attach listener for retry ONLY if the modal is still open (user hasn't dismissed it)
                if (bootstrap.Modal.getInstance(dialogEl)) {
                    deleteBtnElement.addEventListener('click', function handleRetry() {
                        deleteBtnElement.removeEventListener('click', handleRetry); // remove self
                        handleDeleteClick(); // Call the original handler logic
                    });
                }
            });
    }

    function showToast(title, message, type = 'info') {
        // 使用全局Toast组件显示消息
        const fullMessage = title ? `${title}: ${message}` : message;
        window.parent?.postMessage({
            type: 'SHOW_TOAST',
            payload: { message: fullMessage, type: type }
        }, '*');
    }

    function filterModels() {
        const searchText = searchInput.value.toLowerCase().trim();
        const sizeFilterValue = sizeFilter ? sizeFilter.value : 'all';
        const showRecommended = showRecommendedCheckbox ? showRecommendedCheckbox.checked : false;

        // 根据当前活动标签页选择要筛选的模型列表
        let modelsToFilter = [];
        if (currentActiveTab === 'local') {
            modelsToFilter = allLocalModels;
        } else if (currentActiveTab === 'remote') {
            modelsToFilter = allRemoteModels;
        }

        const filtered = modelsToFilter.filter(model => {
            // 搜索文本匹配
            const nameMatch = model.name.toLowerCase().includes(searchText) || model.id.toLowerCase().includes(searchText);
            if (searchText && !nameMatch) return false;

            // 本地模型特有的过滤条件
            if (currentActiveTab === 'local') {
                // 大小筛选
                if (sizeFilterValue !== 'all' && model.size !== undefined) {
                    const modelSizeGB = parseFloat(model.size);
                    if (!isNaN(modelSizeGB)) {
                        if (sizeFilterValue === 'small' && modelSizeGB >= 5) return false;
                        if (sizeFilterValue === 'medium' && (modelSizeGB < 5 || modelSizeGB > 10)) return false;
                        if (sizeFilterValue === 'large' && modelSizeGB <= 10) return false;
                    }
                }

                // 推荐筛选
                if (showRecommended) {
                    const score = model._compatibilityScore !== undefined ? model._compatibilityScore : calculateCompatibilityScore(model);
                    if (score < 70) return false;
                }
            }

            return true;
        });

        // 根据当前标签页渲染对应的模型
        if (currentActiveTab === 'local') {
            renderLocalModels(filtered);
        } else if (currentActiveTab === 'remote') {
            renderRemoteModels(filtered);
        }
    }

    function showModelDetails(model) {
        currentModelDetails = model;
        const compatibilityScore = model._compatibilityScore !== undefined ? model._compatibilityScore : calculateCompatibilityScore(model);
        const compatClass = getCompatibilityClassSimple(compatibilityScore);

        modalModelName.textContent = model.name;
        modalModelSize.textContent = model.size ? `${parseFloat(model.size).toFixed(1)}GB` : 'N/A';

        let typeText = model.type || '未知';
        if (model.type === 'llm') typeText = '语言模型';
        else if (model.type === 'embedding') typeText = '嵌入模型';
        modalModelType.textContent = typeText;

        modalModelCompatibility.innerHTML = `<span class="fw-bold text-compat-${compatClass}">${compatibilityScore}%</span>`;

        modalMinRam.textContent = model.requirements.minRam || 'N/A';
        modalGpuReq.textContent = model.requirements.minVRam || 'N/A';
        modalDiskSpace.textContent = model.requirements.diskSpace || 'N/A';
        modalModelDescription.textContent = model.description || '暂无详细描述。';

        // Reset progress bar and status text every time the modal is shown
        downloadProgressContainer.classList.add('d-none'); // Hide initially
        downloadProgressBar.style.width = '0%';
        downloadProgressBar.setAttribute('aria-valuenow', '0');
        // Reset to default progress bar classes, removing any success/danger/warning states
        downloadProgressBar.className = 'progress-bar';
        downloadStatus.textContent = '准备下载中...'; // Initial status text

        downloadModelBtn.disabled = model.isLocal;
        if (model.isLocal) {
            downloadModelBtn.textContent = '已安装';
        } else {
            downloadModelBtn.textContent = '下载模型';
        }

        modelDetailsModal.show();
    }

    /**
     * 显示Ollama不可用的对话框
     */
    function showOllamaNotAvailableDialog(errorData) {
        // 重置下载按钮状态
        downloadModelBtn.disabled = false;
        downloadModelBtn.textContent = '下载模型';
        downloadProgressContainer.classList.add('d-none');

        // 创建Ollama不可用的提示对话框（优化为紧凑版本）
        const modalHtml = `
            <div class="modal fade" id="ollama-not-available-modal" tabindex="-1" aria-labelledby="ollama-not-available-label" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title" id="ollama-not-available-label">
                                <i class="bi bi-exclamation-triangle me-2"></i>需要Ollama服务
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center mb-3">
                                <i class="bi bi-robot text-warning" style="font-size: 3rem;"></i>
                                <p class="mt-2 mb-0">
                                    <strong>Ollama</strong> 是EdgeMind运行AI模型的核心引擎，需要先安装并启动才能下载模型。
                                </p>
                            </div>

                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="card text-center h-100">
                                        <div class="card-body py-3">
                                            <i class="bi bi-arrow-clockwise text-success fs-4"></i>
                                            <h6 class="card-title mt-2 mb-1">已安装？</h6>
                                            <button type="button" class="btn btn-success btn-sm" onclick="checkOllamaAndRetry()">
                                                检查重试
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="card text-center h-100">
                                        <div class="card-body py-3">
                                            <i class="bi bi-download text-primary fs-4"></i>
                                            <h6 class="card-title mt-2 mb-1">未安装？</h6>
                                            <a href="${errorData.ollamaDownloadUrl || 'https://ollama.com/download'}"
                                               target="_blank" class="btn btn-primary btn-sm">
                                                立即下载
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3 p-2 bg-light rounded">
                                <small class="text-muted">
                                    <i class="bi bi-info-circle me-1"></i>
                                    安装后Ollama会自动启动，然后点击"检查重试"即可继续下载模型
                                </small>
                            </div>
                        </div>
                        <div class="modal-footer py-2">
                            <button type="button" class="btn btn-outline-secondary btn-sm" data-bs-dismiss="modal">稍后处理</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('ollama-not-available-modal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新的模态框到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('ollama-not-available-modal'));
        modal.show();

        // 模态框关闭时清理
        document.getElementById('ollama-not-available-modal').addEventListener('hidden.bs.modal', function () {
            this.remove();
        });
    }

    /**
     * 检查Ollama服务并重试下载
     */
    window.checkOllamaAndRetry = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('ollama-not-available-modal'));

        // 显示检查状态
        showToast('检查中', '正在检查Ollama服务状态...', 'info');

        // 调用API检查Ollama状态
        fetch('/wkg/api/models/ollama/status')
            .then(response => response.json())
            .then(data => {
                if (data.code === 200 && data.data) {
                    if (data.data.available) {
                        // Ollama服务可用，关闭模态框并重试下载
                        if (modal) {
                            modal.hide();
                        }
                        showToast('成功', 'Ollama服务已可用，开始下载模型...', 'success');
                        setTimeout(() => {
                            startModelDownload();
                        }, 1000);
                    } else {
                        // Ollama服务仍不可用
                        showToast('提示', 'Ollama服务仍不可用，请确保已正确安装并启动Ollama', 'warning');
                    }
                } else {
                    showToast('错误', '检查Ollama服务状态失败', 'error');
                }
            })
            .catch(error => {
                console.error('检查Ollama状态失败:', error);
                showToast('错误', '检查Ollama服务状态时发生错误', 'error');
            });
    };

    function startModelDownload() {
        if (!currentModelDetails || currentModelDetails.isLocal) return;

        // Show progress container and set initial downloading states immediately when download starts
        downloadProgressContainer.classList.remove('d-none');
        downloadModelBtn.disabled = true;
        downloadModelBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> 请求中...';
        downloadStatus.textContent = '正在准备下载...'; // Status before SSE connection
        // Ensure progress bar is animated and striped from the start of the request
        downloadProgressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
        downloadProgressBar.style.width = '0%'; // Explicitly set to 0% width at start
        downloadProgressBar.setAttribute('aria-valuenow', '0');

        const requestData = {
            modelName: currentModelDetails.id,
            modelType: currentModelDetails.type || 'llm',
            useGpu: systemSpecs && systemSpecs.gpu && systemSpecs.gpu.available === true
        };

        fetch('/wkg/api/models/download', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestData)
        })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200 && data.data) {
                    // 检查是否是Ollama不可用的情况
                    if (data.data.success === false && data.data.error === 'OLLAMA_NOT_AVAILABLE') {
                        showOllamaNotAvailableDialog(data.data);
                        return;
                    }

                    // 正常下载流程
                    if (data.data.taskId) {
                        monitorDownloadProgress(data.data.taskId);
                    } else {
                        throw new Error('启动下载失败，未获取任务ID');
                    }
                } else {
                    throw new Error(data.msg || '启动下载失败');
                }
            })
            .catch(error => {
                console.error('下载请求失败:', error);
                downloadStatus.textContent = `启动下载失败: ${error.message}`;
                downloadProgressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
                downloadProgressBar.classList.add('bg-danger');
                downloadModelBtn.disabled = false;
                downloadModelBtn.textContent = '重试下载';
            });
    }

    function monitorDownloadProgress(taskId) {
        downloadModelBtn.textContent = '下载中...';
        let eventSource = null;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;
        let isDownloadCompleted = false;
        let progressStallTimer = null;
        const progressStallTimeout = 5 * 60 * 1000;
        const safetyTimeoutDuration = 3 * 60 * 60 * 1000;

        function createEventSource() {
            if (progressStallTimer) clearTimeout(progressStallTimer);
            eventSource = new EventSource(`/wkg/api/models/download/stream/${taskId}`);

            eventSource.onopen = () => {
                downloadStatus.textContent = '已连接，等待下载数据...';
                reconnectAttempts = 0;
            };

            eventSource.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    if (data.progress !== undefined) {
                        const progress = Math.min(100, Math.max(0, parseInt(data.progress, 10)));
                        downloadProgressBar.style.width = `${progress}%`;
                        downloadProgressBar.setAttribute('aria-valuenow', progress);

                        let statusText = data.status || (progress < 100 ? '下载中' : '处理中');
                        if (statusText.toLowerCase().includes('pulling manifest')) statusText = '获取清单';
                        else if (statusText.toLowerCase() === 'downloading') statusText = '下载中';
                        else if (statusText.toLowerCase() === 'verifying') statusText = '校验中';
                        else if (statusText.toLowerCase() === 'success' || statusText.toLowerCase() === 'completed') statusText = '完成';

                        downloadStatus.textContent = `${statusText}: ${progress}%`;

                        if (progressStallTimer) clearTimeout(progressStallTimer);
                        if (progress < 100 && !isDownloadCompleted) {
                            progressStallTimer = setTimeout(createEventSource, progressStallTimeout);
                        }
                    }

                    const statusLower = (data.status || '').toLowerCase();
                    if (statusLower === 'success' || statusLower === 'completed' || statusLower === 'done' || (data.progress && parseInt(data.progress, 10) >= 100 && statusLower !== 'failed')) {
                        isDownloadCompleted = true; // Set flag first
                        closeConnection(); // Close SSE connection as download part is done

                        // UI updates for download completion, before installation confirmation
                        downloadProgressBar.style.width = '100%';
                        downloadProgressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
                        downloadProgressBar.classList.add('bg-success');
                        downloadStatus.textContent = '下载完成，正在安装和准备模型，请稍候...'; // New status
                        downloadModelBtn.disabled = true; // Keep button disabled
                        downloadModelBtn.textContent = '处理中...'; // New button text

                        // Wait for a few seconds to allow backend to install/register the model,
                        // then fetch the updated models list from the backend.
                        const installWaitDelay = 12000; // 12 seconds, increased for better reliability

                        setTimeout(async () => {
                            try {
                                downloadStatus.textContent = '正在更新模型列表...';

                                // Fetch updated models from backend, this will trigger ModelService.init() implicitly
                                await fetchLocalModels();
                                // fetchLocalModels() will call renderLocalModels internally upon success,
                                // and also updates allLocalModels array with fresh data including local status.

                                downloadStatus.textContent = '模型安装完成！';

                            } catch (error) {
                                console.error("获取模型列表失败 (安装后):", error);
                                downloadStatus.textContent = '模型列表更新失败';
                                showToast('列表更新失败', `${currentModelDetails.name} 可能已安装，但无法确认状态。请手动刷新页面。`, 'warning');
                            }

                            // 延迟关闭模态框，让用户看到最终状态
                            setTimeout(() => {
                                if (bootstrap.Modal.getInstance(modelDetailsModalEl)) {
                                    modelDetailsModal.hide();
                                }
                            }, 2000); // 额外等待2秒让用户看到完成状态

                        }, installWaitDelay);

                    } else if (statusLower === 'failed' || statusLower === 'error') {
                        isDownloadCompleted = true; // Set flag first
                        closeConnection();
                        downloadStatus.textContent = `下载失败: ${data.error || data.status || '未知错误'}`;
                        downloadProgressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
                        downloadProgressBar.classList.add('bg-danger');
                        downloadModelBtn.disabled = false;
                        downloadModelBtn.textContent = '重试下载';
                    }
                } catch (error) {
                    console.error('解析SSE事件数据出错:', error, event.data);
                }
            };

            eventSource.onerror = (errorEvent) => {
                console.error('EventSource stream error:', errorEvent); // Log the actual error event

                if (isDownloadCompleted) {
                    closeConnection();
                    return;
                }

                if (eventSource) { // Ensure it exists before closing
                    eventSource.close();
                }

                if (reconnectAttempts < maxReconnectAttempts) {
                    reconnectAttempts++;
                    downloadStatus.textContent = `连接中断，尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})...`;
                    setTimeout(createEventSource, reconnectAttempts * 2000 + 1000);
                } else {
                    // Max retries reached
                    const currentProgress = parseInt(downloadProgressBar.getAttribute('aria-valuenow'), 10);
                    if (currentProgress === 100) {
                        // Optimistic completion if progress was 100%
                        downloadStatus.textContent = '下载已完成，最终状态确认超时。';
                        downloadProgressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
                        downloadProgressBar.classList.add('bg-success');
                        downloadModelBtn.textContent = '已完成';
                        downloadModelBtn.disabled = true;

                        showToast('状态确认超时', `${currentModelDetails.name} 下载可能已完成，正在刷新列表...`, 'info');

                        // Optimistically mark as downloaded for immediate UI feedback
                        const optimisticModel = allLocalModels.find(m => m.id === currentModelDetails.id);
                        if (optimisticModel) {
                            optimisticModel.isLocal = true;
                        }

                        // Attempt to hide modal and render optimistically updated list first
                        if (bootstrap.Modal.getInstance(modelDetailsModalEl)) {
                           modelDetailsModal.hide();
                        }
                        renderLocalModels(allLocalModels); // Render with optimistic update

                        // Then, try to fetch the authoritative list from the backend to confirm
                        fetchLocalModels().catch(err => {
                            console.error("Failed to refresh models from backend after optimistic completion, UI might not be perfectly up-to-date:", err);
                            // UI was already updated optimistically, so just log the error.
                        });

                    } else {
                        // Standard failure if progress was not 100%
                        downloadStatus.textContent = '无法连接服务器。下载可能已中断或在后台进行。';
                        downloadProgressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
                        downloadProgressBar.classList.add('bg-warning');
                        downloadModelBtn.disabled = false;
                        downloadModelBtn.textContent = '检查状态';
                        showToast('连接中断', `与 ${currentModelDetails.name} 的下载监控连接中断。`, 'warning');
                    }
                }
            };
        }

        function closeConnection() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            if (progressStallTimer) clearTimeout(progressStallTimer);
            if (safetyTimeout) clearTimeout(safetyTimeout);
        }

        createEventSource();
        const safetyTimeout = setTimeout(() => {
            if (!isDownloadCompleted && eventSource && eventSource.readyState !== EventSource.CLOSED) {
                closeConnection();
                downloadStatus.textContent = `下载超时。上次进度 ${downloadProgressBar.getAttribute('aria-valuenow')}%.`;
                downloadProgressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
                downloadProgressBar.classList.add('bg-warning');
                downloadModelBtn.disabled = false;
                downloadModelBtn.textContent = '检查状态';
                showToast('下载超时', '长时间未获取到进度，请检查。', 'warning');
            }
        }, safetyTimeoutDuration);
    }

    // BGE-Large模型状态检查已删除，现在使用本地BGE中文嵌入模型

    // BGE-Large模型引导提示和下载函数已删除，现在使用本地BGE中文嵌入模型

    /**
     * 检查语言模型（LLM）状态并显示引导
     */
    function checkLlmStatusAndShowGuide() {
        // 移除现有的LLM引导提示
        const existingLlmGuide = document.querySelector('.llm-guide-alert');
        if (existingLlmGuide) {
            existingLlmGuide.remove();
        }

        // 检查是否有已安装的语言模型
        const installedLlmModels = allLocalModels.filter(model => {
            return model.isLocal && isLanguageModel(model);
        });

        console.log('已安装的语言模型:', installedLlmModels);

        if (installedLlmModels.length === 0) {
            // 没有安装任何语言模型，显示引导提示
            showLlmGuide();
        }
    }

    /**
     * 判断是否为语言模型（LLM）
     */
    function isLanguageModel(model) {
        // 根据模型ID或名称判断是否为语言模型
        const llmKeywords = [
            'llama', 'qwen', 'gemma', 'mistral', 'phi', 'codellama',
            'deepseek', 'yi', 'baichuan', 'chatglm', 'internlm',
            'vicuna', 'alpaca', 'wizardlm', 'orca', 'falcon'
        ];

        const modelIdLower = model.id.toLowerCase();
        const modelNameLower = model.name.toLowerCase();

        // 排除嵌入模型（embedding models）
        const embeddingKeywords = ['bge', 'embedding', 'embed', 'sentence', 'retrieval'];
        const isEmbeddingModel = embeddingKeywords.some(keyword =>
            modelIdLower.includes(keyword) || modelNameLower.includes(keyword)
        );

        if (isEmbeddingModel) {
            return false;
        }

        // 检查是否包含LLM关键词
        return llmKeywords.some(keyword =>
            modelIdLower.includes(keyword) || modelNameLower.includes(keyword)
        );
    }

    /**
     * 显示语言模型引导提示
     */
    function showLlmGuide() {
        const guideHtml = `
            <div class="alert alert-warning llm-guide-alert shadow-sm" style="
                border-left: 4px solid #ffc107;
                background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
                margin-bottom: 1.5rem;
            ">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0 me-3">
                        <i class="bi bi-chat-left-text text-warning" style="font-size: 1.5rem;"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="alert-heading mb-1" style="color: #856404; font-weight: 600;">
                            <i class="bi bi-exclamation-triangle me-1"></i>
                            需要安装语言模型
                        </h6>
                        <p class="mb-0" style="color: #856404; font-size: 0.9rem; line-height: 1.5;">
                            要进行AI对话，您需要安装至少一个大语言模型。请从下方模型列表中选择并安装合适的模型。
                        </p>
                    </div>
                </div>
            </div>
        `;

        // 将引导提示插入到页面顶部
        const container = document.querySelector('.container-fluid');
        if (container) {
            // 在容器开头插入
            container.insertAdjacentHTML('afterbegin', guideHtml);
        }
    }

    // ==================== 远程模型功能 ====================

    /**
     * 获取远程模型列表
     */
    async function fetchRemoteModels() {
        try {
            console.log('获取远程模型列表...');
            const response = await fetch('/wkg/api/models/remote/available');
            const result = await response.json();

            if (result.code === 200 && result.data) {
                allRemoteModels = result.data;
                console.log('获取到远程模型:', allRemoteModels.length, '个');

                // 同时获取远程模型配置
                await fetchRemoteConfigs();

                // 合并配置信息到模型数据中
                allRemoteModels.forEach(model => {
                    const config = userRemoteConfigs.find(config => config.modelId === model.id);
                    model.isConfigured = !!config;
                    model.isEnabled = config ? config.enabled : false;
                    model.configId = config ? config.id : null;
                });

                // 重新渲染远程模型
                if (currentActiveTab === 'remote') {
                    renderRemoteModels(allRemoteModels);
                }
            } else {
                console.error('获取远程模型失败:', result.msg);
            }
        } catch (error) {
            console.error('获取远程模型时发生错误:', error);
        }
    }

    /**
     * 获取远程模型配置
     */
    async function fetchRemoteConfigs() {
        try {
            const response = await fetch('/wkg/api/models/remote/configs');
            const result = await response.json();

            if (result.code === 200 && result.data) {
                userRemoteConfigs = result.data;
                console.log('获取到远程模型配置:', userRemoteConfigs.length, '个');
            }
        } catch (error) {
            console.error('获取远程模型配置时发生错误:', error);
        }
    }

    // mergeAndRenderModels函数已移除，使用标签页分别渲染

    /**
     * 显示远程模型配置弹窗
     */
    function showRemoteModelConfig(model) {
        // 填充模型信息
        document.getElementById('config-model-id').value = model.id;
        document.getElementById('config-model-name').textContent = model.name || '';
        document.getElementById('config-model-description').textContent = model.description || '';

        // 如果已有配置，填充现有数据
        if (model.isConfigured) {
            const userConfig = userRemoteConfigs.find(config =>
                config.modelId === model.id
            );

            if (userConfig) {
                document.getElementById('config-api-key').value = userConfig.apiKey || '';
                document.getElementById('config-remark').value = userConfig.remark || '';
                document.getElementById('config-enabled').checked = userConfig.enabled;

                // 已配置的模型，无需额外操作
            }
        } else {
            // 清空表单
            document.getElementById('config-api-key').value = '';
            document.getElementById('config-remark').value = '';
            document.getElementById('config-enabled').checked = true;
            // 新配置的模型，无需额外操作
        }

        // 存储当前模型信息
        currentModelDetails = model;

        // 显示弹窗
        remoteModelConfigModal.show();
    }

    /**
     * 刷新远程模型集成
     */
    async function refreshRemoteModels() {
        const btn = refreshRemoteBtn;
        const originalText = btn.innerHTML;

        try {
            btn.disabled = true;
            btn.innerHTML = '<span class="spinner-border spinner-border-sm"></span>';

            const response = await fetch('/wkg/api/models/remote/refresh', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const result = await response.json();

            if (result.code === 200 && result.data.success) {
                showToast('成功', `远程模型刷新成功！可用模型: ${result.data.availableModels.length}个`, 'success');
                console.log('可用远程模型:', result.data.availableModels);

                // 重新获取远程模型配置
                await fetchRemoteConfigs();

                // 合并配置信息到模型数据中
                allRemoteModels.forEach(model => {
                    const config = userRemoteConfigs.find(config => config.modelId === model.id);
                    model.isConfigured = !!config;
                    model.isEnabled = config ? config.enabled : false;
                    model.configId = config ? config.id : null;
                });

                // 重新渲染远程模型
                if (currentActiveTab === 'remote') {
                    renderRemoteModels(allRemoteModels);
                }
            } else {
                showToast('失败', result.data.message || '远程模型刷新失败', 'error');
            }
        } catch (error) {
            console.error('刷新远程模型失败:', error);
            showToast('错误', '刷新远程模型失败: ' + error.message, 'error');
        } finally {
            btn.disabled = false;
            btn.innerHTML = originalText;
        }
    }

    // 绑定远程模型配置相关事件

    // API密钥显示/隐藏切换
    document.getElementById('toggle-api-key').addEventListener('click', function() {
        const apiKeyInput = document.getElementById('config-api-key');
        const icon = this.querySelector('i');

        if (apiKeyInput.type === 'password') {
            apiKeyInput.type = 'text';
            icon.className = 'bi bi-eye-slash';
        } else {
            apiKeyInput.type = 'password';
            icon.className = 'bi bi-eye';
        }
    });

    // 测试连接
    document.getElementById('test-connection-btn').addEventListener('click', async function() {
        const btn = this;
        const originalText = btn.innerHTML;

        try {
            btn.disabled = true;
            btn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>测试中...';

            const formData = new FormData(document.getElementById('remoteModelConfigForm'));
            const testData = {
                modelId: formData.get('modelId'),
                apiKey: formData.get('apiKey')
            };

            const response = await fetch('/wkg/api/models/remote/test-connection', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(testData)
            });

            const result = await response.json();

            if (result.code === 200 && result.data.success) {
                showToast('成功', '连接测试成功！', 'success');
            } else {
                showToast('失败', result.data.message || '连接测试失败', 'error');
            }
        } catch (error) {
            console.error('测试连接失败:', error);
            showToast('错误', '连接测试失败: ' + error.message, 'error');
        } finally {
            btn.disabled = false;
            btn.innerHTML = originalText;
        }
    });

    // 保存远程模型配置
    document.getElementById('save-remote-config-btn').addEventListener('click', async function() {
        const btn = this;
        const originalText = btn.innerHTML;

        try {
            btn.disabled = true;
            btn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>保存中...';

            const formData = new FormData(document.getElementById('remoteModelConfigForm'));
            const configData = {
                modelId: formData.get('modelId'),
                apiKey: formData.get('apiKey'),
                remark: formData.get('remark'),
                enabled: formData.has('enabled')
            };

            const response = await fetch('/wkg/api/models/remote/configs', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(configData)
            });

            const result = await response.json();

            if (result.code === 200) {
                showToast('成功', '远程模型配置保存成功！', 'success');
                remoteModelConfigModal.hide();

                // 刷新远程配置和模型列表
                await fetchRemoteConfigs();

                // 合并配置信息到模型数据中
                allRemoteModels.forEach(model => {
                    const config = userRemoteConfigs.find(config => config.modelId === model.id);
                    model.isConfigured = !!config;
                    model.isEnabled = config ? config.enabled : false;
                    model.configId = config ? config.id : null;
                });

                // 重新渲染远程模型
                if (currentActiveTab === 'remote') {
                    renderRemoteModels(allRemoteModels);
                }

                // 配置保存成功
            } else {
                showToast('失败', result.msg || '保存配置失败', 'error');
            }
        } catch (error) {
            console.error('保存配置失败:', error);
            showToast('错误', '保存配置失败: ' + error.message, 'error');
        } finally {
            btn.disabled = false;
            btn.innerHTML = originalText;
        }
    });

    // 测试模型功能已移除，仅保留测试连接功能

    // 将函数暴露到全局作用域
    window.showModelDetails = showModelDetails;
    window.showRemoteModelConfig = showRemoteModelConfig;
});