/**
 * Model Selector Component
 * Populates a select element with available models fetched from the API.
 * Supports localStorage to remember user's model selection across page refreshes.
 * @param {string} selectElementId - The ID of the <select> element.
 */

// 存储键名常量
const STORAGE_KEY = 'selected-model';

/**
 * 从localStorage获取上次选择的模型
 * @returns {string|null} 上次选择的模型值，如果没有则返回null
 */
function getStoredModelSelection() {
    try {
        return localStorage.getItem(STORAGE_KEY);
    } catch (error) {
        console.warn('[ModelSelector] 无法访问localStorage:', error);
        return null;
    }
}

/**
 * 保存模型选择到localStorage
 * @param {string} modelValue - 要保存的模型值
 */
function saveModelSelection(modelValue) {
    try {
        localStorage.setItem(STORAGE_KEY, modelValue);
    } catch (error) {
        console.warn('[ModelSelector] 无法保存到localStorage:', error);
    }
}

/**
 * 设置模型选择监听器，当用户改变选择时自动保存
 * @param {HTMLSelectElement} selectElement - select元素
 */
function setupModelSelectionListener(selectElement) {
    selectElement.addEventListener('change', function() {
        const selectedValue = this.value;
        if (selectedValue) {
            saveModelSelection(selectedValue);
        }
    });
    
    // 如果使用bootstrap-select，也监听其特定事件
    if (typeof $ !== 'undefined' && $.fn.selectpicker) {
        $(selectElement).on('changed.bs.select', function() {
            const selectedValue = this.value;
            if (selectedValue) {
                saveModelSelection(selectedValue);
            }
        });
    }
}

async function initialize(selectElementOrId) {
    let modelSelectElement;

    // 支持传入DOM元素或ID字符串
    if (typeof selectElementOrId === 'string') {
        modelSelectElement = document.getElementById(selectElementOrId);
        if (!modelSelectElement) {
            console.error(`[ModelSelector] Element with ID '${selectElementOrId}' not found.`);
            return;
        }
    } else if (selectElementOrId instanceof HTMLElement) {
        modelSelectElement = selectElementOrId;
    } else {
        console.error(`[ModelSelector] Invalid parameter: expected string ID or HTMLElement, got:`, selectElementOrId);
        return;
    }

    // Add a specific class for CSS targeting if needed
    modelSelectElement.classList.add('model-select-component');

    // Keep select empty and disabled initially
    modelSelectElement.innerHTML = ''; 
    modelSelectElement.disabled = true;

    try {
        console.log("[ModelSelector] 开始获取模型列表...");
        const response = await fetch('/wkg/api/models');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const availableModels = await response.json();
        console.log("[ModelSelector] 获取到模型列表:", availableModels);

        modelSelectElement.innerHTML = ''; // Clear loading state

        if (!availableModels || availableModels.length === 0) {
            console.log("[ModelSelector] 检测到无可用模型，显示引导提示");
            modelSelectElement.innerHTML = '<option value="">无可用模型</option>';
            modelSelectElement.disabled = true;
            console.warn("[ModelSelector] No available models found.");

            // 显示模型下载提示（使用固定位置提示）
            showNoModelsGuideFixed();
            return;
        }

        // 获取上次选择的模型
        const storedSelection = getStoredModelSelection();
        let selectedIndex = 0; // 默认选择第一个
        let foundStoredModel = false;

        availableModels.forEach((model, index) => {
            const option = document.createElement('option');
            option.value = model.value; 
            option.textContent = model.name;
            modelSelectElement.appendChild(option);
            
            // 检查是否匹配存储的选择
            if (storedSelection && model.value === storedSelection) {
                selectedIndex = index;
                foundStoredModel = true;
            }
        });

        if (modelSelectElement.options.length > 0) {
            // 设置选中项：优先使用存储的选择，否则使用第一个
            modelSelectElement.selectedIndex = selectedIndex;
            modelSelectElement.disabled = false;
            
            if (!foundStoredModel && storedSelection) {
                // 当存储的模型不可用时，清除localStorage中的选择并保存新的默认选择
                const defaultModel = availableModels[0].value;
                saveModelSelection(defaultModel);
            }

            // 设置选择变化监听器
            setupModelSelectionListener(modelSelectElement);

            // Initialize or refresh bootstrap-select ONLY after successful population
            if (typeof $ !== 'undefined' && $.fn.selectpicker) {
                // Check if it's already initialized
                if ($(modelSelectElement).data('selectpicker')) {
                    $(modelSelectElement).selectpicker('refresh');
                } else {
                    $(modelSelectElement).selectpicker(); 
                }
            } else {
                console.warn("[ModelSelector] jQuery or $.fn.selectpicker not found. Skipping bootstrap-select initialization.");
            }
        }

    } catch (error) {
        console.error("[ModelSelector] Failed to fetch or populate models:", error);
        modelSelectElement.innerHTML = '<option value="">加载失败</option>';
        modelSelectElement.disabled = true;
    }
}

/**
 * 显示无模型时的引导提示
 */
function showNoModelsGuide() {
    console.log("[ModelSelector] 开始显示无模型引导提示");

    // 检查是否已经显示了提示，避免重复显示
    if (document.querySelector('.no-models-guide')) {
        console.log("[ModelSelector] 引导提示已存在，跳过显示");
        return;
    }

    // 创建提示容器
    const guideContainer = document.createElement('div');
    guideContainer.className = 'no-models-guide alert alert-info d-flex align-items-center';
    guideContainer.style.cssText = `
        margin: 1rem !important;
        padding: 1rem !important;
        border-radius: 8px !important;
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%) !important;
        border: 2px solid #2196f3 !important;
        box-shadow: 0 4px 16px rgba(33, 150, 243, 0.3) !important;
        animation: slideInDown 0.3s ease-out !important;
        position: relative !important;
        z-index: 1000 !important;
        display: flex !important;
        width: calc(100% - 2rem) !important;
        min-height: 120px !important;
    `;

    // 创建图标
    const icon = document.createElement('i');
    icon.className = 'bi bi-download me-3';
    icon.style.cssText = `
        font-size: 2rem !important;
        color: #2196f3 !important;
        flex-shrink: 0 !important;
        display: block !important;
    `;

    // 创建内容区域
    const content = document.createElement('div');
    content.className = 'flex-grow-1';
    content.innerHTML = `
        <h6 class="mb-2" style="color: #1976d2; font-weight: 600;">
            <i class="bi bi-info-circle me-1"></i>
            暂无可用的AI模型
        </h6>
        <p class="mb-2" style="color: #424242; font-size: 0.9rem; line-height: 1.4;">
            要开始AI对话，您需要先下载一个大语言模型。我们为您准备了多种优秀的开源模型供选择。
        </p>
        <div class="d-flex gap-2 flex-wrap">
            <button class="btn btn-primary btn-sm" onclick="goToModelCenter()" style="
                background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
                border: none;
                border-radius: 6px;
                font-weight: 500;
                padding: 0.4rem 1rem;
                transition: all 0.2s ease;
            " onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(33, 150, 243, 0.3)'"
               onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                <i class="bi bi-download me-1"></i>
                前往模型中心
            </button>
            <button class="btn btn-outline-secondary btn-sm" onclick="refreshModels()" style="
                border-radius: 6px;
                font-weight: 500;
                padding: 0.4rem 1rem;
                transition: all 0.2s ease;
            ">
                <i class="bi bi-arrow-clockwise me-1"></i>
                刷新检查
            </button>
        </div>
    `;

    // 组装提示框
    guideContainer.appendChild(icon);
    guideContainer.appendChild(content);

    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .no-models-guide:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(33, 150, 243, 0.15) !important;
        }
    `;
    document.head.appendChild(style);

    // 找到合适的位置插入提示 - 优先选择更稳定的容器
    let targetContainer = null;

    // 尝试多个可能的容器
    const possibleContainers = [
        '.chat-container',
        '.chat-main-page',
        '#chat-messages',
        '.chat-messages',
        'body'
    ];

    for (const selector of possibleContainers) {
        targetContainer = document.querySelector(selector);
        if (targetContainer) {
            console.log(`[ModelSelector] 找到容器: ${selector}`);
            break;
        }
    }

    if (targetContainer) {
        // 如果是聊天消息区域，插入到开头；否则插入到末尾
        if (targetContainer.id === 'chat-messages' || targetContainer.classList.contains('chat-messages')) {
            targetContainer.insertBefore(guideContainer, targetContainer.firstChild);
        } else {
            targetContainer.appendChild(guideContainer);
        }
        console.log("[ModelSelector] 引导提示已插入到:", targetContainer.tagName, targetContainer.className);
    } else {
        console.error("[ModelSelector] 未找到合适的容器插入提示，使用备用方案");
        // 备用方案：创建固定位置的提示
        showNoModelsGuideFixed();
        return;
    }

    console.log("[ModelSelector] 无模型引导提示已显示");

    // 验证提示是否真的在DOM中，并设置监听器防止被删除
    setTimeout(() => {
        const insertedGuide = document.querySelector('.no-models-guide');
        if (insertedGuide) {
            console.log("[ModelSelector] 验证成功：提示框已在DOM中", insertedGuide);
            console.log("[ModelSelector] 提示框位置:", insertedGuide.getBoundingClientRect());
            console.log("[ModelSelector] 提示框样式:", window.getComputedStyle(insertedGuide).display);

            // 设置MutationObserver监听提示框是否被删除
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        mutation.removedNodes.forEach((node) => {
                            if (node.classList && node.classList.contains('no-models-guide')) {
                                console.warn("[ModelSelector] 检测到提示框被删除，尝试重新创建");
                                // 延迟重新创建，避免无限循环
                                setTimeout(() => {
                                    if (!document.querySelector('.no-models-guide')) {
                                        showNoModelsGuide();
                                    }
                                }, 500);
                            }
                        });
                    }
                });
            });

            // 监听整个文档的变化
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

        } else {
            console.error("[ModelSelector] 验证失败：提示框未在DOM中找到");
            // 尝试重新创建
            console.log("[ModelSelector] 尝试重新创建提示框");
            setTimeout(() => showNoModelsGuide(), 200);
        }
    }, 100);
}

/**
 * 前往模型中心
 */
function goToModelCenter() {
    try {
        // 尝试在父窗口中导航到模型中心
        if (window.parent && window.parent !== window) {
            // 在iframe中，通知父窗口导航
            window.parent.postMessage({
                type: 'navigate',
                url: '/wkg/models'
            }, '*');
        } else {
            // 直接导航
            window.location.href = '/wkg/models';
        }
    } catch (error) {
        console.error('导航到模型中心失败:', error);
        // 备用方案：在新标签页打开
        window.open('/wkg/models', '_blank');
    }
}

/**
 * 刷新模型列表
 */
function refreshModels() {
    // 移除现有的提示
    const existingGuide = document.querySelector('.no-models-guide');
    if (existingGuide) {
        existingGuide.remove();
    }

    // 重新初始化模型选择器
    const modelSelect = document.querySelector('#model-select') || document.querySelector('.model-select-component');
    if (modelSelect) {
        initialize(modelSelect);
    }
}

/**
 * 备用方案：显示固定位置的无模型提示
 */
function showNoModelsGuideFixed() {
    console.log("[ModelSelector] 使用固定位置提示方案");

    // 检查是否已经显示了固定提示
    if (document.querySelector('.no-models-guide-fixed')) {
        return;
    }

    // 创建固定位置的提示容器
    const fixedGuide = document.createElement('div');
    fixedGuide.className = 'no-models-guide-fixed';
    fixedGuide.style.cssText = `
        position: fixed !important;
        top: 80px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        z-index: 9999 !important;
        background: #ffffff !important;
        border: 1px solid #e9ecef !important;
        border-radius: 12px !important;
        padding: 1.5rem 2rem !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08) !important;
        max-width: 480px !important;
        width: 90% !important;
        text-align: center !important;
        animation: slideInFromTop 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        backdrop-filter: blur(10px) !important;
    `;

    fixedGuide.innerHTML = `
        <!-- 右上角关闭按钮 -->
        <button onclick="closeFixedGuide()" style="
            position: absolute;
            top: 0.75rem;
            right: 0.75rem;
            background: transparent;
            border: none;
            color: #adb5bd;
            padding: 0.25rem;
            cursor: pointer;
            font-size: 1.125rem;
            transition: all 0.2s ease;
            border-radius: 4px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        " onmouseover="this.style.color='#6c757d'; this.style.background='#f8f9fa'"
           onmouseout="this.style.color='#adb5bd'; this.style.background='transparent'">
            <i class="bi bi-x"></i>
        </button>

        <!-- 主要内容 -->
        <div style="
            color: #2c3e50;
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 0.75rem;
            letter-spacing: -0.01em;
            padding-right: 2rem;
        ">
            暂无可用的AI模型
        </div>
        <div style="
            color: #6c757d;
            font-size: 0.875rem;
            margin-bottom: 1.5rem;
            line-height: 1.5;
        ">
            需要先下载模型才能开始对话
        </div>
        <div style="display: flex; gap: 0.75rem; justify-content: center; align-items: center;">
            <button onclick="goToModelCenter()" style="
                background: #007bff;
                border: none;
                border-radius: 8px;
                color: white;
                padding: 0.625rem 1.25rem;
                cursor: pointer;
                font-size: 0.875rem;
                font-weight: 500;
                transition: all 0.2s ease;
                letter-spacing: -0.01em;
            " onmouseover="this.style.background='#0056b3'" onmouseout="this.style.background='#007bff'">
                前往模型中心
            </button>
            <button onclick="refreshModels()" style="
                background: transparent;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                color: #6c757d;
                padding: 0.625rem 1rem;
                cursor: pointer;
                font-size: 0.875rem;
                font-weight: 500;
                transition: all 0.2s ease;
            " onmouseover="this.style.borderColor='#adb5bd'; this.style.color='#495057'"
               onmouseout="this.style.borderColor='#dee2e6'; this.style.color='#6c757d'">
                刷新
            </button>
        </div>
    `;

    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInFromTop {
            0% {
                opacity: 0;
                transform: translateX(-50%) translateY(-30px) scale(0.95);
            }
            100% {
                opacity: 1;
                transform: translateX(-50%) translateY(0) scale(1);
            }
        }
        .no-models-guide-fixed {
            animation: slideInFromTop 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }
    `;
    document.head.appendChild(style);

    // 插入到body
    document.body.appendChild(fixedGuide);

    console.log("[ModelSelector] 固定位置提示已显示");
}

/**
 * 关闭固定位置提示
 */
function closeFixedGuide() {
    const fixedGuide = document.querySelector('.no-models-guide-fixed');
    if (fixedGuide) {
        fixedGuide.remove();
    }
}

// 将函数添加到全局作用域，以便HTML中的onclick可以访问
window.goToModelCenter = goToModelCenter;
window.refreshModels = refreshModels;
window.closeFixedGuide = closeFixedGuide;

// Export the initialize function
export { initialize };