/**
 * 登录页面JavaScript - EdgeMind设计系统
 * 处理登录表单提交、密码显示切换、错误处理等功能
 */

// 常量定义
const CONTEXT_PATH = window.location.pathname.startsWith('/wkg') ? '/wkg' : '';
const TOKEN_KEY = 'saToken';

// DOM元素
let loginForm;
let usernameInput;
let passwordInput;
let passwordToggle;
let rememberMeCheckbox;
let loginButton;
let errorMessage;
let errorText;
let redirectUrl;

// 初始化函数
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    bindEvents();
    initializePasswordToggle();
    loadRememberedCredentials();
});

/**
 * 初始化DOM元素
 */
function initializeElements() {
    loginForm = document.getElementById('loginForm');
    usernameInput = document.getElementById('username');
    passwordInput = document.getElementById('password');
    passwordToggle = document.getElementById('passwordToggle');
    rememberMeCheckbox = document.getElementById('rememberMe');
    loginButton = document.getElementById('loginButton');
    errorMessage = document.getElementById('errorMessage');
    errorText = document.getElementById('errorText');
    redirectUrl = document.getElementById('redirectUrl');
}

/**
 * 绑定事件监听器
 */
function bindEvents() {
    // 表单提交事件
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // 密码显示切换
    if (passwordToggle) {
        passwordToggle.addEventListener('click', togglePasswordVisibility);
    }
    
    // 输入框焦点事件 - 清除错误状态
    [usernameInput, passwordInput].forEach(input => {
        if (input) {
            input.addEventListener('focus', clearErrorState);
            input.addEventListener('input', clearErrorState);
        }
    });
    
    // 记住我状态变化
    if (rememberMeCheckbox) {
        rememberMeCheckbox.addEventListener('change', handleRememberMeChange);
    }
}

/**
 * 处理登录表单提交
 */
async function handleLogin(event) {
    event.preventDefault();
    
    const username = usernameInput.value.trim();
    const password = passwordInput.value;
    
    // 基础验证
    if (!username || !password) {
        showError('请输入用户名和密码');
        return;
    }
    
    // 设置加载状态
    setLoadingState(true);
    clearErrorState();
    
    try {
        const response = await fetch(`${CONTEXT_PATH}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });
        
        const result = await response.json();
        
        if (result.code === 200 && result.data?.token) {
            // 登录成功
            handleLoginSuccess(result.data, username, password);
        } else {
            // 登录失败
            showError(result.message || '登录失败，请检查用户名或密码');
            setLoadingState(false);
        }
    } catch (error) {
        console.error('登录请求失败:', error);
        showError('网络错误，请稍后重试');
        setLoadingState(false);
    }
}

/**
 * 处理登录成功
 */
function handleLoginSuccess(loginData, username, password) {
    // 保存token
    saveToken(loginData.token);
    
    // 处理记住我功能
    if (rememberMeCheckbox.checked) {
        saveCredentials(username, password);
    } else {
        clearSavedCredentials();
    }
    

    // 延迟跳转，让用户看到成功提示
    setTimeout(() => {
        let redirect = redirectUrl?.value || '/';
        // 确保重定向URL包含上下文路径
        if (!redirect.startsWith(CONTEXT_PATH) && redirect.startsWith('/')) {
            redirect = CONTEXT_PATH + redirect;
        }
        window.location.href = redirect;
    }, 1000);
}

/**
 * 密码显示切换
 */
function togglePasswordVisibility() {
    const isPassword = passwordInput.type === 'password';
    passwordInput.type = isPassword ? 'text' : 'password';
    
    const icon = passwordToggle.querySelector('i');
    icon.className = isPassword ? 'bi bi-eye-slash' : 'bi bi-eye';
}

/**
 * 初始化密码切换功能
 */
function initializePasswordToggle() {
    if (passwordToggle && passwordInput) {
        passwordToggle.style.cursor = 'pointer';
    }
}

/**
 * 设置加载状态
 */
function setLoadingState(loading) {
    if (!loginButton) return;
    
    const btnText = loginButton.querySelector('.btn-text');
    const btnLoading = loginButton.querySelector('.btn-loading');
    
    if (loading) {
        btnText.style.display = 'none';
        btnLoading.style.display = 'inline-flex';
        loginButton.disabled = true;
    } else {
        btnText.style.display = 'inline-flex';
        btnLoading.style.display = 'none';
        loginButton.disabled = false;
    }
}

/**
 * 显示错误信息
 */
function showError(message) {
    if (errorMessage && errorText) {
        errorMessage.className = 'alert alert-danger';
        errorMessage.style.display = 'block';
        errorText.textContent = message;
        
        // 添加震动效果
        errorMessage.style.animation = 'none';
        setTimeout(() => {
            errorMessage.style.animation = 'shake 0.5s ease-in-out';
        }, 10);
    }
}

/**
 * 清除错误状态
 */
function clearErrorState() {
    if (errorMessage) {
        errorMessage.style.display = 'none';
    }
    
    // 清除输入框错误样式
    [usernameInput, passwordInput].forEach(input => {
        if (input) {
            input.classList.remove('is-invalid');
        }
    });
}

/**
 * 保存token到localStorage
 */
function saveToken(token) {
    try {
        localStorage.setItem(TOKEN_KEY, token);
    } catch (error) {
        console.warn('无法保存token到localStorage:', error);
    }
}

/**
 * 保存登录凭据（记住我功能）
 */
function saveCredentials(username, password) {
    if (!rememberMeCheckbox.checked) return;
    
    try {
        const credentials = {
            username: username,
            // 注意：实际生产环境中不应该保存明文密码
            // 这里仅为演示，实际应该使用更安全的方式
            rememberMe: true,
            timestamp: Date.now()
        };
        localStorage.setItem('loginCredentials', JSON.stringify(credentials));
    } catch (error) {
        console.warn('无法保存登录凭据:', error);
    }
}

/**
 * 加载记住的登录凭据
 */
function loadRememberedCredentials() {
    try {
        const saved = localStorage.getItem('loginCredentials');
        if (saved) {
            const credentials = JSON.parse(saved);
            
            // 检查是否过期（7天）
            const isExpired = Date.now() - credentials.timestamp > 7 * 24 * 60 * 60 * 1000;
            
            if (!isExpired && credentials.rememberMe) {
                if (usernameInput && credentials.username) {
                    usernameInput.value = credentials.username;
                }
                if (rememberMeCheckbox) {
                    rememberMeCheckbox.checked = true;
                }
            } else {
                // 过期则清除
                clearSavedCredentials();
            }
        }
    } catch (error) {
        console.warn('无法加载保存的登录凭据:', error);
        clearSavedCredentials();
    }
}

/**
 * 清除保存的登录凭据
 */
function clearSavedCredentials() {
    try {
        localStorage.removeItem('loginCredentials');
    } catch (error) {
        console.warn('无法清除保存的登录凭据:', error);
    }
}

/**
 * 处理记住我状态变化
 */
function handleRememberMeChange() {
    if (!rememberMeCheckbox.checked) {
        clearSavedCredentials();
    }
}

/**
 * 工具函数：HTML转义
 */
function escapeHtml(unsafe) {
    if (typeof unsafe !== 'string') return '';
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}
