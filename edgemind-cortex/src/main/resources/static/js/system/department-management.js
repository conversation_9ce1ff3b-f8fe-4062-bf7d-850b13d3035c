/**
 * 部门管理页面JavaScript - 使用EdgeMind组件系统
 */

// 全局变量
let deptTree = [];
let allDepts = [];
let allUsers = [];

// 获取上下文路径
const CONTEXT_PATH = window.location.pathname.startsWith('/wkg') ? '/wkg' : '';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initPage();
});

/**
 * 初始化页面
 */
function initPage() {
    loadDeptTree();
    loadUsers();
    bindEvents();
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 可以添加其他事件绑定
}

/**
 * 统一的API请求方法 - 使用EdgeMind标准
 */
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'include'
    };

    const finalOptions = { ...defaultOptions, ...options };

    try {
        const response = await fetch(`${CONTEXT_PATH}${url}`, finalOptions);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.code !== 200 && !data.success) {
            throw new Error(data.message || '操作失败');
        }

        return data;
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

/**
 * 显示Toast提示 - 使用EdgeMind组件
 */
function showToast(message, type = 'info') {
    // 检查是否有全局的showToast函数，避免递归调用
    if (typeof window.showToast === 'function' && window.showToast !== showToast) {
        window.showToast(message, type);
    } else {
        // 回退到console.log和简单的页面提示
        console.log(`${type.toUpperCase()}: ${message}`);

        // 创建简单的页面提示
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        document.body.appendChild(alertDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }
}

/**
 * 加载部门树
 */
async function loadDeptTree() {
    try {
        const data = await apiRequest('/api/system/dept/tree');
        deptTree = data.data || [];
        allDepts = flattenDeptTree(deptTree);
        renderDeptTree(deptTree);
        updateParentDeptOptions();
    } catch (error) {
        showToast('加载部门树失败: ' + error.message, 'danger');
        // 显示空状态
        document.getElementById('deptTreeContainer').innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="bi bi-exclamation-triangle" style="font-size: 3rem;"></i>
                <p class="mt-2">部门数据加载失败</p>
            </div>
        `;
    }
}

/**
 * 扁平化部门树
 */
function flattenDeptTree(nodes, result = []) {
    nodes.forEach(node => {
        result.push(node);
        if (node.children && node.children.length > 0) {
            flattenDeptTree(node.children, result);
        }
    });
    return result;
}

/**
 * 加载用户列表（用于负责人选择）
 */
async function loadUsers() {
    try {
        // 使用简化用户API，只获取启用状态的用户
        const data = await apiRequest('/api/system/user/simple?status=1');
        allUsers = data.data || [];
        updateManagerOptions(allUsers);
    } catch (error) {
        console.error('Error loading users:', error);
        showToast('加载用户列表失败', 'warning');
    }
}

/**
 * 渲染部门树 - 使用Bootstrap标准树形组件
 */
function renderDeptTree(nodes, level = 0, parentContainer = null) {
    const container = parentContainer || document.getElementById('deptTreeContainer');
    if (level === 0 && !parentContainer) {
        container.innerHTML = '';
    }

    nodes.forEach(node => {
        const treeNode = document.createElement('div');
        treeNode.className = 'tree-node';
        treeNode.setAttribute('data-dept-id', node.id);
        treeNode.setAttribute('data-level', level);

        const hasChildren = node.children && node.children.length > 0;
        const isDisabled = node.status === 0;

        // 创建树形节点内容
        const nodeContent = document.createElement('div');
        nodeContent.className = `tree-node-content ${isDisabled ? 'disabled' : ''}`;
        nodeContent.onclick = () => selectDept(node.id);

        nodeContent.innerHTML = `
            <!-- 展开/收起按钮 -->
            <div class="tree-toggle ${hasChildren ? '' : 'invisible'}" onclick="event.stopPropagation(); toggleTreeNode(this)">
                <i class="bi bi-chevron-right"></i>
            </div>

            <!-- 树形内容 -->
            <div class="tree-content">
                <i class="bi bi-building tree-icon"></i>
                <div class="dept-info">
                    <div class="dept-name">
                        ${node.deptName || node.name}
                        ${(node.deptCode || node.code) ? `<span class="badge bg-secondary ms-2">${node.deptCode || node.code}</span>` : ''}
                        ${isDisabled ? '<span class="badge bg-danger ms-2">禁用</span>' : '<span class="badge bg-success ms-2">启用</span>'}
                    </div>
                    ${node.managerName ? `
                    <div class="dept-meta">
                        <i class="bi bi-person-badge me-1"></i>负责人: ${node.managerName}
                    </div>` : ''}
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="tree-actions">
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="event.stopPropagation(); editDept(${node.id})" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                    ${isDisabled ?
                        `<button type="button" class="btn btn-outline-success" onclick="event.stopPropagation(); toggleDeptStatus(${node.id}, 1)" title="启用">
                            <i class="bi bi-check-circle"></i>
                        </button>` :
                        `<button type="button" class="btn btn-outline-warning" onclick="event.stopPropagation(); toggleDeptStatus(${node.id}, 0)" title="禁用">
                            <i class="bi bi-pause-circle"></i>
                        </button>`
                    }
                    <button type="button" class="btn btn-outline-info" onclick="event.stopPropagation(); addChildDept(${node.id})" title="添加子部门">
                        <i class="bi bi-plus"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="event.stopPropagation(); deleteDept(${node.id})" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `;

        treeNode.appendChild(nodeContent);

        // 如果有子节点，创建子节点容器
        if (hasChildren) {
            const childrenContainer = document.createElement('div');
            childrenContainer.className = 'tree-children collapsed';
            treeNode.appendChild(childrenContainer);

            // 递归渲染子节点
            renderDeptTree(node.children, level + 1, childrenContainer);
        }

        container.appendChild(treeNode);
    });
}



/**
 * 切换树形节点展开/收起 - 新的标准实现
 */
window.toggleTreeNode = function(toggleElement) {
    const treeNode = toggleElement.closest('.tree-node');
    const childrenContainer = treeNode.querySelector('.tree-children');
    const icon = toggleElement.querySelector('i');

    if (!childrenContainer) return;

    const isExpanded = toggleElement.classList.contains('expanded');

    if (isExpanded) {
        // 收起
        toggleElement.classList.remove('expanded');
        childrenContainer.classList.add('collapsed');
        icon.classList.remove('bi-chevron-down');
        icon.classList.add('bi-chevron-right');
    } else {
        // 展开
        toggleElement.classList.add('expanded');
        childrenContainer.classList.remove('collapsed');
        icon.classList.remove('bi-chevron-right');
        icon.classList.add('bi-chevron-down');
    }
};

/**
 * 展开全部节点 - 更新为新的结构
 */
window.expandAll = function() {
    const toggles = document.querySelectorAll('.tree-toggle:not(.expanded)');
    toggles.forEach(toggle => {
        if (!toggle.classList.contains('invisible')) {
            window.toggleTreeNode(toggle);
        }
    });
};

/**
 * 收起全部节点 - 更新为新的结构
 */
window.collapseAll = function() {
    const toggles = document.querySelectorAll('.tree-toggle.expanded');
    toggles.forEach(toggle => {
        window.toggleTreeNode(toggle);
    });
};

/**
 * 选择部门 - 适配新的树形结构
 */
window.selectDept = function(deptId) {
    // 移除之前的选中状态
    document.querySelectorAll('.tree-node-content').forEach(content => {
        content.classList.remove('active');
    });

    // 添加选中状态
    const selectedNode = document.querySelector(`[data-dept-id="${deptId}"] .tree-node-content`);
    if (selectedNode) {
        selectedNode.classList.add('active');
    }

    // 加载部门详情
    loadDeptDetail(deptId);
};

/**
 * 加载部门详情
 */
async function loadDeptDetail(deptId) {
    try {
        const data = await apiRequest(`/api/system/dept/${deptId}`);
        renderDeptDetail(data.data);
    } catch (error) {
        console.error('Error:', error);
        showToast('加载部门详情失败: ' + error.message, 'danger');
    }
}

/**
 * 渲染部门详情
 */
function renderDeptDetail(dept) {
    const detailContainer = document.getElementById('deptDetail');
    detailContainer.innerHTML = `
        <div class="mb-3">
            <label class="form-label fw-bold">部门名称</label>
            <div class="form-control-plaintext">${dept.deptName}</div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">部门编码</label>
            <div class="form-control-plaintext"><code>${dept.deptCode || '暂无编码'}</code></div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">部门路径</label>
            <div class="form-control-plaintext">${dept.deptPath || '-'}</div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">部门层级</label>
            <div class="form-control-plaintext">第${dept.deptLevel}级</div>
        </div>
        ${dept.managerName ? `
        <div class="mb-3">
            <label class="form-label fw-bold">部门负责人</label>
            <div class="form-control-plaintext">${dept.managerName}</div>
        </div>
        ` : ''}
        ${dept.contactPhone ? `
        <div class="mb-3">
            <label class="form-label fw-bold">联系电话</label>
            <div class="form-control-plaintext">${dept.contactPhone}</div>
        </div>
        ` : ''}
        ${dept.contactEmail ? `
        <div class="mb-3">
            <label class="form-label fw-bold">邮箱</label>
            <div class="form-control-plaintext">${dept.contactEmail}</div>
        </div>
        ` : ''}
        ${dept.address ? `
        <div class="mb-3">
            <label class="form-label fw-bold">部门地址</label>
            <div class="form-control-plaintext">${dept.address}</div>
        </div>
        ` : ''}
        <div class="mb-3">
            <label class="form-label fw-bold">状态</label>
            <div class="form-control-plaintext">
                <span class="badge ${dept.status === 1 ? 'bg-success' : 'bg-danger'}">
                    ${dept.status === 1 ? '启用' : '禁用'}
                </span>
            </div>
        </div>
        ${dept.description ? `
        <div class="mb-3">
            <label class="form-label fw-bold">部门描述</label>
            <div class="form-control-plaintext">${dept.description}</div>
        </div>
        ` : ''}
        <div class="mb-3">
            <label class="form-label fw-bold">创建时间</label>
            <div class="form-control-plaintext">${formatDateTime(dept.createTime)}</div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">更新时间</label>
            <div class="form-control-plaintext">${formatDateTime(dept.updateTime)}</div>
        </div>
    `;
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
}

// showToast函数已移除，使用showToast替代

/**
 * 显示创建部门模态框
 */
function showCreateModal() {
    document.getElementById('deptModalTitle').textContent = '新增部门';
    document.getElementById('deptForm').reset();
    document.getElementById('deptId').value = '';
    document.getElementById('parentId').value = '0';

    const modal = new bootstrap.Modal(document.getElementById('deptModal'));
    modal.show();
}

/**
 * 添加子部门
 */
function addChildDept(parentId) {
    document.getElementById('deptModalTitle').textContent = '新增子部门';
    document.getElementById('deptForm').reset();
    document.getElementById('deptId').value = '';
    document.getElementById('parentId').value = parentId;

    const modal = new bootstrap.Modal(document.getElementById('deptModal'));
    modal.show();
}

/**
 * 编辑部门
 */
async function editDept(deptId) {
    try {
        const data = await apiRequest(`/api/system/dept/${deptId}`);
        const dept = data.data;
        document.getElementById('deptModalTitle').textContent = '编辑部门';
        document.getElementById('deptId').value = dept.id;
        document.getElementById('deptName').value = dept.deptName;
        document.getElementById('deptCode').value = dept.deptCode;
        document.getElementById('parentId').value = dept.parentId || '0';
        document.getElementById('managerId').value = dept.managerId || '';
        document.getElementById('phone').value = dept.contactPhone || '';
        document.getElementById('email').value = dept.contactEmail || '';
        document.getElementById('sortOrder').value = dept.sortOrder || 0;
        document.getElementById('status').value = dept.status;
        document.getElementById('address').value = dept.address || '';
        document.getElementById('description').value = dept.description || '';

        const modal = new bootstrap.Modal(document.getElementById('deptModal'));
        modal.show();
    } catch (error) {
        console.error('Error:', error);
        showToast('获取部门信息失败: ' + error.message, 'danger');
    }
}

/**
 * 保存部门
 */
async function saveDept() {
    const deptId = document.getElementById('deptId').value;
    const isEdit = !!deptId;

    // 表单验证
    if (!validateDeptForm()) {
        return;
    }

    const formData = {
        deptName: document.getElementById('deptName').value,
        deptCode: document.getElementById('deptCode').value,
        parentId: parseInt(document.getElementById('parentId').value) || 0,
        managerId: parseInt(document.getElementById('managerId').value) || null,
        contactPhone: document.getElementById('phone').value || null,
        contactEmail: document.getElementById('email').value || null,
        sortOrder: parseInt(document.getElementById('sortOrder').value) || 0,
        status: parseInt(document.getElementById('status').value),
        address: document.getElementById('address').value || null,
        description: document.getElementById('description').value || null
    };

    if (isEdit) {
        formData.id = parseInt(deptId);
    }

    try {
        const url = isEdit ? `/api/system/dept/${deptId}` : '/api/system/dept';
        const method = isEdit ? 'PUT' : 'POST';

        const data = await apiRequest(url, {
            method: method,
            body: JSON.stringify(formData)
        });

        if (data.code === 200) {
            showToast(isEdit ? '部门更新成功' : '部门创建成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('deptModal')).hide();
            loadDeptTree();
        } else {
            showToast((isEdit ? '部门更新失败: ' : '部门创建失败: ') + data.message, 'danger');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('保存失败: ' + error.message, 'danger');
    }
}

/**
 * 切换部门状态（启用/禁用）
 */
async function toggleDeptStatus(deptId, newStatus) {
    try {
        const statusText = newStatus === 1 ? '启用' : '禁用';
        if (!confirm(`确定要${statusText}这个部门吗？`)) {
            return;
        }

        const data = await apiRequest(`/api/system/dept/${deptId}/status`, {
            method: 'PUT',
            body: JSON.stringify({ status: newStatus })
        });

        showToast(`部门${statusText}成功`, 'success');
        loadDeptTree();
        // 如果当前选中的是这个部门，刷新详情
        const selectedNode = document.querySelector('.dept-node.selected');
        if (selectedNode && selectedNode.getAttribute('data-dept-id') == deptId) {
            loadDeptDetail(deptId);
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('操作失败: ' + error.message, 'danger');
    }
}

/**
 * 删除部门
 */
async function deleteDept(deptId) {
    try {
        // 先检查是否可以删除
        const checkData = await apiRequest(`/api/system/dept/${deptId}/can-delete`);
        if (!checkData.data) {
            showToast('该部门下还有子部门或用户，无法删除', 'warning');
            return;
        }

        if (!confirm('确定要删除这个部门吗？此操作不可恢复。')) {
            return;
        }

        const data = await apiRequest(`/api/system/dept/${deptId}`, {
            method: 'DELETE'
        });

        showToast('部门删除成功', 'success');
        loadDeptTree();
        // 清空详情面板
        document.getElementById('deptDetail').innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="bi bi-building" style="font-size: 3rem;"></i>
                <p class="mt-2">请选择一个部门查看详情</p>
            </div>
        `;
    } catch (error) {
        console.error('Error:', error);
        showToast('删除失败: ' + error.message, 'danger');
    }
}

/**
 * 更新父部门选项
 */
function updateParentDeptOptions() {
    const parentSelect = document.getElementById('parentId');
    const currentOptions = parentSelect.innerHTML;

    // 保留顶级部门选项
    parentSelect.innerHTML = '<option value="0">顶级部门</option>';

    // 添加其他部门作为父部门选项
    allDepts.forEach(dept => {
        const option = document.createElement('option');
        option.value = dept.id;
        option.textContent = dept.deptName;
        parentSelect.appendChild(option);
    });
}

/**
 * 更新负责人选项
 */
function updateManagerOptions(users) {
    const managerSelect = document.getElementById('managerId');
    managerSelect.innerHTML = '<option value="">请选择负责�?/option>';

    users.forEach(user => {
        const option = document.createElement('option');
        option.value = user.id;
        option.textContent = `${user.nickname || user.username} (${user.username})`;
        managerSelect.appendChild(option);
    });
}

/**
 * 表单验证
 */
function validateDeptForm() {
    const deptName = document.getElementById('deptName').value.trim();
    const deptCode = document.getElementById('deptCode').value.trim();
    const email = document.getElementById('email').value.trim();
    const phone = document.getElementById('phone').value.trim();

    if (!deptName) {
        showToast('请输入部门名称', 'warning');
        return false;
    }

    if (!deptCode) {
        showToast('请输入部门编码', 'warning');
        return false;
    }

    // 部门编码格式验证
    if (!/^[A-Z0-9_]+$/.test(deptCode)) {
        showToast('部门编码只能包含大写字母、数字和下划线', 'warning');
        return false;
    }

    // 邮箱格式验证
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        showToast('邮箱格式不正确', 'warning');
        return false;
    }

    // 手机号格式验证
    if (phone && !/^1[3-9]\d{9}$/.test(phone)) {
        showToast('手机号格式不正确', 'warning');
        return false;
    }

    return true;
}

/**
 * 更新父部门选择选项
 */
function updateParentDeptOptions() {
    const parentSelect = document.getElementById('parentId');
    parentSelect.innerHTML = '<option value="0">顶级部门</option>';

    function addOptions(depts, level = 0) {
        depts.forEach(dept => {
            const option = document.createElement('option');
            option.value = dept.id;
            option.textContent = '　'.repeat(level) + dept.deptName;
            parentSelect.appendChild(option);

            if (dept.children && dept.children.length > 0) {
                addOptions(dept.children, level + 1);
            }
        });
    }

    addOptions(deptTree);
}

/**
 * 更新负责人选择选项
 */
function updateManagerOptions(users) {
    const managerSelect = document.getElementById('managerId');
    managerSelect.innerHTML = '<option value="">请选择负责人</option>';

    users.forEach(user => {
        const option = document.createElement('option');
        option.value = user.id;
        option.textContent = `${user.nickname || user.username} (${user.username})`;
        managerSelect.appendChild(option);
    });
}



/**
 * 绑定事件监听器
 */
function bindEvents() {
    // 保存部门按钮
    document.getElementById('saveDeptBtn')?.addEventListener('click', saveDept);

    // 新增部门按钮
    document.getElementById('addDeptBtn')?.addEventListener('click', showCreateModal);

    // 表单提交事件
    document.getElementById('deptForm')?.addEventListener('submit', function(e) {
        e.preventDefault();
        saveDept();
    });
}

/**
 * 切换节点展开/收起
 */
function toggleNode(icon) {
    const isExpanded = icon.classList.contains('bi-chevron-down');
    const deptNode = icon.closest('.dept-node');
    const childContainer = deptNode.nextElementSibling;

    if (childContainer && childContainer.classList.contains('dept-children')) {
        if (isExpanded) {
            // 收起
            icon.classList.remove('bi-chevron-down');
            icon.classList.add('bi-chevron-right');
            childContainer.style.display = 'none';
        } else {
            // 展开
            icon.classList.remove('bi-chevron-right');
            icon.classList.add('bi-chevron-down');
            childContainer.style.display = 'block';
        }
    }
}



// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initPage);
