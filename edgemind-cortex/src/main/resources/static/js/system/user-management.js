/**
 * 用户管理页面JavaScript - 使用EdgeMind组件系统
 */

// 全局变量
let currentPage = 1;
let pageSize = 10;
let selectedUserIds = [];
let isLoading = false; // 防止重复请求
let paginationEventBound = false; // 防止重复绑定分页事件

// 获取上下文路径
const CONTEXT_PATH = '/wkg';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initPage();
});

/**
 * 初始化页面
 */
function initPage() {
    loadUserList();
    loadDepartments();
    loadRoles();
    bindEvents();
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 搜索表单提交
    document.getElementById('searchForm').addEventListener('submit', function(e) {
        e.preventDefault();
        currentPage = 1;
        loadUserList();
    });

    // 全选复选框
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('input[name="userCheckbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedUsers();
    });
}

/**
 * 统一的API请求方法 - 使用EdgeMind标准
 */
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'include'
    };

    const finalOptions = { ...defaultOptions, ...options };

    try {
        const response = await fetch(`${CONTEXT_PATH}${url}`, finalOptions);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.code !== 200) {
            throw new Error(data.message || '操作失败');
        }

        return data;
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

/**
 * 显示Toast提示 - 使用EdgeMind组件
 */
function showToast(message, type = 'info') {
    // 检查是否有全局的showToast函数，避免递归调用
    if (typeof window.showToast === 'function' && window.showToast !== showToast) {
        window.showToast(message, type);
    } else {
        // 回退到console.log和简单的页面提示
        console.log(`${type.toUpperCase()}: ${message}`);

        // 创建简单的页面提示
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        document.body.appendChild(alertDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }
}

/**
 * 加载用户列表
 */
async function loadUserList() {
    // 防止重复请求
    if (isLoading) {
        console.log('用户管理请求正在进行中，跳过重复请求');
        return;
    }

    try {
        isLoading = true;

        const requestData = {
            pageNum: currentPage,
            pageSize: pageSize,
            username: document.getElementById('searchUsername').value,
            nickname: document.getElementById('searchNickname').value,
            deptId: document.getElementById('searchDept').value,
            status: document.getElementById('searchStatus').value
        };

        console.log('用户管理分页请求参数:', requestData); // 调试日志

        // 移除空值参数（包括空字符串、null、undefined）
        Object.keys(requestData).forEach(key => {
            if (requestData[key] === null || requestData[key] === undefined || requestData[key] === '') {
                delete requestData[key];
            }
        });

        // 使用POST请求发送JSON数据
        const data = await apiRequest('/api/system/user/page', {
            method: 'POST',
            body: JSON.stringify(requestData)
        });

        console.log('API返回的分页数据:', data.data); // 调试日志
        renderUserTable(data.data.records || []);
        renderPagination(data.data);
    } catch (error) {
        showToast('加载用户列表失败: ' + error.message, 'danger');
    } finally {
        isLoading = false;
    }
}

/**
 * 渲染用户表格
 */
function renderUserTable(users) {
    const tbody = document.getElementById('userTableBody');
    tbody.innerHTML = '';

    users.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <input type="checkbox" class="form-check-input" name="userCheckbox" value="${user.id}" onchange="updateSelectedUsers()">
            </td>
            <td>
                <div class="avatar-placeholder">
                    ${user.avatar ? `<img src="${user.avatar}" class="rounded-circle" width="32" height="32">` : 
                      `<i class="bi bi-person-fill text-muted"></i>`}
                </div>
            </td>
            <td>${user.username}</td>
            <td>${user.nickname || '-'}</td>
            <td>${user.email || '-'}</td>
            <td>${user.phone || '-'}</td>
            <td>${user.deptName || '-'}</td>
            <td>
                <span class="badge status-badge ${user.status === 1 ? 'bg-success' : 'bg-danger'}">
                    ${user.status === 1 ? '启用' : '禁用'}
                </span>
            </td>
            <td>
                ${user.roles && user.roles.length > 0 ?
                    user.roles.map(role => role.roleName || role.name || role).join(', ') :
                    '-'}
            </td>
            <td>
                ${user.lastLoginTime ? formatDateTime(user.lastLoginTime) : '-'}
                ${user.lastLoginIp ? `<br><small class="text-muted">${user.lastLoginIp}</small>` : ''}
            </td>
            <td>${formatDateTime(user.createTime)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="editUser(${user.id})" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="showResetPasswordModal(${user.id}, '${user.username}')" title="重置密码">
                        <i class="bi bi-key"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="deleteUser(${user.id})" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

/**
 * 渲染分页
 */
function renderPagination(pageData) {
    const pagination = document.getElementById('pagination');
    if (!pagination) {
        console.error('分页容器未找到');
        return;
    }

    pagination.innerHTML = '';

    // 清除之前的分页信息
    const existingInfo = pagination.parentNode.querySelector('.pagination-info');
    if (existingInfo) {
        existingInfo.remove();
    }

    console.log('分页数据详情:', pageData); // 调试日志

    // MyBatis Plus IPage 字段映射
    const totalPages = pageData.pages || pageData.totalPages || 1;
    const current = pageData.current || pageData.pageNum || 1;
    const total = pageData.total || pageData.totalCount || 0;
    const size = pageData.size || pageData.pageSize || 10;

    // 如果没有数据，显示提示信息
    if (total === 0) {
        const noDataLi = document.createElement('li');
        noDataLi.className = 'page-item disabled';
        noDataLi.innerHTML = '<span class="page-link">暂无数据</span>';
        pagination.appendChild(noDataLi);
        return;
    }

    // 如果只有一页，显示页码但不可点击
    if (totalPages <= 1) {
        const singlePageLi = document.createElement('li');
        singlePageLi.className = 'page-item active';
        singlePageLi.innerHTML = '<span class="page-link">1</span>';
        pagination.appendChild(singlePageLi);

        // 显示分页信息
        const infoDiv = document.createElement('div');
        infoDiv.className = 'text-center mt-3 text-muted small pagination-info';
        infoDiv.innerHTML = `共 ${total} 条记录`;
        pagination.parentNode.appendChild(infoDiv);
        return;
    }

    // 上一页
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${current === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${current - 1})">上一页</a>`;
    pagination.appendChild(prevLi);

    // 页码
    const startPage = Math.max(1, current - 2);
    const endPage = Math.min(totalPages, current + 2);

    // 如果起始页大于1，显示第一页和省略号
    if (startPage > 1) {
        const firstLi = document.createElement('li');
        firstLi.className = 'page-item';
        firstLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(1)">1</a>`;
        pagination.appendChild(firstLi);

        if (startPage > 2) {
            const ellipsisLi = document.createElement('li');
            ellipsisLi.className = 'page-item disabled';
            ellipsisLi.innerHTML = '<span class="page-link">...</span>';
            pagination.appendChild(ellipsisLi);
        }
    }

    // 显示页码
    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === current ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }

    // 如果结束页小于总页数，显示省略号和最后一页
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            const ellipsisLi = document.createElement('li');
            ellipsisLi.className = 'page-item disabled';
            ellipsisLi.innerHTML = '<span class="page-link">...</span>';
            pagination.appendChild(ellipsisLi);
        }

        const lastLi = document.createElement('li');
        lastLi.className = 'page-item';
        lastLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${totalPages})">${totalPages}</a>`;
        pagination.appendChild(lastLi);
    }

    // 下一页
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${current === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${current + 1})">下一页</a>`;
    pagination.appendChild(nextLi);

    // 显示分页信息
    const start = (current - 1) * size + 1;
    const end = Math.min(current * size, total);

    const infoDiv = document.createElement('div');
    infoDiv.className = 'text-center mt-3 text-muted small pagination-info';
    infoDiv.innerHTML = `显示第 ${start}-${end} 条，共 ${total} 条记录`;
    pagination.parentNode.appendChild(infoDiv);
}

/**
 * 切换页面
 */
function changePage(page) {
    if (page < 1 || isLoading) return;
    currentPage = page;
    loadUserList();
}

/**
 * 更新选中的用户
 */
function updateSelectedUsers() {
    const checkboxes = document.querySelectorAll('input[name="userCheckbox"]:checked');
    selectedUserIds = Array.from(checkboxes).map(cb => parseInt(cb.value));
    
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    batchDeleteBtn.disabled = selectedUserIds.length === 0;
}

/**
 * 显示创建用户模态框
 */
function showCreateModal() {
    document.getElementById('userModalTitle').textContent = '新增用户';
    document.getElementById('userForm').reset();
    document.getElementById('userId').value = '';

    // 启用用户名字段
    document.getElementById('username').disabled = false;

    // 显示密码字段
    document.getElementById('password').parentElement.style.display = 'block';
    document.getElementById('confirmPassword').parentElement.style.display = 'block';

    const modal = new bootstrap.Modal(document.getElementById('userModal'));
    modal.show();
}

/**
 * 编辑用户
 */
function editUser(userId) {
    fetch(`/wkg/api/system/user/${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success || data.code === 200) {
                const user = data.data;
                document.getElementById('userModalTitle').textContent = '编辑用户';
                document.getElementById('userId').value = user.id;
                document.getElementById('username').value = user.username;
                document.getElementById('username').disabled = true; // 编辑时禁用用户名修改
                document.getElementById('nickname').value = user.nickname || '';
                document.getElementById('email').value = user.email || '';
                document.getElementById('phone').value = user.phone || '';
                document.getElementById('deptId').value = user.deptId || '';
                document.getElementById('status').value = user.status;
                document.getElementById('remark').value = user.remark || '';

                // 隐藏密码字段
                document.getElementById('password').parentElement.style.display = 'none';
                document.getElementById('confirmPassword').parentElement.style.display = 'none';
                
                // 设置角色
                setUserRoles(user.roles || []);
                
                const modal = new bootstrap.Modal(document.getElementById('userModal'));
                modal.show();
            } else {
                showToast('获取用户信息失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误，请稍后重试', 'danger');
        });
}

/**
 * 保存用户
 */
function saveUser() {
    const userId = document.getElementById('userId').value;
    const isEdit = !!userId;
    
    // 表单验证
    if (!validateUserForm(isEdit)) {
        return;
    }
    
    const formData = {
        username: document.getElementById('username').value,
        nickname: document.getElementById('nickname').value,
        email: document.getElementById('email').value,
        phone: document.getElementById('phone').value,
        deptId: document.getElementById('deptId').value || null,
        status: parseInt(document.getElementById('status').value),
        remark: document.getElementById('remark').value,
        roleIds: getSelectedRoles()
    };
    
    if (!isEdit) {
        formData.password = document.getElementById('password').value;
    } else {
        formData.id = parseInt(userId);
    }
    
    const url = isEdit ? `/wkg/api/system/user/${userId}` : `/wkg/api/system/user`;
    const method = isEdit ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(isEdit ? '用户更新成功' : '用户创建成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('userModal')).hide();
            loadUserList();
        } else {
            showToast((isEdit ? '用户更新失败: ' : '用户创建失败: ') + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请稍后重试', 'danger');
    });
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
}

// showAlert函数已移除，使用EdgeMind Toast组件

/**
 * 加载部门列表
 */
function loadDepartments() {
    fetch(`/wkg/api/system/dept/all`)
        .then(response => response.json())
        .then(data => {
            if (data.success || data.code === 200) {
                const deptSelects = document.querySelectorAll('#searchDept, #deptId');
                deptSelects.forEach(select => {
                    // 保留第一个选项
                    const firstOption = select.firstElementChild;
                    select.innerHTML = '';
                    select.appendChild(firstOption);

                    data.data.forEach(dept => {
                        const option = document.createElement('option');
                        option.value = dept.id;
                        option.textContent = dept.deptName;
                        select.appendChild(option);
                    });
                });
            }
        })
        .catch(error => console.error('加载部门列表失败:', error));
}

/**
 * 加载角色列表
 */
function loadRoles() {
    fetch(`/wkg/api/system/role/all`)
        .then(response => response.json())
        .then(data => {
            if (data.success || data.code === 200) {
                const roleContainer = document.getElementById('roleCheckboxes');
                roleContainer.innerHTML = '';

                data.data.forEach(role => {
                    const div = document.createElement('div');
                    div.className = 'col-md-4 mb-2';
                    div.innerHTML = `
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="${role.id}" id="role_${role.id}">
                            <label class="form-check-label" for="role_${role.id}">
                                ${role.roleName}
                            </label>
                        </div>
                    `;
                    roleContainer.appendChild(div);
                });
            }
        })
        .catch(error => console.error('加载角色列表失败:', error));
}

/**
 * 设置用户角色
 */
function setUserRoles(roles) {
    // 清除所有选中状态
    const checkboxes = document.querySelectorAll('#roleCheckboxes input[type="checkbox"]');
    checkboxes.forEach(checkbox => checkbox.checked = false);

    // 设置选中状态
    roles.forEach(role => {
        const checkbox = document.getElementById(`role_${role.id}`);
        if (checkbox) {
            checkbox.checked = true;
        }
    });
}

/**
 * 获取选中的角色
 */
function getSelectedRoles() {
    const checkboxes = document.querySelectorAll('#roleCheckboxes input[type="checkbox"]:checked');
    return Array.from(checkboxes).map(cb => parseInt(cb.value));
}

/**
 * 表单验证
 */
function validateUserForm(isEdit) {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const email = document.getElementById('email').value.trim();
    const phone = document.getElementById('phone').value.trim();

    if (!username) {
        showToast('请输入用户名', 'warning');
        return false;
    }

    if (username.length < 3 || username.length > 50) {
        showToast('用户名长度必须在3-50个字符之间', 'warning');
        return false;
    }

    if (!/^[a-zA-Z0-9_]+$/.test(username)) {
        showToast('用户名只能包含字母、数字和下划线', 'warning');
        return false;
    }

    if (!isEdit) {
        if (!password) {
            showToast('请输入密码', 'warning');
            return false;
        }

        if (password.length < 6 || password.length > 20) {
            showToast('密码长度必须在6-20位之间', 'warning');
            return false;
        }

        if (password !== confirmPassword) {
            showToast('两次输入的密码不一致', 'warning');
            return false;
        }
    }

    const nickname = document.getElementById('nickname').value.trim();
    if (nickname && nickname.length > 50) {
        showToast('昵称长度不能超过50个字符', 'warning');
        return false;
    }

    if (email) {
        if (email.length > 100) {
            showToast('邮箱长度不能超过100个字符', 'warning');
            return false;
        }
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
            showToast('邮箱格式不正确', 'warning');
            return false;
        }
    }

    if (phone && !/^1[3-9]\d{9}$/.test(phone)) {
        showToast('手机号格式不正确', 'warning');
        return false;
    }

    const remark = document.getElementById('remark').value.trim();
    if (remark && remark.length > 255) {
        showToast('备注长度不能超过255个字符', 'warning');
        return false;
    }

    // 验证角色必填
    const selectedRoles = getSelectedRoles();
    if (!selectedRoles || selectedRoles.length === 0) {
        showToast('请至少选择一个角色', 'warning');
        return false;
    }

    // 验证状态必填
    const status = document.getElementById('status').value;
    if (status === null || status === undefined || status === '') {
        showToast('请选择用户状态', 'warning');
        return false;
    }

    return true;
}

/**
 * 重置搜索
 */
function resetSearch() {
    document.getElementById('searchForm').reset();
    currentPage = 1;
    loadUserList();
}

// 旧版本密码管理函数已删除，使用文件末尾的新版本

/**
 * 删除用户
 */
function deleteUser(userId) {
    if (!confirm('确定要删除这个用户吗？此操作不可恢复。')) {
        return;
    }

    fetch(`/wkg/api/system/user/${userId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success || data.code === 200) {
            showToast('用户删除成功', 'success');
            loadUserList();
        } else {
            showToast('用户删除失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请稍后重试', 'danger');
    });
}

/**
 * 批量删除用户
 */
function batchDelete() {
    if (selectedUserIds.length === 0) {
        showToast('请选择要删除的用户', 'warning');
        return;
    }

    if (!confirm(`确定要删除选中的${selectedUserIds.length}个用户吗？此操作不可恢复。`)) {
        return;
    }

    fetch(`/wkg/api/system/user/batch`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(selectedUserIds)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success || data.code === 200) {
            showToast('用户批量删除成功', 'success');
            selectedUserIds = [];
            updateSelectedUsers();
            loadUserList();
        } else {
            showToast('用户批量删除失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请稍后重试', 'danger');
    });
}





/**
 * 显示重置密码模态框
 */
function showResetPasswordModal(userId, username) {
    document.getElementById('resetUserId').value = userId;
    document.getElementById('newPassword').value = '';
    document.getElementById('confirmNewPassword').value = '';

    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();
}

/**
 * 重置用户密码（管理员操作）
 */
async function resetPassword() {
    try {
        const userId = document.getElementById('resetUserId').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmNewPassword = document.getElementById('confirmNewPassword').value;

        // 验证输入
        if (!newPassword) {
            showToast('请输入新密码', 'warning');
            return;
        }

        if (newPassword.length < 6 || newPassword.length > 20) {
            showToast('密码长度必须在6-20个字符之间', 'warning');
            return;
        }

        if (newPassword !== confirmNewPassword) {
            showToast('两次输入的密码不一致', 'warning');
            return;
        }

        const data = await apiRequest(`/api/system/user/${userId}/reset-password`, {
            method: 'POST',
            body: JSON.stringify({
                newPassword: newPassword
            })
        });

        if (data.code === 200) {
            showToast('密码重置成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();
        } else {
            showToast('密码重置失败: ' + data.message, 'danger');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('密码重置失败: ' + error.message, 'danger');
    }
}


