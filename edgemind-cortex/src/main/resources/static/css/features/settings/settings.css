/* settings.css - 系统设置页面样式 - 已更新 */

:root {
    --primary-color: #4285f4;
    --text-primary: #2c3e50;
    --text-secondary: #6c757d;
    --border-color: #dee2e6;
    --bg-light: #f8f9fa;
    --card-bg: #ffffff;
    --hover-bg: #f1f3f4;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

body {
    background-color: var(--bg-light);
    color: var(--text-primary);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    font-weight: 400;
    min-height: 100vh;
}

.settings-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem;
    background-color: var(--bg-light);
    min-height: 100vh;
}

.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.page-header h1 {
    color: var(--primary-color);
    font-size: 2.25rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(66, 133, 244, 0.1);
}

.section-title {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 1.125rem;
    margin-bottom: 1rem;
}

.settings-header h2 {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
}

.settings-section {
    margin-bottom: 1rem;
}

.settings-section-title {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1.5rem;
    margin-bottom: 2rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--primary-color);
}

.settings-card {
    background: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 12px !important;
    margin-bottom: 1.5rem !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    transition: all 0.2s ease !important;
}

.settings-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.settings-card .card-header {
    background: var(--hover-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 1.25rem 2rem;
    border-radius: 12px 12px 0 0;
}

.settings-card .card-title {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1.125rem;
    margin: 0;
}

.settings-card .card-body {
    padding: 2rem;
}

.settings-form-label {
    font-weight: 500;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
    font-size: 0.95rem;
}

.form-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

/* 输入框样式 */
.form-control {
    border: 1px solid var(--border-color) !important;
    border-radius: 8px !important;
    padding: 0.875rem 1.125rem !important;
    font-size: 0.95rem !important;
    transition: all 0.15s ease-in-out !important;
    background-color: var(--card-bg) !important;
    font-family: inherit !important;
}

/* 输入框聚焦效果强制覆盖 */
.settings-container .form-control:focus,
#storagePath:focus,
#personalStoragePath:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(66, 133, 244, 0.25) !important;
    outline: none !important;
    background-color: var(--card-bg) !important;
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
    border: 1px solid transparent;
    transition: all 0.15s ease-in-out;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

/* 按钮样式强制覆盖 */
.settings-container .btn-primary,
#syncButton,
#confirmSyncBtn {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
    border-radius: 8px !important;
    padding: 0.875rem 1.5rem !important;
    font-weight: 500 !important;
    transition: all 0.15s ease-in-out !important;
}

/* 按钮悬停效果强制覆盖 */
.settings-container .btn-primary:hover,
#syncButton:hover,
#confirmSyncBtn:hover {
    background-color: #3367d6 !important;
    border-color: #3367d6 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3) !important;
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 133, 244, 0.2);
}

.btn-outline-secondary {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-outline-secondary:hover {
    background-color: var(--hover-bg);
    border-color: var(--text-secondary);
    color: var(--text-primary);
    transform: translateY(-1px);
}

/* 输入组样式强制覆盖 */
.settings-container .input-group {
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

/* 输入组内部元素样式强制覆盖 */
.settings-container .input-group .form-control {
    border-right: none !important;
    border-radius: 8px 0 0 8px !important;
}

.settings-container .input-group .btn {
    border-radius: 0 8px 8px 0 !important;
    border-left: none !important;
}

.input-group .form-control:focus {
    z-index: 3;
}

/* 表单标签样式 */
.settings-form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
}

/* 表单帮助文字 */
.form-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
    line-height: 1.4;
}

/* 系统信息行样式 */
.system-info-row {
    line-height: 1.5;
    padding: 0.75rem 0;
    min-height: auto;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.system-info-row:last-child {
    border-bottom: none;
}

.system-info-row .label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.4;
}

.system-info-row .value {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 14px;
    line-height: 1.4;
}

/* 系统信息卡片样式 - 保持紧凑 */
.settings-card:first-child .card-body {
    margin: 0;
    padding: 0 1.5rem;
}

.settings-card:first-child .row {
    margin: 0;
    align-items: center;
}

.settings-card:first-child .row.mb-3 {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 0 !important;
}

.settings-card:first-child .row.mb-3:last-child {
    border-bottom: none;
}

.settings-card:first-child .row .col-3 {
    padding: 0.5rem 1rem 0.5rem 0;
    display: flex;
    align-items: center;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.settings-card:first-child .row .col-9 {
    padding: 0.5rem 0 0.5rem 1rem;
    display: flex;
    align-items: center;
}

.settings-card:first-child .row .col-9 .d-flex {
    align-items: center;
    gap: 0.5rem;
}

/* 知识库设置卡片样式 - 保持宽松 */
.settings-card:not(:first-child) .card-body {
    padding: 2rem;
}

/* 调整复制按钮大小 */
.settings-card:first-child #copyIdentifierBtn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.2;
    height: auto;
    min-width: auto;
    border-radius: 4px;
}

.settings-card:first-child #copyIdentifierBtn i {
    font-size: 0.7rem;
}

.fw-medium.text-secondary {
    font-size: 0.85rem;
    color: var(--text-secondary) !important;
    font-weight: 500 !important;
}

.text-primary.fw-medium {
    font-size: 0.85rem;
    color: var(--text-primary) !important;
    font-weight: 500 !important;
}

/* 警告提示框样式 */
.alert {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.25rem 1.5rem;
    margin-top: 1rem;
    margin-bottom: 1.5rem;
    background-color: var(--card-bg);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-info {
    background-color: #e7f3ff;
    border-color: #b8daff;
    color: #0c5460;
    border-left: 4px solid var(--primary-color);
}

.alert-heading {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.75rem;
    color: inherit;
}

.alert ul {
    margin-bottom: 0;
    padding-left: 1.25rem;
}

.alert li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
    font-size: 0.9rem;
}

.alert li:last-child {
    margin-bottom: 0;
}

/* 输入组样式 */
.input-group {
    border-radius: 0;
    overflow: hidden;
}

.input-group .form-control {
    border-right: none;
    border-radius: 0;
}

.input-group .btn {
    border-radius: 0;
    border-left: none;
}

/* 模态框样式 */
.modal-content {
    border-radius: 0;
    border: 1px solid var(--border-color);
    box-shadow: none;
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    padding: 2rem;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    padding: 1.5rem 2rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .settings-container {
        padding: 1rem;
        max-width: 100%;
    }
    
    .page-header {
        text-align: center;
        margin-bottom: 1.5rem;
    }
    
    .page-header h1 {
        font-size: 1.75rem;
    }
    
    .settings-section-title {
        font-size: 1.25rem;
        text-align: center;
    }
    
    .settings-card .card-header {
        padding: 1rem;
    }
    
    .settings-card .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
    
    .alert {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .form-control {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
}

/* 同步状态区域样式已移除，使用简洁布局 */

/* 模态框样式增强 */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    border-radius: 12px 12px 0 0;
    padding: 1.25rem 1.5rem;
}

.modal-title {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1.125rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 12px 12px;
    padding: 1rem 1.5rem;
}

.modal-footer .btn {
    margin-left: 0.5rem;
}

/* Alert 组件样式增强 */
.alert {
    border-radius: 8px;
    border: none;
    padding: 1rem 1.25rem;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

.alert-heading {
    color: inherit;
    font-weight: 600;
    margin-bottom: 0.5rem;
}
