/**
 * 知识库空间管理页面样式
 */

/* 页面整体布局 */
.container-fluid {
    max-width: 1400px;
}

/* 页面标题区域 */
.page-header {
    border-bottom: 1px solid var(--bs-border-color);
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.page-header h2 {
    color: var(--bs-primary);
    font-weight: 600;
}

/* 空间卡片样式 */
.space-card {
    transition: all 0.3s ease;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.75rem;
    overflow: hidden;
}

.space-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-color: var(--bs-primary);
}

.space-card .card-header {
    background-color: #007bff;
    color: white;
    border-bottom: none;
    padding: 1rem 1.25rem;
}

.space-card .card-header h5 {
    margin: 0;
    font-weight: 600;
}

.space-card .card-body {
    padding: 1.25rem;
}

/* 空间类型标签 */
.space-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
}

.space-type-public {
    background-color: var(--bs-success);
    color: white;
}

.space-type-private {
    background-color: var(--bs-warning);
    color: white;
}

.space-type-role-based {
    background-color: var(--bs-info);
    color: white;
}

/* 移除了空间分类相关样式 */

/* 空间操作按钮 */
.space-actions .dropdown-toggle {
    border: none;
    background: transparent;
    color: var(--bs-secondary);
    padding: 0.25rem 0.5rem;
}

.space-actions .dropdown-toggle:hover {
    color: var(--bs-primary);
    background-color: var(--bs-light);
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--bs-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h5 {
    color: var(--bs-secondary);
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: var(--bs-muted);
    margin-bottom: 1.5rem;
}

/* 模态框样式 */
.modal-header {
    background-color: var(--bs-light);
    border-bottom: 1px solid var(--bs-border-color);
}

.modal-title {
    color: var(--bs-primary);
    font-weight: 600;
}

/* 角色权限配置样式 */
.role-permission-item {
    background-color: var(--bs-light);
    border: 1px solid var(--bs-border-color) !important;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.role-permission-item:hover {
    border-color: var(--bs-primary) !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.role-permission-item .btn-remove {
    color: var(--bs-danger);
    background: none;
    border: none;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.role-permission-item .btn-remove:hover {
    background-color: var(--bs-danger);
    color: white;
}

/* 表单样式增强 */
.form-label {
    font-weight: 500;
    color: var(--bs-dark);
    margin-bottom: 0.5rem;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

/* 按钮样式 - 使用项目标准蓝色 */
.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 123, 255, 0.25);
}

.btn-outline-primary:hover {
    transform: translateY(-1px);
}

/* 搜索框样式 */
.search-container {
    position: relative;
}

.search-container .form-control {
    padding-left: 2.5rem;
}

.search-container::before {
    content: '\F52A';
    font-family: 'bootstrap-icons';
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--bs-secondary);
    z-index: 10;
}

/* 加载状态 */
.loading-state {
    text-align: center;
    padding: 2rem;
    color: var(--bs-secondary);
}

.loading-state .spinner-border {
    margin-bottom: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .page-header {
        text-align: center;
        margin-bottom: 1.5rem;
    }
    
    .page-header .btn {
        margin-top: 1rem;
        width: 100%;
    }
    
    .space-card {
        margin-bottom: 1rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .role-permission-item .row {
        margin: 0;
    }
    
    .role-permission-item .col-md-6 {
        padding: 0.25rem;
        margin-bottom: 0.5rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.space-card {
    animation: fadeIn 0.5s ease-out;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--bs-dark);
    color: white;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
}

/* 下拉菜单样式 */
.dropdown-menu {
    border: 1px solid var(--bs-border-color);
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: var(--bs-primary);
    color: white;
}

.dropdown-item.text-danger:hover {
    background-color: var(--bs-danger);
    color: white;
}
