/* 登录页面样式 - EdgeMind设计系统 */

/* 页面基础样式 */
.login-body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* 登录容器 */
.login-container {
    position: relative;
    z-index: 100;
    width: 100%;
    max-width: 550px;
    padding: 2rem;
}

/* 背景装饰 */
.login-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;
    pointer-events: none; /* 确保背景不会阻挡点击事件 */
}

.bg-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* 登录卡片 */
.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 3rem 2.5rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideUp 0.6s ease-out;
    position: relative;
    z-index: 10;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 登录头部 */
.login-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.logo-container {
    margin-bottom: 1.5rem;
}

.logo-container img {
    filter: drop-shadow(0 4px 8px rgba(66, 133, 244, 0.3));
    animation: logoGlow 2s ease-in-out infinite alternate;
}

@keyframes logoGlow {
    from { filter: drop-shadow(0 4px 8px rgba(66, 133, 244, 0.3)); }
    to { filter: drop-shadow(0 6px 12px rgba(66, 133, 244, 0.5)); }
}

.login-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color, #4285f4);
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #4285f4, #667eea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.login-subtitle {
    color: #6c757d;
    font-size: 1rem;
    margin: 0;
}

/* 表单样式 */
.login-form-container {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: var(--text-primary, #2c3e50);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.input-group {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.input-group:focus-within {
    box-shadow: 0 4px 16px rgba(66, 133, 244, 0.2);
    transform: translateY(-2px);
}

.input-group-text {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: none;
    color: var(--primary-color, #4285f4);
    font-size: 1.1rem;
    padding: 0.75rem 1rem;
}

.form-control {
    border: none;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.form-control:focus {
    background-color: #ffffff;
    box-shadow: none;
    border-color: transparent;
}

.password-toggle {
    border: none;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    color: var(--primary-color, #4285f4);
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.password-toggle:hover {
    background: linear-gradient(135deg, #e9ecef, #dee2e6);
    color: var(--primary-dark, #3367d6);
}

/* 复选框样式 */
.form-check-container {
    margin-bottom: 1rem;
}

.form-check-input:checked {
    background-color: var(--primary-color, #4285f4);
    border-color: var(--primary-color, #4285f4);
}

.form-check-label {
    color: #6c757d;
    font-size: 0.9rem;
}

/* 错误信息样式 */
.alert-danger {
    border-radius: 12px;
    border: none;
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
    font-size: 0.9rem;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 登录按钮 */
.btn-login {
    width: 100%;
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-color, #4285f4), #667eea);
    border: none;
    color: white;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-login::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-login:hover::before {
    left: 100%;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(66, 133, 244, 0.4);
}

.btn-login:active {
    transform: translateY(0);
}

.btn-login:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* 页脚样式 */
.login-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.login-footer p {
    font-size: 0.85rem;
    color: #6c757d;
    margin: 0;
}

.login-footer a {
    color: var(--primary-color, #4285f4);
    text-decoration: none;
    transition: color 0.3s ease;
}

.login-footer a:hover {
    color: var(--primary-dark, #3367d6);
    text-decoration: underline;
}

/* 版权信息 */
.copyright {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    z-index: 10;
}

.copyright p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.8rem;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .login-container {
        max-width: 480px;
    }
}

@media (max-width: 768px) {
    .login-container {
        padding: 1rem;
        max-width: 420px;
    }

    .login-card {
        padding: 2rem 1.5rem;
        border-radius: 16px;
    }

    .login-title {
        font-size: 1.75rem;
    }

    .bg-shape {
        display: none;
    }
}

@media (max-width: 480px) {
    .login-container {
        max-width: 100%;
    }

    .login-card {
        padding: 1.5rem 1rem;
    }

    .login-title {
        font-size: 1.5rem;
    }
}
