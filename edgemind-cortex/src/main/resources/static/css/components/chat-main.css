
/* 直接应用到所有代码块的滚动条样式 */
pre {
    overflow: auto !important;
}
pre code {
    white-space: pre !important;
}
pre::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
}
pre::-webkit-scrollbar-track {
    background: #f5f5f5 !important;
    border-radius: 4px !important;
}
pre::-webkit-scrollbar-thumb {
    background: #c0c0c0 !important;
    border-radius: 4px !important;
    border: 2px solid #f5f5f5 !important;
    background-clip: padding-box !important;
}
pre::-webkit-scrollbar-thumb:hover {
    background: #a0a0a0 !important;
}

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    background: #ffffff;
}

/* 确保代码块滚动条美化生效 - 简单直接版 */
pre {
    overflow: auto !important;
}

pre code {
    white-space: pre !important;
}

pre::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
}

pre:hover::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
}

pre:hover::-webkit-scrollbar-thumb {
    background: rgba(66, 133, 244, 0.6) !important;
    border-radius: 4px !important;
}

.chat-main-layout {
    display: flex;
    flex-direction: row;
    height: 100vh;
    background: none;
    width: 100%; /* 使用全宽，确保历史栏贴着左侧导航栏 */
    margin: 0; /* 移除居中样式 */
    box-shadow: none; /* 移除阴影 */
}

.chat-history-panel {
    width: 260px;
    background: #f5f6fa;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    min-width: 220px;
    max-width: 340px;
    box-shadow: none;
}

.new-chat-button {
    display: flex;
    align-items: center;
    justify-content: center; /* 添加水平居中 */
    padding: 10px 16px;
    margin: 20px 8px;
    background-color: #4285f4;
    color: white;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
    flex-shrink: 0; /* 确保按钮不被压缩 */
}

.new-chat-button:hover {
    background-color: #3367d6;
}

.new-chat-button i {
    margin-right: 8px;
}

.conversations-list {
    flex-grow: 1;
    overflow-y: auto;
    padding: 8px 0;
}

.conversation-item {
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 6px;
    margin: 4px 8px;
    transition: background 0.15s, color 0.15s;
    color: #333;
    display: flex;
    align-items: center;
    font-size: 15px;
}

.conversation-item.active {
    background: #e3f0ff;
    font-weight: 600;
    color: #1976d2;
}

.conversation-item:hover {
    background: #e9ecef;
}

.chat-main-page {
    flex: 1 1 0%;
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: #ffffff;
}

.chat-messages {
    flex: 1 1 0%;
    overflow-y: auto;
    background: #ffffff;
    padding: 24px 32px 0 32px;
    max-width: 900px; /* 限制消息区域最大宽度，提高可读性 */
    margin: 0 auto; /* 水平居中 */
    width: 100%; /* 确保占满可用空间 */
}

.chat-input-area {
    background: #fff;
    border-top: none; /* 移除上方的线 */
    box-shadow: none; /* 移除阴影 */
    padding-bottom: 8px;
}

.chat-input-content {
    max-width: 900px; /* 与消息区域保持一致的最大宽度 */
    margin: 0 auto; /* 水平居中 */
    padding: 0 32px; /* 与消息区域保持一致的内边距 */
}

.model-selector-container {
    margin-bottom: 0px;
}

/* 响应式布局调整 */
@media (max-width: 1300px) {
    .chat-main-layout {
        max-width: 90%; /* 在较大屏幕上使用百分比宽度 */
    }
}

@media (max-width: 900px) {
    .chat-main-layout {
        flex-direction: column;
        max-width: 95%; /* 在平板和手机上增加宽度利用率 */
    }

    .chat-history-panel {
        width: 100%;
        max-width: none;
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
    }

    .chat-main-page {
        height: auto;
    }
}

@media (max-width: 576px) {
    .chat-main-layout {
        max-width: 100%; /* 在手机上占满全屏 */
        margin: 0; /* 移除边距 */
        box-shadow: none; /* 移除阴影 */
    }
}

/* 输入区域整体容器样式 */
.chat-input-box {
    border: 1px solid #dee2e6; /* 边框 */
    border-radius: 0.375rem; /* 圆角 (Bootstrap default) */
    padding: 0.75rem 1rem; /* 内边距 */
    background-color: #fff; /* 背景色 */
    display: flex;
    flex-direction: column; /* 垂直排列内部元素 */
}

/* 移除 textarea 默认边框，可能添加底部边框作为分隔 */
.chat-input-box .chat-textarea {
    border: none; /* 移除边框 */
    box-shadow: none; /* 移除阴影 */
    padding: 0; /* 移除内边距，由父容器控制 */
    resize: none;
    overflow-y: hidden;
    min-height: 38px; /* 初始高度 */
    max-height: 150px; /* 最大高度 */
    line-height: 1.5;
}

.chat-input-box .chat-textarea:focus {
    box-shadow: none; /* 聚焦时也移除阴影 */
    border: none;
}

/* 底部工具栏 */
.input-toolbar {
    padding-top: 0.5rem; /* 与上方内容的间距 */
    /* 可以添加一个上边框作为视觉分隔 */
    /* border-top: 1px solid #e9ecef; */
    /* margin-top: 0.5rem; */ /* 如果使用边框，可以移除mt-2类改为margin */
}


/* 图标按钮样式 (如果需要更小的圆形按钮) */
.btn-icon {
    width: 32px; /* 固定宽度 */
    height: 32px; /* 固定高度 */
    padding: 0; /* 移除内边距 */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%; /* 圆形 */
}

/* 调整发送按钮的大小，如果需要 */
#send-button.btn-icon {
    /* 可以稍微大一点 */
    /* width: 36px; */
    /* height: 36px; */
}

/* 移除之前输入区域的特定样式 */
.chat-input-area .input-wrapper textarea { /* 删除之前的样式 */
    /* styles to remove or modify */
    /* padding-right: 10px; */
    /* padding-left: 10px; */
    /* min-height: 38px; */
    /* ... */
}

.chat-input-area .input-wrapper .btn { /* 删除之前的样式 */
    /* styles to remove or modify */
    /* height: 38px; */
    /* ... */
}

/* 图片预览样式 (保持或调整) */
#image-preview-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    /* padding-top: 4px; */ /* 调整或移除 */
}

.image-preview-item {
    position: relative;
    display: inline-block;
    /* margin-right: 8px; */ /* 调整或移除 */
}

.image-preview-item img {
    max-height: 60px;
    max-width: 100px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.remove-image-btn {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    border: none;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 12px;
    line-height: 16px;
    text-align: center;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 模型选择器容器和下拉框样式 */
.model-selector-container {
    /* 移除 flex-grow-0 以便在需要时可以稍微拉伸，但限制最大宽度 */
    max-width: 240px; /* 增加最大宽度以显示完整模型名称 */
    flex-shrink: 1; /* 允许收缩 */
}

#model-select.form-select-sm {
    padding-top: 0.25rem; /* 微调垂直内边距 */
    padding-bottom: 0.25rem;
    padding-left: 0.5rem; /* 左侧内边距 */
    font-size: 0.875rem; /* 保持字体大小 */
    line-height: 1.5;
    height: calc(1.5em + 0.5rem + 2px); /* 与 sm 按钮高度类似 */
    /* 移除自定义样式，恢复 Bootstrap 默认 */
    /* background-color: #f8f9fa; */ /* Removed */
    /* border: 1px solid #ced4da; */ /* Removed */
    /* border-radius: 0.25rem; */ /* Removed */
}

#model-select.form-select-sm:focus {
    /* 保留 Bootstrap 默认聚焦效果 */
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25); /* Bootstrap 默认聚焦效果 */
}

/* Override bootstrap-select button styles */
.bootstrap-select > .btn.bs-placeholder,
.bootstrap-select > .btn {
    background-color: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0.25rem 0.5rem !important; /* 恢复一些内边距，但比默认小 */
    margin: 0; /* 确保没有外边距 */
    color: var(--bs-body-color) !important; /* 使用 CSS 变量确保颜色一致 */
    /* 尝试匹配旁边的 sm 按钮高度 */
    height: calc(1.5em + 0.5rem + 2px);
    line-height: 1.5; /* 尝试与高度匹配的行高 */
    display: inline-flex; /* 使用 flex 垂直居中 */
    align-items: center;
    width: 100%; /* 占据容器宽度 */
    max-width: 100%; /* 确保不超过容器 */
    text-align: left; /* 文本左对齐 */
}

/* 去掉点击时的轮廓 */
.bootstrap-select > .btn.bs-placeholder:focus,
.bootstrap-select > .btn:focus {
    outline: none !important;
    box-shadow: none !important; /* 再次确保没有阴影 */
}

/* 调整内部文本容器的样式 */
.bootstrap-select > .btn .filter-option {
    /* 可能需要设置 display: inline-block 或调整对齐 */
    text-align: left;
    flex-grow: 1; /* 让文本区域占据可用空间 */
    overflow: hidden; /* 防止文本溢出 */
    text-overflow: ellipsis; /* 溢出时显示省略号 */
    white-space: nowrap; /* 防止文本换行 */
    padding-right: 5px; /* 给箭头留出空间 */
}

/* 调整箭头样式 */
.bootstrap-select > .btn .bs-caret {
    /* 可能需要调整位置或大小 */
    margin-left: auto; /* 将箭头推到右边 */
}

/* Keep bootstrap-select active item blue on hover */
.bootstrap-select .dropdown-menu li.selected a:hover,
.bootstrap-select .dropdown-menu .dropdown-item.active:hover,
.bootstrap-select .dropdown-menu li a.active:hover { /* Check different structures */
    background-color: #0d6efd !important; /* Bootstrap primary blue */
    color: white !important;
}

/* --- Markdown Content Styling --- */

/* Base styling within messages */
.chat-messages .message {
    line-height: 1.6; /* Improve readability */
    font-size: 1rem; /* Base font size */
    color: #212529; /* Default text color */
}

/* Paragraphs */
.chat-messages .message p {
    margin-bottom: 1rem; /* Space below paragraphs */
}

.chat-messages .message p:last-child {
    margin-bottom: 0; /* No space after the last element in a message */
}

/* Headings */
.chat-messages .message h1,
.chat-messages .message h2,
.chat-messages .message h3,
.chat-messages .message h4,
.chat-messages .message h5,
.chat-messages .message h6 {
    margin-top: 1.5rem; /* Space above headings */
    margin-bottom: 1rem; /* Space below headings */
    font-weight: 600; /* Bootstrap heading weight */
    line-height: 1.3;
}

.chat-messages .message h1 {
    font-size: 1.6rem;
}

.chat-messages .message h2 {
    font-size: 1.4rem;
}

.chat-messages .message h3 {
    font-size: 1.25rem;
}

.chat-messages .message h4 {
    font-size: 1.1rem;
}

.chat-messages .message h5 {
    font-size: 1rem;
}

.chat-messages .message h6 {
    font-size: 0.9rem;
}

/* Lists */
.chat-messages .message ul,
.chat-messages .message ol {
    margin-bottom: 1rem;
    padding-left: 1.8rem; /* Indentation */
}

.chat-messages .message li {
    margin-bottom: 0.4rem; /* Space between list items */
}

/* Blockquotes */
.chat-messages .message blockquote {
    margin: 0 0 1rem 0;
    padding: 0.5rem 1rem;
    border-left: 0.25rem solid #e9ecef; /* Subtle left border */
    background-color: #f8f9fa; /* Light background */
    color: #6c757d; /* Muted text color */
}

.chat-messages .message blockquote p:last-child {
    margin-bottom: 0;
}

/* Code - Inline */
.chat-messages .message :not(pre) > code {
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    background-color: rgba(27, 31, 35, 0.05); /* Subtle background like GitHub */
    border-radius: 3px;
    font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

/* Code - Block (pre > code) - Rely on highlight.js styles but add margin */
.chat-messages .message pre {
    margin-bottom: 1rem;
    padding: 1rem;
    font-size: 0.9rem;
    border-radius: 6px;
    /* background is handled by highlight.js theme (github.min.css) */
}

/* Tables */
.chat-messages .message table {
    width: auto; /* Let table size itself, or use 100% */
    max-width: 100%; /* Prevent overflow */
    margin-bottom: 1rem;
    border-collapse: collapse;
    border: 1px solid #dee2e6; /* Bootstrap table border color */
}

.chat-messages .message th,
.chat-messages .message td {
    padding: 0.6rem 0.8rem; /* Padding */
    border: 1px solid #dee2e6;
    text-align: left;
}

.chat-messages .message thead th {
    font-weight: 600;
    background-color: #f8f9fa; /* Light header background */
    border-bottom-width: 2px; /* Thicker bottom border for header */
}

/* Basic striping - optional */
.chat-messages .message tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.03);
}

/* Links */
.chat-messages .message a {
    color: #0d6efd; /* Bootstrap primary link color */
    text-decoration: underline;
}

.chat-messages .message a:hover {
    color: #0a58ca;
}

/* Horizontal Rule */
.chat-messages .message hr {
    margin: 1.5rem 0;
    border: 0;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Ensure images are responsive */
.chat-messages .message img {
    max-width: 100%;
    height: auto;
    margin-bottom: 1rem;
}

/* 发送/暂停按钮样式 */
#send-button, #pause-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
    transition: all 0.2s ease;
}

#pause-button {
    background-color: #4285f4; /* 从红色 #dc3545 改为蓝色 #4285f4 */
    border-color: #4285f4; /* 边框颜色也相应修改 */
}

#send-button i, #pause-button i {
    font-size: 1.2rem;
    color: white;
}

/* 思考过程样式简化 */
details.thinking-details {
    margin-bottom: 10px;
}

details.thinking-details summary {
    cursor: pointer;
    color: #6c757d;
    font-style: italic;
}

.thinking-content {
    font-style: italic;
    color: #6c757d;
    white-space: pre-wrap;
    margin-top: 5px;
    font-size: 0.85rem;
}