/* 侧边栏组件独立样式 */
:root {
    --sidebar-width-expanded: 180px;
    --sidebar-width-collapsed: 60px;
    --sidebar-transition-duration: 0.3s;
    --sidebar-nav-link-padding-y: 0.6rem;
    --sidebar-nav-link-padding-x: 0.8rem;
    --sidebar-icon-size: 1.0rem;
    --sidebar-icon-width: 20px; /* 固定图标宽度以对齐 */
}

/* 侧边栏基础样式 */
.sidebar {
    width: var(--sidebar-width-expanded);
    height: 100vh;
    background-color: var(--sidebar-bg, #ffffff);
    border-right: 1px solid var(--sidebar-border, #e6f0ff);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: width var(--sidebar-transition-duration) ease;
    position: fixed; /* 使用固定定位确保侧边栏不随主内容移动 */
    left: 0;
    top: 0;
    z-index: 1030; /* 确保侧边栏在主内容上层 */
    box-shadow: 2px 0 16px 0 rgba(60, 80, 120, 0.08);
    border-radius: 10px 0 0 10px;
}

/* 使用初始变量的情况 */
.sidebar:not(.collapsed) {
    width: var(--initial-sidebar-width, var(--sidebar-width-expanded));
}

/* 折叠状态样式 */
.sidebar.collapsed {
    width: var(--sidebar-width-collapsed);
}

/* 初始折叠状态样式 */
html.sidebar-initially-collapsed .sidebar {
    width: var(--sidebar-width-collapsed);
}

html.sidebar-initially-collapsed .sidebar .nav-link-text,
html.sidebar-initially-collapsed .sidebar .toggle-text {
    display: none;
}

html.sidebar-initially-collapsed .sidebar .nav-link {
    justify-content: center;
    padding-left: 0;
    padding-right: 0;
}

/* 主导航菜单样式 */
.main-nav {
    min-height: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    flex: 1 1 0%;
    gap: 0.25rem;
    /* 美化滚动条 */
    scrollbar-width: thin;
    scrollbar-color: var(--border-color, #e6f0ff) var(--sidebar-bg, #ffffff);
}
.main-nav::-webkit-scrollbar { width: 6px; }
.main-nav::-webkit-scrollbar-track { background: var(--sidebar-bg, #ffffff); }
.main-nav::-webkit-scrollbar-thumb { 
    background-color: var(--border-color, #e6f0ff); 
    border-radius: 3px; 
}
.main-nav::-webkit-scrollbar-thumb:hover { 
    background-color: var(--primary-color, #4285f4);
    opacity: 0.7; 
}

/* 导航项目基础样式 */
.nav-item {
    margin-bottom: 0.25rem;
    flex-shrink: 0;
}

/* 导航容器样式 */
.nav-link-container {
    margin: 0 0.5rem;
    padding-left: 0.05rem;
    border-radius: 6px;
    transition: background-color 0.2s;
    position: relative;
}

/* 导航链接样式 */
.nav-link-container a.nav-link {
    padding: var(--sidebar-nav-link-padding-y) 0 var(--sidebar-nav-link-padding-y) 0.3rem;
    margin: 0;
    border-radius: 0;
    background-color: transparent !important;
    flex-grow: 1;
    color: var(--text-primary, #2c3e50); /* 默认链接颜色 */
    display: flex; /* 确保使用flexbox布局 */
    align-items: center; /* 垂直居中 */
}
.nav-link-container a.nav-link:hover {
    color: var(--primary-color, #4285f4); /* 悬停时文字变蓝 */
}

/* 导航节点的选中状态 - 满蓝色背景 */
.nav-link-container.active,
.nav-link-container a.nav-link.active {
    background: var(--primary-color, #4285f4) !important;
    color: #ffffff !important;
    font-weight: 500 !important;
    border-radius: 6px !important;
}

/* 导航节点选中时图标也要变成白色（反色） */
.nav-link-container.active i,
.nav-link-container a.nav-link.active i {
    color: #ffffff !important;
}

/* 所有侧边栏图标的标准化样式 */
.sidebar a.nav-link i.bi,
.sidebar .conversation-item i.bi,
.sidebar .collapse-toggle-icon .collapse-icon {
    width: var(--sidebar-icon-width) !important;
    height: var(--sidebar-icon-width) !important;
    min-width: var(--sidebar-icon-width) !important;
    min-height: var(--sidebar-icon-width) !important;
    max-width: var(--sidebar-icon-width) !important;
    max-height: var(--sidebar-icon-width) !important;
    line-height: var(--sidebar-icon-width) !important;
    display: inline-flex !important;
    align-items: center;
    justify-content: center;
    border-radius: 50% !important; /* 圆形图标 */
    font-size: var(--sidebar-icon-size) !important;
    vertical-align: middle;
    background: none !important;
    box-sizing: content-box;
    padding: 0 !important;
    margin-right: 0.5rem !important; /* 确保图标与文本有间距 */
    position: relative; /* 添加相对定位 */
    top: -1px; /* 微调垂直位置 */
}

/* 导航文本样式 */
.nav-link-text {
    opacity: 1;
    transition: opacity var(--sidebar-transition-duration) ease;
    flex-grow: 1; /* 让文本占据剩余空间 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 折叠指示图标样式 */
.collapse-toggle-icon {
    color: var(--text-secondary, #5f6368);
    cursor: pointer;
    text-decoration: none;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    transition: background-color 0.2s, color 0.2s, opacity 0.2s;
}
.collapse-toggle-icon:hover {
    background-color: var(--hover-bg, #f5f8ff);
    color: var(--primary-color, #4285f4);
}

/* 折叠项展开时图标旋转 */
.collapse-toggle-icon[aria-expanded="true"] .collapse-icon {
    transform: rotate(180deg);
}

/* Logo区域样式 */
.sidebar-logo {
    display: flex;
    align-items: center;
    transition: all var(--sidebar-transition-duration) ease;
    color: var(--primary-color, #4285f4);
    opacity: 1;
}

.sidebar-logo img {
    flex-shrink: 0;
}

.logo-text {
    font-size: 1.1rem;
    color: var(--primary-color, #4285f4);
    white-space: nowrap;
    overflow: hidden;
    transition: opacity var(--sidebar-transition-duration) ease;
}

/* Logo淡入动画 */
@keyframes logoFadeIn {
    from {
        opacity: 0;
        transform: translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 底部切换按钮 */
.sidebar-footer {
    border-top: 1px solid var(--border-color, #e6f0ff);
    flex-shrink: 0;
    margin-top: auto;
}
#sidebar-toggle {
    color: var(--text-secondary, #5f6368);
    border-color: var(--border-color, #e6f0ff);
    background-color: transparent;
    transition: all 0.2s ease;
}
#sidebar-toggle:hover {
    color: #ffffff;
    border-color: var(--primary-color, #4285f4);
    background-color: var(--primary-color, #4285f4);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(66, 133, 244, 0.3);
}
#sidebar-toggle:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(66, 133, 244, 0.2);
}
#sidebar-toggle span.toggle-text {
    opacity: 1;
    transition: opacity var(--sidebar-transition-duration) ease;
}
#sidebar-toggle i {
    transition: transform var(--sidebar-transition-duration) ease;
}

/* 折叠状态下的样式调整 */
.sidebar.collapsed .sidebar-logo {
    justify-content: center;
    opacity: 1;
    animation: none;
}

.sidebar.collapsed .logo-text,
.sidebar.collapsed .nav-link-text,
.sidebar.collapsed .toggle-text,
.sidebar.collapsed .collapse-icon {
    opacity: 0;
    visibility: hidden;
    width: 0;
    display: none !important;
}

/* 初始化时禁用过渡效果 */
.app-container.no-transition .sidebar-logo {
    transition: none !important;
    animation: none !important;
    opacity: 1 !important;
}

.sidebar.collapsed .nav-link {
    padding: var(--sidebar-nav-link-padding-y) 0;
    position: relative; /* 添加相对定位 */
    display: block; /* 改为block，因为内部图标绝对定位了 */
    height: calc(var(--sidebar-icon-width) + 2 * var(--sidebar-nav-link-padding-y)); /* 确保链接有高度 */
}

.sidebar.collapsed .nav-link > i.bi {
    margin: 0 !important; 
    position: absolute; /* 使用绝对定位 */
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%); /* 居中图标 */
}

.sidebar.collapsed #sidebar-toggle i {
    transform: rotate(180deg);
}

/* 收起状态下的子菜单显示 */
.sidebar.collapsed .collapse {
    display: none !important;
    position: static !important;
    padding-left: 0 !important;
    margin-top: 0.25rem;
}

/* 当子菜单被激活显示时（立即显示，无动画） */
.sidebar.collapsed .collapse.show {
    display: block !important;
    animation: none !important;
    transition: none !important;
}

/* 收起状态下的子菜单项样式 */
.sidebar.collapsed .collapse .nav-item {
    margin-bottom: 0.25rem;
}

.sidebar.collapsed .collapse .nav-link {
    padding: 0.5rem;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;
    background: transparent !important;
    transition: background-color 0.15s ease;
}

.sidebar.collapsed .collapse .nav-link:hover {
    background: var(--nav-link-hover-bg, #f8f9fa) !important;
    /* 移除transform动画，保持图标不动 */
}

/* 收起状态下只显示图标 */
.sidebar.collapsed .collapse .nav-link .nav-link-text {
    display: none;
}

.sidebar.collapsed .collapse .nav-link i {
    font-size: 1.1rem;
    margin: 0;
    color: var(--primary-color, #4285f4) !important;
}

/* 确保子菜单项背景色正确显示 */
.sidebar.collapsed .collapse.show .nav-link {
    background: transparent !important;
    border: none !important;
    border-radius: 6px;
    transition: all 0.15s ease;
    position: relative;
}

.sidebar.collapsed .collapse.show .nav-link:hover {
    background: var(--nav-link-hover-bg, #f8f9fa) !important;
}

/* 子节点点击时的选中状态 - 满蓝色背景与导航节点一致 */
.sidebar.collapsed .collapse.show .nav-link.active {
    background: var(--primary-color, #4285f4) !important;
    color: #ffffff !important;
    font-weight: 500 !important;
    border: none !important;
    border-radius: 6px !important;
}

/* 子节点选中时图标也要变成白色（反色） */
.sidebar.collapsed .collapse.show .nav-link.active i {
    color: #ffffff !important;
}

/* 折叠图标控制 */
.sidebar.collapsed .collapse-icon {
    display: none !important;
}

.sidebar:not(.collapsed) .d-sidebar-expanded-inline {
    display: inline !important;
}

/* 收起状态下父菜单项（如权限管理）的样式控制 */
.sidebar.collapsed [data-bs-toggle="collapse"] {
    background: transparent !important;
    border: none !important;
    transition: background-color 0.15s ease !important;
    color: var(--primary-color, #4285f4) !important; /* 图标和文字都是蓝色 */
}

.sidebar.collapsed [data-bs-toggle="collapse"]:hover {
    background: var(--nav-link-hover-bg, #f8f9fa) !important;
    border: none !important;
    color: var(--primary-color, #4285f4) !important;
}

/* 确保收起状态下父菜单项的图标是蓝色 */
.sidebar.collapsed [data-bs-toggle="collapse"] i {
    color: var(--primary-color, #4285f4) !important;
}

/* 完全防止父菜单项的任何选中效果 */
.sidebar.collapsed [data-bs-toggle="collapse"]:active,
.sidebar.collapsed [data-bs-toggle="collapse"]:focus,
.sidebar.collapsed [data-bs-toggle="collapse"]:focus-visible,
.sidebar.collapsed [data-bs-toggle="collapse"].show,
.sidebar.collapsed [data-bs-toggle="collapse"][aria-expanded="true"] {
    background: transparent !important;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    color: inherit !important;
    font-weight: normal !important;
}

/* 自定义tooltip样式 */
.custom-tooltip {
    position: fixed;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
    font-size: 0.875rem;
    white-space: nowrap;
    z-index: 1050;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    pointer-events: none;
}

/* 收起状态下子菜单项的悬停效果（无动画） */
.sidebar.collapsed .collapse .nav-link:hover {
    background: var(--nav-link-hover-bg, #f8f9fa) !important;
    /* 移除transform和box-shadow，保持图标静止 */
}

/* 折叠状态下调整导航容器样式 */
.sidebar.collapsed .nav-link-container {
    margin: 0.1rem 0.25rem; /* 微调边距 */
    justify-content: center;
    padding: var(--sidebar-nav-link-padding-y) calc((var(--sidebar-width-collapsed) - var(--sidebar-icon-width)) / 2);
}

/* 对话列表容器样式 */
.conversations-list-container {
    flex-grow: 1;
    overflow-y: auto;
    max-height: 60vh;
    min-height: 60px;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 0 0.5rem;
    /* 美化滚动条 */
    scrollbar-width: thin;
    scrollbar-color: var(--border-color, #e6f0ff) transparent;
}
.conversations-list-container::-webkit-scrollbar { width: 6px; }
.conversations-list-container::-webkit-scrollbar-track { background: transparent; border-radius: 8px; }
.conversations-list-container::-webkit-scrollbar-thumb { 
    background-color: var(--border-color, #e6f0ff);
    border-radius: 3px;
    transition: background-color 0.3s;
}
.conversations-list-container::-webkit-scrollbar-thumb:hover { 
    background-color: var(--primary-color, #4285f4);
    opacity: 0.7;
}

/* 对话列表项样式 */
.conversation-item {
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    margin: 0 0.25rem 0.25rem 0.25rem;
    color: var(--text-primary, #2c3e50);
    transition: all 0.2s ease;
    overflow: hidden;
    white-space: nowrap;
    position: relative;
    border-left: 3px solid transparent;
}

.conversation-item:hover {
    background-color: var(--hover-bg, #f5f8ff);
    color: var(--primary-color, #4285f4);
    transform: translateX(2px);
}

.conversation-item.active {
    background-color: var(--active-bg, #e8f0fe);
    color: var(--primary-color, #4285f4);
    font-weight: 500;
    border-left: 3px solid var(--primary-color, #4285f4);
}

.conversation-title {
    opacity: 1;
    transition: opacity var(--sidebar-transition-duration) ease;
    font-size: 0.85rem;
}

/* 响应式样式 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: calc(-1 * var(--sidebar-width-expanded));
        z-index: 1030;
        transition: left 0.3s ease;
        width: var(--sidebar-width-expanded);
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .sidebar.collapsed {
        width: var(--sidebar-width-expanded);
    }
    
    .sidebar.collapsed .nav-link-text,
    .sidebar.collapsed .collapse-toggle-icon,
    .sidebar.collapsed .toggle-text,
    .sidebar.collapsed .logo-text {
        opacity: 1;
        visibility: visible;
        width: auto;
        display: inline;
    }
    
    .sidebar.collapsed .nav-link {
        justify-content: flex-start;
        padding: var(--sidebar-nav-link-padding-y) 0 var(--sidebar-nav-link-padding-y) var(--sidebar-nav-link-padding-x);
    }
    
    .sidebar.collapsed .bi {
        margin-right: 0.5rem !important;
    }
    
    #sidebar-toggle {
        display: none; /* 移动端隐藏切换按钮 */
    }
}