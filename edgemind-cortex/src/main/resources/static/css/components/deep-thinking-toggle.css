/* 深度思考切换按钮样式 */
.deep-thinking-toggle {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6c757d;
    margin-right: 0;
}

.deep-thinking-toggle:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
    color: #495057;
}

.deep-thinking-toggle.active {
    background-color: #e3f2fd;
    border-color: #2196f3;
    color: #1976d2;
}

.deep-thinking-toggle.active:hover {
    background-color: #bbdefb;
    border-color: #1976d2;
}

.deep-thinking-toggle i {
    font-size: 16px;
    transition: transform 0.2s ease;
}

.deep-thinking-toggle.active i {
    transform: scale(1.1);
}

/* 思考中状态样式 - 仅添加视觉指示，不禁用按钮 */
.deep-thinking-toggle.thinking {
    animation: thinking-pulse 2s infinite;
}

/* 思考中且激活状态的样式 */
.deep-thinking-toggle.thinking.active {
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.3);
}

/* 深度思考指示器动画 */
@keyframes thinking-pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.4);
    }
    50% {
        box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.2);
    }
}

/* Tooltip 样式 */
.deep-thinking-toggle[data-bs-toggle="tooltip"] {
    position: relative;
}

/* 工具调用切换按钮样式 */
.tool-calling-toggle {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6c757d;
    margin-left: 6px;
}

.tool-calling-toggle:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
    color: #495057;
}

.tool-calling-toggle.active {
    background-color: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.tool-calling-toggle.active:hover {
    background-color: #ffeaa7;
    border-color: #ffb300;
}

.tool-calling-toggle i {
    font-size: 16px;
    transition: transform 0.2s ease;
}

.tool-calling-toggle.active i {
    transform: scale(1.1);
}

/* 处理中状态样式 */
.tool-calling-toggle.processing {
    animation: tool-processing-pulse 2s infinite;
}

/* 处理中且激活状态的样式 */
.tool-calling-toggle.processing.active {
    box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.3);
}

/* 工具调用处理指示器动画 */
@keyframes tool-processing-pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4);
    }
    50% {
        box-shadow: 0 0 0 4px rgba(255, 193, 7, 0.2);
    }
}

/* 响应式调整 */
@media (max-width: 576px) {
    .deep-thinking-toggle {
        width: 28px;
        height: 28px;
    }

    .deep-thinking-toggle i {
        font-size: 14px;
    }

    .tool-calling-toggle {
        width: 28px;
        height: 28px;
    }

    .tool-calling-toggle i {
        font-size: 14px;
    }
}