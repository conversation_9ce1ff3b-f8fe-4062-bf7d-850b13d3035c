/**
 * 开源文档查看器样式
 * 替代OnlyOffice，支持多种文档格式
 */

.document-viewer-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #fff;
    justify-content: center;
    align-items: center;
}

.document-viewer-content {
    flex: 1;
    overflow: hidden;
    position: relative;
    width: 100%;
    max-width: 1200px; /* 最大宽度限制，适合阅读 */
    height: 100%;
    margin: 0 auto;

    /* 响应式设计 */
    @media (max-width: 1400px) {
        max-width: 95%; /* 大屏幕时留出边距 */
    }

    @media (max-width: 768px) {
        max-width: 100%; /* 移动端全宽 */
    }
}

/* PDF查看器样式 */
.pdf-viewer {
    width: 100%;
    height: 100%;
    border: none;
}

.pdf-viewer-container {
    width: 100%;
    height: 100%;
    position: relative;
    background: #525659;
}

/* Office文档查看器样式 */
.office-viewer {
    width: 100%;
    height: 100%; /* 恢复固定高度，这是滚动容器 */
    padding: 20px;
    overflow: auto; /* 这里提供滚动功能 */
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: flex-start; /* 让内容顶部对齐，避免短文档居中显示时的空白 */
}

.office-content {
    max-width: 800px; /* 文档内容最大宽度，适合阅读 */
    width: 100%;
    margin: 0 auto;
    background: #fff;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-radius: 8px;
    min-height: auto; /* 改为auto，让高度根据内容自适应 */
    line-height: 1.6;
    font-family: 'Microsoft YaHei', Arial, sans-serif;

    /* 响应式设计 */
    @media (max-width: 1024px) {
        max-width: 90%;
        padding: 15px;
    }

    @media (max-width: 768px) {
        max-width: 100%;
        padding: 10px;
        box-shadow: none;
        border-radius: 0;
    }
}



/* Word表格样式 */
.word-document-content table,
.word-document-content .word-table {
    width: 100%;
    border-collapse: collapse;
    margin: 16px 0;
    font-size: 14px;
    border: 1px solid #000;
    table-layout: auto; /* 自动表格布局 */
    overflow-wrap: break-word; /* 长文本换行 */
}

.word-document-content table th,
.word-document-content table td,
.word-document-content .word-table th,
.word-document-content .word-table td {
    border: 1px solid #000;
    padding: 8px 12px;
    text-align: left;
    vertical-align: top;
    background: #fff;
    word-wrap: break-word; /* 强制换行 */
    max-width: 200px; /* 限制最大宽度 */
    min-width: 60px; /* 最小宽度 */
}

.word-document-content table th,
.word-document-content .word-table th {
    background-color: #f0f0f0;
    font-weight: bold;
}

/* 表格容器，支持水平滚动 */
.word-document-content table {
    display: table;
    overflow-x: auto;
    white-space: nowrap;
}

/* 响应式表格 */
@media (max-width: 1024px) {
    .word-document-content table,
    .word-document-content .word-table {
        font-size: 12px;
    }

    .word-document-content table th,
    .word-document-content table td,
    .word-document-content .word-table th,
    .word-document-content .word-table td {
        padding: 6px 8px;
        max-width: 150px;
        min-width: 50px;
    }
}

@media (max-width: 768px) {
    .word-document-content table,
    .word-document-content .word-table {
        font-size: 11px;
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }

    .word-document-content table th,
    .word-document-content table td,
    .word-document-content .word-table th,
    .word-document-content .word-table td {
        padding: 4px 6px;
        max-width: 120px;
        min-width: 40px;
    }
}

/* Word标题样式 */
.word-document-content h1.title {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    margin: 20px 0;
    color: #000;
}

.word-document-content h2.subtitle {
    text-align: center;
    font-size: 18px;
    font-weight: normal;
    margin: 16px 0;
    color: #333;
}

.word-document-content h1 {
    font-size: 20px;
    font-weight: bold;
    margin: 18px 0 12px 0;
    color: #000;
}

.word-document-content h2 {
    font-size: 18px;
    font-weight: bold;
    margin: 16px 0 10px 0;
    color: #000;
}

.word-document-content h3 {
    font-size: 16px;
    font-weight: bold;
    margin: 14px 0 8px 0;
    color: #000;
}

/* Word段落样式 */
.word-document-content p {
    margin: 8px 0;
    text-align: justify;
    text-indent: 2em; /* 中文段落首行缩进 */
}

/* Word列表样式 */
.word-document-content ul,
.word-document-content ol,
.word-document-content .word-list {
    margin: 12px 0;
    padding-left: 30px;
}

.word-document-content li {
    margin: 4px 0;
    line-height: 1.5;
}

/* Word强调样式 */
.word-document-content strong {
    font-weight: bold;
    color: #000;
}

.word-document-content em {
    font-style: italic;
}

/* Word链接样式 */
.word-document-content a {
    color: #0066cc;
    text-decoration: underline;
}

.word-document-content a:hover {
    color: #004499;
}

/* 代码编辑器样式 */
.code-editor-container {
    width: 100%;
    height: 100%;
    position: relative;
}

.monaco-editor {
    width: 100% !important;
    height: 100% !important;
}

/* Markdown查看器样式 */
.markdown-viewer {
    width: 100%;
    height: 100%;
    display: flex;
    max-width: 1200px;
    margin: 0 auto;

    /* 响应式设计 */
    @media (max-width: 1400px) {
        max-width: 95%;
    }

    @media (max-width: 768px) {
        max-width: 100%;
    }
}

.markdown-editor {
    flex: 1;
    border-right: 1px solid #dee2e6;
}

.markdown-preview {
    flex: 1;
    padding: 20px;
    overflow: auto;
    background: #fff;
}

.markdown-preview-only {
    width: 100%;
    padding: 20px;
    overflow: auto;
    background: #fff;
}

/* 图片查看器样式 */
.image-viewer {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    position: relative;
    max-width: 1000px;
    margin: 0 auto;

    /* 响应式设计 */
    @media (max-width: 1200px) {
        max-width: 95%;
    }

    @media (max-width: 768px) {
        max-width: 100%;
    }
}

.image-viewer img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.image-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    border-radius: 20px;
    padding: 8px 16px;
    display: flex;
    gap: 8px;
}

.image-controls button {
    background: transparent;
    border: none;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.image-controls button:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 文本查看器样式 */
.text-viewer {
    width: 100%;
    height: 100%;
    padding: 20px;
    overflow: auto;
    background: #fff;
}

.text-content {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* 不支持的文件类型样式 */
.unsupported-viewer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    color: #6c757d;
}

.unsupported-viewer .icon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.unsupported-viewer .message {
    font-size: 18px;
    margin-bottom: 8px;
}

.unsupported-viewer .description {
    font-size: 14px;
    text-align: center;
    max-width: 400px;
}

/* 加载状态样式 */
.document-loading {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
}

.document-loading .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e9ecef;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.document-loading .message {
    color: #6c757d;
    font-size: 16px;
    font-weight: 500;
    animation: pulse 2s ease-in-out infinite;
}

/* 增强的动画效果 */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 错误状态样式 */
.document-error {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    color: #dc3545;
}

.document-error .icon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.7;
}

.document-error .message {
    font-size: 18px;
    margin-bottom: 8px;
    text-align: center;
}

.document-error .description {
    font-size: 14px;
    text-align: center;
    max-width: 400px;
    color: #6c757d;
}

/* 工具栏按钮样式 */
.toolbar-btn {
    background: transparent;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 4px;
}

.toolbar-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.toolbar-btn:active {
    background: #dee2e6;
}

.toolbar-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.toolbar-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.toolbar-btn:disabled:hover {
    background: transparent;
    border-color: #dee2e6;
}

/* 文件信息样式 */
.file-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6c757d;
    font-size: 14px;
}

.file-type-badge {
    background: #e9ecef;
    color: #495057;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.file-type-badge.pdf { background: #dc3545; color: white; }
.file-type-badge.office { background: #0d6efd; color: white; }
.file-type-badge.excel { background: #107c41; color: white; }
.file-type-badge.code { background: #198754; color: white; }
.file-type-badge.markdown { background: #6f42c1; color: white; }
.file-type-badge.image { background: #fd7e14; color: white; }
.file-type-badge.text { background: #6c757d; color: white; }

/* 不支持格式提示样式 */
.unsupported-format-prompt {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 40px 20px;
    text-align: center;
    max-width: 500px;
    margin: 0 auto;
}

.unsupported-format-prompt .icon {
    font-size: 4rem;
    color: #dc3545;
    margin-bottom: 1.5rem;
}

.unsupported-format-prompt .message {
    font-size: 1.5rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
}

.unsupported-format-prompt .description {
    font-size: 1rem;
    color: #6c757d;
    line-height: 1.5;
}

/* Excel查看器样式 */
.excel-viewer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #fff;
    max-width: 1400px; /* Excel需要更大的宽度 */
    margin: 0 auto;

    /* 响应式设计 */
    @media (max-width: 1600px) {
        max-width: 95%;
    }

    @media (max-width: 768px) {
        max-width: 100%;
    }
}

.excel-sheets {
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
}

.sheet-tabs {
    display: flex;
    padding: 0 16px;
    gap: 4px;
}

.sheet-tab {
    padding: 8px 16px;
    border: none;
    background: transparent;
    color: #6c757d;
    cursor: pointer;
    border-radius: 4px 4px 0 0;
    font-size: 14px;
    transition: all 0.2s ease;
}

.sheet-tab:hover {
    background: #e9ecef;
    color: #495057;
}

.sheet-tab.active {
    background: #fff;
    color: #495057;
    border-bottom: 2px solid #107c41;
    font-weight: 500;
}

.excel-content {
    flex: 1;
    overflow: auto;
    padding: 16px;
    display: flex;
    justify-content: center;
}

.excel-table-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);

    /* 响应式设计 */
    @media (max-width: 1024px) {
        max-width: 100%;
        box-shadow: none;
        border-radius: 0;
    }
}

.excel-table-container {
    width: 100%;
    overflow: auto;
}

.excel-table-container {
    width: 100%;
    height: 100%;
    overflow: auto;
    position: relative;
}

.excel-table-wrapper {
    overflow: auto;
    max-height: calc(100vh - 200px);
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.excel-enhanced-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
    background: #fff;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.excel-enhanced-table th,
.excel-enhanced-table td {
    border: 1px solid #d0d7de;
    padding: 6px 10px;
    text-align: left;
    vertical-align: top;
    min-width: 60px;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
}

.excel-enhanced-table .excel-header-cell {
    background: #f6f8fa;
    font-weight: 600;
    color: #24292f;
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 2px solid #d0d7de;
}

.excel-enhanced-table .excel-even-row {
    background: #f6f8fa;
}

.excel-enhanced-table .excel-even-row:hover {
    background: #eef4fd;
}

.excel-enhanced-table tr:hover {
    background: #f0f6ff;
}

.excel-enhanced-table .excel-cell-hover {
    background: #dbeafe !important;
    box-shadow: inset 0 0 0 2px #3b82f6;
}

.excel-table-info {
    padding: 8px 12px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    font-size: 12px;
    color: #6c757d;
    display: flex;
    justify-content: space-between;
}

.excel-table-info span {
    margin-right: 16px;
}

/* Excel数字单元格右对齐 */
.excel-enhanced-table td[data-type="number"] {
    text-align: right;
}

/* Excel日期单元格居中 */
.excel-enhanced-table td[data-type="date"] {
    text-align: center;
}

/* Excel空单元格样式 */
.excel-enhanced-table td:empty {
    background: #fafafa;
}

/* ExcelJS特定样式 */
.excel-date-cell {
    text-align: center;
    color: #0066cc;
}

.excel-number-cell {
    text-align: right;
    font-family: 'Consolas', 'Monaco', monospace;
}

.excel-formula-cell {
    background-color: #f0f8ff;
    font-style: italic;
}

.excel-empty {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 40px;
    background: #f9f9f9;
    border-radius: 4px;
}

/* Excel大数据量分页样式 */
.excel-large-table-container {
    width: 100%;
    height: 100%;
}

.excel-pagination-info {
    padding: 10px 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #6c757d;
}

.excel-pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.excel-page-btn {
    padding: 4px 8px;
    border: 1px solid #dee2e6;
    background: #fff;
    color: #495057;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s;
}

.excel-page-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.excel-page-btn.active {
    background: #007bff;
    border-color: #007bff;
    color: #fff;
}

.excel-page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* docx-preview样式优化 */
.docx-preview-container {
    font-family: 'Times New Roman', '宋体', serif !important;
    line-height: 1.5 !important;
    color: #000 !important;
}

.docx-preview-container table {
    border-collapse: collapse !important;
    width: 100% !important;
    margin: 16px 0 !important;
}

.docx-preview-container table td,
.docx-preview-container table th {
    border: 1px solid #000 !important;
    padding: 8px 12px !important;
    vertical-align: top !important;
}

.docx-preview-container table th {
    background-color: #f0f0f0 !important;
    font-weight: bold !important;
}

.docx-preview-container p {
    margin: 8px 0 !important;
    text-align: justify !important;
}

.docx-preview-container h1,
.docx-preview-container h2,
.docx-preview-container h3 {
    font-weight: bold !important;
    color: #000 !important;
    margin: 16px 0 8px 0 !important;
}

.docx-preview-container strong {
    font-weight: bold !important;
}

/* 滚动条样式 */
.excel-table-wrapper::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.excel-table-wrapper::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.excel-table-wrapper::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.excel-table-wrapper::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .document-viewer-toolbar {
        flex-direction: column;
        gap: 8px;
        padding: 12px;
    }
    
    .toolbar-left,
    .toolbar-right {
        width: 100%;
        justify-content: center;
    }
    
    .markdown-viewer {
        flex-direction: column;
    }
    
    .markdown-editor,
    .markdown-preview {
        flex: none;
        height: 50%;
    }
    
    .markdown-editor {
        border-right: none;
        border-bottom: 1px solid #dee2e6;
    }
}

/* ==================== docx-preview 专用样式优化 ==================== */



/* docx-preview 主容器样式 - 完全去除边框和背景，确保100%宽度 */
.docx-preview {
    font-family: 'Times New Roman', '宋体', serif !important;
    line-height: 1.4 !important;
    color: #000 !important;
    background-color: transparent !important;
    width: 100% !important;
    max-width: 100% !important;
    min-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    overflow-x: auto !important;
    word-wrap: break-word !important;
    min-height: auto !important; /* 添加最小高度自适应 */
    height: auto !important; /* 确保高度自适应 */
}

/* docx-preview 页面包装器 - 100%展示，无边框，确保内容不被截断 */
.docx-preview .docx-wrapper {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-shadow: none !important;
    border: none !important;
    background-color: transparent !important;
    overflow: visible !important;
    word-wrap: break-word !important;
    white-space: normal !important;
    height: auto !important; /* 确保高度自适应 */
    min-height: auto !important; /* 确保最小高度自适应 */
}

/* docx-preview 页面样式 - 100%内容展示，无边框，确保内容完整显示 */
.docx-preview .docx-page {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-shadow: none !important;
    border: none !important;
    page-break-after: auto !important;
    background-color: transparent !important;
    overflow: visible !important;
    word-wrap: break-word !important;
    white-space: normal !important;
    height: auto !important; /* 确保高度自适应 */
    min-height: auto !important; /* 确保最小高度自适应 */
}

.docx-preview .docx-page:last-child {
    border-bottom: none !important;
    margin-bottom: 0 !important;
}

/* docx-preview 段落样式 */
.docx-preview p {
    margin: 6px 0 !important;
    line-height: 1.5 !important;
    text-align: justify !important;
    font-size: 14px !important;
}

/* docx-preview 标题样式 */
.docx-preview h1,
.docx-preview h2,
.docx-preview h3,
.docx-preview h4,
.docx-preview h5,
.docx-preview h6 {
    margin: 15px 0 10px 0 !important;
    font-weight: bold !important;
    color: #000 !important;
    line-height: 1.3 !important;
}

.docx-preview h1 { font-size: 24px !important; }
.docx-preview h2 { font-size: 20px !important; }
.docx-preview h3 { font-size: 18px !important; }
.docx-preview h4 { font-size: 16px !important; }
.docx-preview h5 { font-size: 14px !important; }
.docx-preview h6 { font-size: 12px !important; }

/* docx-preview 表格样式 */
.docx-preview table {
    border-collapse: collapse !important;
    width: 100% !important;
    margin: 10px 0 !important;
    font-size: inherit !important;
    table-layout: auto !important;
}

.docx-preview table td,
.docx-preview table th {
    border: 1px solid #333 !important;
    padding: 8px !important;
    text-align: left !important;
    vertical-align: top !important;
    word-wrap: break-word !important;
    background-color: #fff !important;
}

.docx-preview table th {
    background-color: #f8f9fa !important;
    font-weight: bold !important;
}

/* docx-preview 列表样式 */
.docx-preview ul,
.docx-preview ol {
    margin: 10px 0 !important;
    padding-left: 25px !important;
}

.docx-preview li {
    margin: 5px 0 !important;
    line-height: 1.5 !important;
}

/* docx-preview 图片样式 */
.docx-preview img {
    max-width: 100% !important;
    height: auto !important;
    display: block !important;
    margin: 10px 0 !important;
}

/* docx-preview 文本样式 */
.docx-preview strong, .docx-preview b {
    font-weight: bold !important;
}

.docx-preview em, .docx-preview i {
    font-style: italic !important;
}

.docx-preview u {
    text-decoration: underline !important;
}

/* docx-preview 链接样式 */
.docx-preview a {
    color: #0066cc !important;
    text-decoration: underline !important;
}

.docx-preview a:hover {
    color: #004499 !important;
}

/* 强制覆盖docx-preview默认样式 - 去除所有边框 */
.docx-preview *,
.docx-preview *::before,
.docx-preview *::after {
    box-shadow: none !important;
}

/* 特别针对docx-preview的容器元素 */
.docx-preview > div,
.docx-preview .docx-wrapper,
.docx-preview .docx-page,
.docx-preview [class*="docx"] {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    background-color: transparent !important;
}

/* 确保文档内容区域无边框，100%宽度显示 */
#wordContent .docx-preview,
#wordContent .docx-preview > *,
#wordContent .docx-preview .docx-wrapper,
#wordContent .docx-preview .docx-page {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    background: transparent !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    min-width: 100% !important;
    overflow: visible !important;
    height: auto !important; /* 确保高度自适应 */
    min-height: auto !important; /* 确保最小高度自适应 */
}

/* 确保所有docx-preview内部元素都能正确显示 */
.docx-preview * {
    max-width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    white-space: normal !important;
}

/* 特别处理表格，确保不会被截断 */
.docx-preview table {
    width: 100% !important;
    max-width: 100% !important;
    table-layout: auto !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
}

.docx-preview table td,
.docx-preview table th {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    white-space: normal !important;
    max-width: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .docx-preview .docx-wrapper,
    .docx-preview .docx-page {
        padding: 0 !important;
    }

    .docx-preview {
        font-size: 12px !important;
    }

    .docx-preview table {
        font-size: 11px !important;
    }

    .docx-preview table td,
    .docx-preview table th {
        padding: 4px !important;
    }
}

/* ==================== 智能解析按钮样式 ==================== */

.smart-parse-btn {
    position: relative;
    border: 1px solid #dee2e6;
    background: #fff;
    color: #495057;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 13px;
    font-weight: 500;
    /* 移除过渡动画 */
    /* transition: all 0.3s ease; */
    cursor: pointer;
    min-width: 100px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.smart-parse-btn:hover {
    border-color: #007bff;
    color: #007bff;
    background: #f8f9fa;
    /* 移除悬停时的位移动画 */
    /* transform: translateY(-1px); */
    box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}

/* 不同状态的样式 */
.smart-parse-btn.status-pending {
    border-color: #ffc107;
    color: #856404;
    background: #fff3cd;
}

.smart-parse-btn.status-processing {
    border-color: #17a2b8;
    color: #0c5460;
    background: #d1ecf1;
}

.smart-parse-btn.status-indexed {
    border-color: #28a745;
    color: #155724;
    background: #d4edda;
}

.smart-parse-btn.status-failed {
    border-color: #dc3545;
    color: #721c24;
    background: #f8d7da;
}

.smart-parse-btn.status-failed:hover {
    border-color: #dc3545;
    color: #721c24;
    background: #f5c6cb;
}

/* 图标样式（移除动画） */
.parse-icon {
    font-size: 14px;
    /* 移除 transition 动画 */
}

/* 移除悬停旋转动画 */
.smart-parse-btn:hover .parse-icon {
    /* 移除 transform: rotate(180deg); */
}

/* 移除解析中的脉冲动画 */
.smart-parse-btn.status-processing .parse-icon {
    /* 移除 animation: pulse 1.5s ease-in-out infinite; */
}

/* 移除加载动画相关样式 */
.parse-spinner {
    display: none !important; /* 彻底隐藏spinner */
}

/* 文本样式 */
.parse-text {
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
}

/* 移除脉冲动画 */
/*
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}
*/

/* 移除旋转动画 */
/*
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
*/

/* 完全移除所有spinner相关样式 */
.smart-parse-btn .spinner-border,
.smart-parse-btn .parse-spinner .spinner-border {
    display: none !important; /* 彻底隐藏所有spinner元素 */
}

/* 响应式设计 */
@media (max-width: 768px) {
    .smart-parse-btn {
        min-width: 80px;
        padding: 4px 8px;
        font-size: 12px;
    }

    .parse-text {
        font-size: 11px;
    }

    .parse-icon {
        font-size: 12px;
    }
}
