/**
 * 百度统计通用脚本 - 优化版本
 * 特点：
 * 1. 完全异步加载，不阻塞页面渲染
 * 2. 延迟加载，确保页面主要内容优先
 * 3. 超时和错误处理，提高容错性
 * 4. 无控制台日志，保持简洁
 */
(function() {
    'use strict';
    
    // 百度统计配置
    const BAIDU_ANALYTICS_ID = 'dbf0743fb0d9db99a8e867b5588d196d';
    const LOAD_DELAY = 1000; // 延迟1秒加载
    const TIMEOUT_DELAY = 5000; // 5秒超时
    
    function loadBaiduAnalytics() {
        try {
            // 初始化百度统计全局变量
            window._hmt = window._hmt || [];
            
            // 创建脚本元素
            var script = document.createElement('script');
            script.async = true;
            script.src = 'https://hm.baidu.com/hm.js?' + BAIDU_ANALYTICS_ID;
            
            // 设置超时处理
            var timeoutId = setTimeout(function() {
                // 超时后静默处理，不输出日志
                script.onload = script.onerror = null;
            }, TIMEOUT_DELAY);
            
            // 加载成功处理
            script.onload = function() {
                clearTimeout(timeoutId);
            };
            
            // 加载失败处理
            script.onerror = function() {
                clearTimeout(timeoutId);
            };
            
            // 插入到页面中
            var firstScript = document.getElementsByTagName('script')[0];
            if (firstScript && firstScript.parentNode) {
                firstScript.parentNode.insertBefore(script, firstScript);
            } else {
                // 备用方案：插入到head中
                document.head.appendChild(script);
            }
        } catch (e) {
            // 静默处理错误，不影响页面功能
        }
    }
    
    // 延迟加载统计脚本
    function initAnalytics() {
        if (document.readyState === 'complete') {
            setTimeout(loadBaiduAnalytics, LOAD_DELAY);
        } else {
            window.addEventListener('load', function() {
                setTimeout(loadBaiduAnalytics, LOAD_DELAY);
            });
        }
    }
    
    // 立即初始化
    initAnalytics();
})();
