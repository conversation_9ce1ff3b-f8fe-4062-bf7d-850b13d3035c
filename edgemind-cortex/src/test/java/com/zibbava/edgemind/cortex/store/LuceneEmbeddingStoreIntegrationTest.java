package com.zibbava.edgemind.cortex.store;

import dev.langchain4j.data.document.Metadata;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import dev.langchain4j.store.embedding.filter.Filter;
import dev.langchain4j.store.embedding.filter.MetadataFilterBuilder;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.nio.file.Path;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * LuceneEmbeddingStore 完整集成测试
 * <p>
 * 🎯 测试目标：
 * - 验证LuceneEmbeddingStore能够正确存储向量和文本数据
 * - 验证向量搜索功能是否正常工作
 * - 验证混合搜索（向量+关键词）功能是否正常工作
 * - 验证元数据存储和过滤查询是否正常工作
 * - 验证数据持久化功能
 * <p>
 * 🔧 测试策略：
 * - 使用临时目录避免测试数据污染
 * - 使用测试用嵌入模型生成稳定的向量
 * - 模拟真实的文档索引场景
 * - 详细的调试信息输出
 */
@SpringBootTest
@TestPropertySource(properties = {
        "embedding.base-url=http://localhost:11434",
        "vector.search.max-results=10",
        "vector.search.min-score=0.3",
        "vector.hybrid.enabled=true"
})
@Slf4j
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class LuceneEmbeddingStoreIntegrationTest {

    @TempDir
    Path tempDir;

    private LuceneEmbeddingStore luceneStore;

    @Autowired
    private EmbeddingModel embeddingModel;

    // 测试数据
    private List<TestDocument> testDocuments;
    private Map<String, String> documentIds;

    @BeforeEach
    void setUp() throws Exception {
        log.info("🚀 开始设置LuceneEmbeddingStore集成测试环境");

        log.info("✅ 使用Spring注入的EmbeddingModel: {}", embeddingModel.getClass().getSimpleName());

        // 创建Lucene向量存储
        String indexPath = tempDir.resolve("test-lucene-index").toString();
        luceneStore = new LuceneEmbeddingStore(
                indexPath,
                512,  // BGE Small Chinese v1.5模型的向量维度
                10,   // 批处理大小
                true, // 启用混合搜索
                0.7f  // 混合搜索权重
        );
        log.info("✅ 创建LuceneEmbeddingStore完成，索引路径: {}", indexPath);

        // 准备测试数据
        prepareTestData();
        log.info("✅ 测试数据准备完成，共 {} 个文档", testDocuments.size());
    }

    @AfterEach
    void tearDown() throws Exception {
        if (luceneStore != null) {
            luceneStore.destroy();
            log.info("✅ LuceneEmbeddingStore资源清理完成");
        }
    }

    /**
     * 准备测试数据 - 模拟真实的啤酒销售合同文档
     */
    private void prepareTestData() {
        testDocuments = Arrays.asList(
                new TestDocument(
                        "啤酒销售合同第一条：甲方为青岛啤酒股份有限公司，乙方为北京超市连锁有限公司。本合同约定甲方向乙方供应青岛啤酒产品。",
                        "contract_clause_1",
                        "space_001",
                        "node_001",
                        "啤酒销售合同.docx",
                        "user_001",
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                ),
                new TestDocument(
                        "第二条：供应产品包括青岛啤酒经典系列、青岛啤酒纯生系列、青岛啤酒IPA系列等多种规格产品，包装形式有瓶装、听装等。",
                        "contract_clause_2",
                        "space_001",
                        "node_001",
                        "啤酒销售合同.docx",
                        "user_001",
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                ),
                new TestDocument(
                        "第三条：价格条款，青岛啤酒经典330ml瓶装单价为3.5元，青岛啤酒纯生500ml听装单价为5.2元，价格含税不含运费。",
                        "contract_clause_3",
                        "space_001",
                        "node_001",
                        "啤酒销售合同.docx",
                        "user_001",
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                ),
                new TestDocument(
                        "第四条：交货条款，甲方应在收到乙方订单后7个工作日内完成发货，运输方式为汽运，运费由乙方承担。",
                        "contract_clause_4",
                        "space_001",
                        "node_001",
                        "啤酒销售合同.docx",
                        "user_001",
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                ),
                new TestDocument(
                        "第五条：付款条款，乙方应在收货后30天内完成付款，付款方式为银行转账，逾期付款按日利率0.05%计算滞纳金。",
                        "contract_clause_5",
                        "space_001",
                        "node_001",
                        "啤酒销售合同.docx",
                        "user_001",
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                ),
                new TestDocument(
                        "附件：产品质量标准，青岛啤酒产品应符合国家食品安全标准，酒精度为3.1-4.3%vol，保质期为12个月。",
                        "contract_appendix_1",
                        "space_001",
                        "node_002", // 不同的节点ID
                        "产品质量标准.docx",
                        "user_001",
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                )
        );

        documentIds = new HashMap<>();
    }

    @Test
    @Order(1)
    @DisplayName("1. 文档索引测试 - 验证向量和文本数据存储")
    void testDocumentIndexing() {
        log.info("🧪 开始测试文档索引功能");

        // 逐个添加测试文档
        for (TestDocument testDoc : testDocuments) {
            try {
                // 创建TextSegment
                Metadata metadata = Metadata.from(testDoc.getMetadataMap());
                TextSegment segment = TextSegment.from(testDoc.getText(), metadata);

                // 生成向量
                Embedding embedding = embeddingModel.embed(segment).content();
                log.debug("📊 生成向量维度: {}", embedding.vectorAsList().size());

                // 存储到Lucene
                String documentId = luceneStore.add(embedding, segment);
                documentIds.put(testDoc.getId(), documentId);

                log.info("✅ 文档存储成功: {} -> {}", testDoc.getId(), documentId);

            } catch (Exception e) {
                log.error("❌ 文档存储失败: {}", testDoc.getId(), e);
                Assertions.fail("文档存储失败: " + e.getMessage());
            }
        }

        log.info("🎯 文档索引测试完成，成功存储 {} 个文档", documentIds.size());
        Assertions.assertEquals(testDocuments.size(), documentIds.size(), "存储的文档数量应该与测试数据一致");
    }

    @Test
    @Order(2)
    @DisplayName("2. 向量搜索测试 - 验证语义相似性检索")
    void testVectorSearch() {
        log.info("🧪 开始测试向量搜索功能");

        // 确保文档已经索引
        testDocumentIndexing();

        // 测试查询
        String[] testQueries = {
                "青岛啤酒价格多少钱",
                "交货时间是多久",
                "付款方式是什么",
                "产品质量标准",
                "乙方是谁",
                "多少毫升",
                "啊",
                "单元测试",
                "你是谁",
                "无异议"
        };

        for (String query : testQueries) {
            try {
                log.info("🔍 测试查询: {}", query);

                // 生成查询向量
                Embedding queryEmbedding = embeddingModel.embed(query).content();

                // 执行向量搜索（降低分数阈值）
                EmbeddingSearchRequest request = EmbeddingSearchRequest.builder()
                        .queryEmbedding(queryEmbedding)
                        .maxResults(5)
                        .minScore(0.0) // 降低阈值以便调试
                        .build();

                EmbeddingSearchResult<TextSegment> result = luceneStore.search(request);
                List<EmbeddingMatch<TextSegment>> matches = result.matches();

                log.info("📊 查询 '{}' 返回 {} 个结果", query, matches.size());

                // 输出详细结果
                for (int i = 0; i < matches.size(); i++) {
                    EmbeddingMatch<TextSegment> match = matches.get(i);
                    log.info("  结果 {}: 分数={}, 文本={}",
                            i + 1, match.score(),
                            match.embedded().text());
                }

                Assertions.assertTrue(matches.size() > 0, "应该返回至少一个搜索结果");

            } catch (Exception e) {
                log.error("❌ 向量搜索失败: {}", query, e);
                Assertions.fail("向量搜索失败: " + e.getMessage());
            }
        }

        log.info("🎯 向量搜索测试完成");
    }

    @Test
    @Order(3)
    @DisplayName("3. 混合搜索测试 - 验证向量+关键词搜索")
    void testHybridSearch() {
        log.info("🧪 开始测试混合搜索功能");

        // 确保文档已经索引
        testDocumentIndexing();

        // 测试混合搜索查询
        String[] testQueries = {
                "青岛啤酒",
                "价格",
                "交货",
                "付款",
                "质量标准"
        };

        for (String query : testQueries) {
            try {
                log.info("🔀 测试混合搜索查询: {}", query);

                // 生成查询向量
                Embedding queryEmbedding = embeddingModel.embed(query).content();

                // 执行混合搜索（降低分数阈值）
                EmbeddingSearchResult<TextSegment> result = luceneStore.hybridSearch(
                        query, queryEmbedding, 5, 0.0, null);
                List<EmbeddingMatch<TextSegment>> matches = result.matches();

                log.info("📊 混合搜索 '{}' 返回 {} 个结果", query, matches.size());

                // 输出详细结果
                for (int i = 0; i < matches.size(); i++) {
                    EmbeddingMatch<TextSegment> match = matches.get(i);
                    log.info("  结果 {}: 分数={:.4f}, 文本={}",
                            i + 1, match.score(),
                            match.embedded().text().substring(0, Math.min(50, match.embedded().text().length())) + "...");
                }

                Assertions.assertTrue(matches.size() > 0, "混合搜索应该返回至少一个结果");

            } catch (Exception e) {
                log.error("❌ 混合搜索失败: {}", query, e);
                Assertions.fail("混合搜索失败: " + e.getMessage());
            }
        }

        log.info("🎯 混合搜索测试完成");
    }

    @Test
    @Order(4)
    @DisplayName("4. 元数据过滤测试 - 验证基于元数据的条件查询")
    void testMetadataFiltering() {
        log.info("🧪 开始测试元数据过滤功能");

        // 确保文档已经索引
        testDocumentIndexing();

        try {
            // 测试基于node_id的精确匹配过滤
            log.info("🔍 测试基于node_id的精确匹配过滤查询");

            String queryText = "青岛啤酒";
            Embedding queryEmbedding = embeddingModel.embed(queryText).content();

            // 创建过滤器 - 只查询node_001的文档
            Filter nodeFilter = MetadataFilterBuilder.metadataKey("node_id").isEqualTo("node_001");

            EmbeddingSearchResult<TextSegment> result = luceneStore.hybridSearch(
                    queryText, queryEmbedding, 10, 0.1, nodeFilter);
            List<EmbeddingMatch<TextSegment>> matches = result.matches();

            log.info("📊 node_id精确匹配过滤查询返回 {} 个结果", matches.size());

            // 验证所有结果都来自node_001
            for (EmbeddingMatch<TextSegment> match : matches) {
                String nodeId = match.embedded().metadata().getString("node_id");
                log.info("  结果node_id: {}, 文本: {}", nodeId,
                        match.embedded().text().substring(0, Math.min(30, match.embedded().text().length())) + "...");
                Assertions.assertEquals("node_001", nodeId, "过滤结果应该只包含node_001的文档");
            }

            // 测试基于node_path的包含匹配过滤（类似like操作）
            log.info("🔍 测试基于node_path的包含匹配过滤查询");

            // 创建包含匹配过滤器 - 查询node_path包含"documents"的文档
            Filter pathContainsFilter = MetadataFilterBuilder.metadataKey("node_path").containsString("documents");

            EmbeddingSearchResult<TextSegment> pathResult = luceneStore.hybridSearch(
                    queryText, queryEmbedding, 10, 0.1, pathContainsFilter);
            List<EmbeddingMatch<TextSegment>> pathMatches = pathResult.matches();

            log.info("📊 node_path包含匹配过滤查询返回 {} 个结果", pathMatches.size());

            // 验证所有结果的node_path都包含"documents"
            for (EmbeddingMatch<TextSegment> match : pathMatches) {
                String nodePath = match.embedded().metadata().getString("node_path");
                log.info("  结果node_path: {}, 文本: {}", nodePath,
                        match.embedded().text().substring(0, Math.min(30, match.embedded().text().length())) + "...");
                Assertions.assertTrue(nodePath.contains("documents"),
                        "过滤结果的node_path应该包含'documents'");
            }

            // 测试基于文件名的包含匹配过滤
            log.info("🔍 测试基于file_name的包含匹配过滤查询");

            Filter fileContainsFilter = MetadataFilterBuilder.metadataKey("file_name").containsString("啤酒");

            EmbeddingSearchResult<TextSegment> fileResult = luceneStore.hybridSearch(
                    queryText, queryEmbedding, 10, 0.1, fileContainsFilter);
            List<EmbeddingMatch<TextSegment>> fileMatches = fileResult.matches();

            log.info("📊 file_name包含匹配过滤查询返回 {} 个结果", fileMatches.size());

            // 验证所有结果的file_name都包含"啤酒"
            for (EmbeddingMatch<TextSegment> match : fileMatches) {
                String fileName = match.embedded().metadata().getString("file_name");
                log.info("  结果file_name: {}, 文本: {}", fileName,
                        match.embedded().text().substring(0, Math.min(30, match.embedded().text().length())) + "...");
                Assertions.assertTrue(fileName.contains("啤酒"),
                        "过滤结果的file_name应该包含'啤酒'");
            }


        } catch (Exception e) {
            log.error("❌ 元数据过滤测试失败", e);
            Assertions.fail("元数据过滤测试失败: " + e.getMessage());
        }

        log.info("🎯 元数据过滤测试完成");
    }

    @Test
    @Order(5)
    @DisplayName("5. 数据删除测试 - 验证文档删除功能")
    void testDataDeletion() {
        log.info("🧪 开始测试数据删除功能");

        // 确保文档已经索引
        testDocumentIndexing();

        try {
            // 测试根据ID删除单个文档
            String documentIdToDelete = documentIds.values().iterator().next();
            log.info("🗑️ 删除文档: {}", documentIdToDelete);

            luceneStore.remove(documentIdToDelete);

            // 验证删除后的搜索结果
            String queryText = "青岛啤酒";
            Embedding queryEmbedding = embeddingModel.embed(queryText).content();

            EmbeddingSearchResult<TextSegment> result = luceneStore.hybridSearch(
                    queryText, queryEmbedding, 10, 0.1, null);
            List<EmbeddingMatch<TextSegment>> matches = result.matches();

            log.info("📊 删除后搜索返回 {} 个结果", matches.size());

            // 验证被删除的文档不在结果中
            boolean deletedDocFound = matches.stream()
                    .anyMatch(match -> documentIdToDelete.equals(match.embeddingId()));
            Assertions.assertFalse(deletedDocFound, "被删除的文档不应该出现在搜索结果中");

            // 测试根据元数据批量删除
            log.info("🗑️ 测试根据元数据批量删除");
            luceneStore.removeByMetadata("node_id", "node_002");

            // 验证node_002的文档已被删除
            Filter nodeFilter = MetadataFilterBuilder.metadataKey("node_id").isEqualTo("node_002");
            EmbeddingSearchResult<TextSegment> nodeResult = luceneStore.hybridSearch(
                    queryText, queryEmbedding, 10, 0.1, nodeFilter);

            log.info("📊 node_002删除后搜索返回 {} 个结果", nodeResult.matches().size());
            Assertions.assertEquals(0, nodeResult.matches().size(), "node_002的文档应该已被删除");

        } catch (Exception e) {
            log.error("❌ 数据删除测试失败", e);
            Assertions.fail("数据删除测试失败: " + e.getMessage());
        }

        log.info("🎯 数据删除测试完成");
    }

    @Test
    @Order(6)
    @DisplayName("6. 数据持久化测试 - 验证重启后数据保持")
    void testDataPersistence() {
        log.info("🧪 开始测试数据持久化功能");

        // 确保文档已经索引
        testDocumentIndexing();

        try {
            String indexPath = tempDir.resolve("test-lucene-index").toString();

            // 执行一次搜索，记录结果
            String queryText = "青岛啤酒价格";
            Embedding queryEmbedding = embeddingModel.embed(queryText).content();

            EmbeddingSearchResult<TextSegment> beforeResult = luceneStore.hybridSearch(
                    queryText, queryEmbedding, 5, 0.3, null);
            int beforeCount = beforeResult.matches().size();

            log.info("📊 重启前搜索返回 {} 个结果", beforeCount);

            // 关闭当前存储
            luceneStore.destroy();
            log.info("🔄 关闭LuceneEmbeddingStore");

            // 重新创建存储（模拟重启）
            luceneStore = new LuceneEmbeddingStore(
                    indexPath,
                    1024, // BGE-Large模型的向量维度
                    10,
                    true,
                    0.7f
            );
            log.info("🔄 重新创建LuceneEmbeddingStore");

            // 再次执行相同的搜索
            EmbeddingSearchResult<TextSegment> afterResult = luceneStore.hybridSearch(
                    queryText, queryEmbedding, 5, 0.3, null);
            int afterCount = afterResult.matches().size();

            log.info("📊 重启后搜索返回 {} 个结果", afterCount);

            // 验证数据持久化
            Assertions.assertTrue(afterCount > 0, "重启后应该能够检索到数据");

            // 比较搜索结果的一致性
            if (beforeCount > 0 && afterCount > 0) {
                String beforeFirstText = beforeResult.matches().get(0).embedded().text();
                String afterFirstText = afterResult.matches().get(0).embedded().text();

                log.info("📊 重启前第一个结果: {}", beforeFirstText.substring(0, Math.min(50, beforeFirstText.length())));
                log.info("📊 重启后第一个结果: {}", afterFirstText.substring(0, Math.min(50, afterFirstText.length())));
            }

        } catch (Exception e) {
            log.error("❌ 数据持久化测试失败", e);
            Assertions.fail("数据持久化测试失败: " + e.getMessage());
        }

        log.info("🎯 数据持久化测试完成");
    }

    @Test
    @Order(7)
    @DisplayName("7. 性能基准测试 - 验证搜索性能")
    void testPerformanceBenchmark() {
        log.info("🧪 开始测试性能基准");

        // 确保文档已经索引
        testDocumentIndexing();

        try {
            String queryText = "青岛啤酒";
            Embedding queryEmbedding = embeddingModel.embed(queryText).content();

            int iterations = 10;
            long totalTime = 0;

            // 预热
            for (int i = 0; i < 3; i++) {
                luceneStore.hybridSearch(queryText, queryEmbedding, 5, 0.3, null);
            }

            // 性能测试
            for (int i = 0; i < iterations; i++) {
                long startTime = System.currentTimeMillis();

                EmbeddingSearchResult<TextSegment> result = luceneStore.hybridSearch(
                        queryText, queryEmbedding, 5, 0.3, null);

                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                totalTime += duration;

                log.debug("🏃 第 {} 次搜索耗时: {}ms, 结果数: {}",
                        i + 1, duration, result.matches().size());
            }

            double averageTime = (double) totalTime / iterations;
            log.info("📊 性能基准测试结果:");
            log.info("  - 总测试次数: {}", iterations);
            log.info("  - 总耗时: {}ms", totalTime);
            log.info("  - 平均耗时: {:.2f}ms", averageTime);
            log.info("  - QPS: {:.2f}", 1000.0 / averageTime);

            // 性能断言（根据实际情况调整）
            Assertions.assertTrue(averageTime < 1000, "平均搜索时间应该小于1秒");

        } catch (Exception e) {
            log.error("❌ 性能基准测试失败", e);
            Assertions.fail("性能基准测试失败: " + e.getMessage());
        }

        log.info("🎯 性能基准测试完成");
    }

    /**
     * 测试文档数据类
     */
    private static class TestDocument {
        private final String text;
        private final String id;
        private final String spaceId;
        private final String nodeId;
        private final String fileName;
        private final String userId;
        private final String mimeType;

        public TestDocument(String text, String id, String spaceId, String nodeId,
                            String fileName, String userId, String mimeType) {
            this.text = text;
            this.id = id;
            this.spaceId = spaceId;
            this.nodeId = nodeId;
            this.fileName = fileName;
            this.userId = userId;
            this.mimeType = mimeType;
        }

        public String getText() {
            return text;
        }

        public String getId() {
            return id;
        }

        public String getSpaceId() {
            return spaceId;
        }

        public String getNodeId() {
            return nodeId;
        }

        public String getFileName() {
            return fileName;
        }

        public String getUserId() {
            return userId;
        }

        public String getMimeType() {
            return mimeType;
        }

        public Map<String, Object> getMetadataMap() {
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("space_id", spaceId);
            metadata.put("node_id", nodeId);
            metadata.put("file_name", fileName);
            metadata.put("user_id", userId);
            metadata.put("mime_type", mimeType);
            metadata.put("document_id", id);
            metadata.put("created_time", "2024-01-01T00:00:00Z");
            metadata.put("updated_time", "2024-01-01T00:00:00Z");
            metadata.put("chunk_index", "0");
            metadata.put("chunk_size", String.valueOf(text.length()));
            // 添加缺失的必需字段
            metadata.put("node_path", "/documents/" + fileName);
            metadata.put("title", "测试文档标题");
            return metadata;
        }
    }


}
