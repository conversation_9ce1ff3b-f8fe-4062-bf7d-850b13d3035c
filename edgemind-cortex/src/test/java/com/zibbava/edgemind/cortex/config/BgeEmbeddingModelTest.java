package com.zibbava.edgemind.cortex.config;

import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.embedding.onnx.bgesmallzhv15.BgeSmallZhV15EmbeddingModel;
import dev.langchain4j.model.output.Response;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * BGE中文嵌入模型测试
 */
@SpringBootTest
@Slf4j
public class BgeEmbeddingModelTest {

    @Test
    public void testBgeSmallZhV15EmbeddingModel() {
        log.info("🚀 开始测试BGE Small Chinese v1.5嵌入模型");

        try {
            // 创建BGE中文嵌入模型
            EmbeddingModel embeddingModel = new BgeSmallZhV15EmbeddingModel();
            log.info("✅ BGE中文嵌入模型创建成功");

            // 测试中文文本嵌入
            String chineseText = "这是一个中文测试文本，用于验证BGE中文嵌入模型的功能。";
            TextSegment textSegment = TextSegment.from(chineseText);

            // 生成嵌入向量
            Response<Embedding> response = embeddingModel.embed(textSegment);
            Embedding embedding = response.content();

            // 验证结果
            assertNotNull(embedding, "嵌入向量不应为空");
            assertNotNull(embedding.vectorAsList(), "嵌入向量列表不应为空");
            assertEquals(512, embedding.dimension(), "BGE Small Chinese v1.5模型的向量维度应为512");

            log.info("📊 嵌入向量维度: {}", embedding.dimension());
            log.info("📝 测试文本: {}", chineseText);
            log.info("🎯 向量前5个值: {}", embedding.vectorAsList().subList(0, 5));
            log.info("✅ BGE中文嵌入模型测试通过");

        } catch (Exception e) {
            log.error("❌ BGE中文嵌入模型测试失败: {}", e.getMessage(), e);
            fail("BGE中文嵌入模型测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testMultipleTextsEmbedding() {
        log.info("🚀 开始测试多文本嵌入");

        try {
            EmbeddingModel embeddingModel = new BgeSmallZhV15EmbeddingModel();

            // 测试多个中文文本
            String[] texts = {
                "人工智能是计算机科学的一个分支",
                "机器学习是人工智能的核心技术",
                "深度学习是机器学习的重要方法",
                "自然语言处理是AI的重要应用领域"
            };

            for (String text : texts) {
                Response<Embedding> response = embeddingModel.embed(text);
                Embedding embedding = response.content();

                assertNotNull(embedding, "嵌入向量不应为空");
                assertEquals(512, embedding.dimension(), "向量维度应为512");

                log.info("📝 文本: {} -> 向量维度: {}", text, embedding.dimension());
            }

            log.info("✅ 多文本嵌入测试通过");

        } catch (Exception e) {
            log.error("❌ 多文本嵌入测试失败: {}", e.getMessage(), e);
            fail("多文本嵌入测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testEmbeddingConsistency() {
        log.info("🚀 开始测试嵌入一致性");

        try {
            EmbeddingModel embeddingModel = new BgeSmallZhV15EmbeddingModel();
            String testText = "测试文本的嵌入一致性";

            // 多次嵌入同一文本
            Response<Embedding> response1 = embeddingModel.embed(testText);
            Response<Embedding> response2 = embeddingModel.embed(testText);

            Embedding embedding1 = response1.content();
            Embedding embedding2 = response2.content();

            // 验证一致性
            assertEquals(embedding1.dimension(), embedding2.dimension(), "向量维度应一致");
            assertEquals(embedding1.vectorAsList(), embedding2.vectorAsList(), "相同文本的嵌入向量应一致");

            log.info("✅ 嵌入一致性测试通过");

        } catch (Exception e) {
            log.error("❌ 嵌入一致性测试失败: {}", e.getMessage(), e);
            fail("嵌入一致性测试失败: " + e.getMessage());
        }
    }
}
