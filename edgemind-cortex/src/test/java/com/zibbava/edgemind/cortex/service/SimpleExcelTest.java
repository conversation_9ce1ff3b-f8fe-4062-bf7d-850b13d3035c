package com.zibbava.edgemind.cortex.service;

import com.zibbava.edgemind.cortex.strategy.impl.TikaParsingStrategy;
import com.zibbava.edgemind.cortex.store.LuceneEmbeddingStore;
import dev.langchain4j.data.document.Document;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;

/**
 * 简化的Excel文档测试类
 * 专门测试Excel文档的解析和搜索功能
 */
@SpringBootTest
@ActiveProfiles("demo")
@Slf4j
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class SimpleExcelTest {

    @Autowired
    private LuceneEmbeddingStore luceneEmbeddingStore;
    
    @Autowired
    private EmbeddingModel embeddingModel;
    
    @Autowired
    private TikaParsingStrategy tikaParsingStrategy;

    private static byte[] testExcelContent;

    @BeforeAll
    static void setupTestData() throws IOException {
        log.info("🧪 开始设置Excel文档测试数据");
        
        // 创建测试Excel文件
        testExcelContent = createTestExcelFile();
        log.info("✅ 测试Excel文件创建完成，大小: {} bytes", testExcelContent.length);
    }

    @Test
    @Order(1)
    @DisplayName("1. 测试Excel文档解析")
    void testExcelDocumentParsing() throws Exception {
        log.info("🧪 开始测试Excel文档解析");
        
        // 1. 测试文档解析
        Document document = tikaParsingStrategy.parseDocument(testExcelContent, "工作进度表（工作计划表）1.xlsx");
        Assertions.assertNotNull(document, "解析后的文档不应为空");
        
        String content = document.text();
        Assertions.assertNotNull(content, "文档内容不应为空");
        Assertions.assertFalse(content.trim().isEmpty(), "文档内容不应为空字符串");
        
        log.info("✅ Excel文档解析成功");
        log.info("📄 解析内容长度: {} 字符", content.length());
        log.info("📄 内容预览: {}", content.substring(0, Math.min(500, content.length())));
        
        // 3. 验证内容包含预期的Excel数据
        Assertions.assertTrue(content.contains("工作") || content.contains("进度") || content.contains("计划") || 
                content.contains("任务") || content.contains("负责人"), 
                "解析内容应包含Excel中的关键词");
    }

    @Test
    @Order(2)
    @DisplayName("2. 测试Excel文档向量化和搜索")
    void testExcelDocumentVectorization() throws Exception {
        log.info("🧪 开始测试Excel文档向量化和搜索");
        
        // 1. 解析文档
        Document document = tikaParsingStrategy.parseDocument(testExcelContent, "工作进度表（工作计划表）1.xlsx");
        String content = document.text();
        
        // 2. 创建文本段
        TextSegment segment = TextSegment.from(content);
        
        // 3. 生成向量
        Embedding embedding = embeddingModel.embed(content).content();
        log.info("📊 向量维度: {}", embedding.dimension());
        
        // 4. 存储到Lucene
        String vectorId = luceneEmbeddingStore.add(embedding, segment);
        log.info("✅ 文档向量化完成，ID: {}", vectorId);
        
        // 5. 测试搜索
        String[] searchQueries = {
                "工作进度",
                "计划表",
                "任务",
                "负责人",
                "系统设计"
        };
        
        for (String query : searchQueries) {
            log.info("🔍 搜索关键词: {}", query);
            
            // 生成查询向量
            Embedding queryEmbedding = embeddingModel.embed(query).content();
            
            // 执行搜索
            EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
                    .queryEmbedding(queryEmbedding)
                    .maxResults(5)
                    .minScore(0.1)
                    .build();
            
            EmbeddingSearchResult<TextSegment> searchResult = luceneEmbeddingStore.search(searchRequest);
            List<EmbeddingMatch<TextSegment>> matches = searchResult.matches();
            
            log.info("📊 搜索结果: {} 个匹配", matches.size());
            
            if (!matches.isEmpty()) {
                for (int i = 0; i < Math.min(2, matches.size()); i++) {
                    EmbeddingMatch<TextSegment> match = matches.get(i);
                    log.info("📄 匹配 {}: 分数={}, 内容预览={}", 
                            i + 1, 
                            String.format("%.4f", match.score()),
                            match.embedded().text().substring(0, Math.min(100, match.embedded().text().length())));
                }
                
                // 验证至少找到一个相关结果
                boolean foundRelevant = matches.stream()
                        .anyMatch(match -> match.score() > 0.2);
                
                if (foundRelevant) {
                    log.info("✅ 关键词 '{}' 搜索成功，找到相关结果", query);
                } else {
                    log.warn("⚠️ 关键词 '{}' 搜索结果相关性较低", query);
                }
            } else {
                log.warn("⚠️ 关键词 '{}' 没有找到匹配结果", query);
            }
        }
    }

    @Test
    @Order(3)
    @DisplayName("3. 测试真实Excel文件")
    void testRealExcelFile() throws Exception {
        log.info("🧪 开始测试真实Excel文件");
        
        String realFilePath = "/Users/<USER>/Documents/工作进度表（工作计划表）1.xlsx";
        File realFile = new File(realFilePath);
        
        if (!realFile.exists()) {
            log.warn("⚠️ 真实Excel文件不存在，跳过测试: {}", realFilePath);
            return;
        }
        
        // 1. 读取真实文件
        byte[] realFileContent;
        try (FileInputStream fis = new FileInputStream(realFile)) {
            realFileContent = fis.readAllBytes();
        }
        
        log.info("📁 真实文件大小: {} bytes", realFileContent.length);
        
        // 2. 测试解析
        Document document = tikaParsingStrategy.parseDocument(realFileContent, realFile.getName());
        
        String content = document.text();
        log.info("📄 真实文件解析内容长度: {} 字符", content.length());
        log.info("📄 内容预览: {}", content.substring(0, Math.min(500, content.length())));
        
        // 3. 测试向量化
        Embedding embedding = embeddingModel.embed(content).content();
        log.info("📊 向量维度: {}", embedding.dimension());
        
        // 4. 测试搜索
        TextSegment segment = TextSegment.from(content);
        String vectorId = luceneEmbeddingStore.add(embedding, segment);
        
        EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
                .queryEmbedding(embeddingModel.embed("工作进度").content())
                .maxResults(5)
                .minScore(0.1)
                .build();
        
        EmbeddingSearchResult<TextSegment> searchResult = luceneEmbeddingStore.search(searchRequest);
        log.info("🔍 搜索结果: {} 个匹配", searchResult.matches().size());
        
        if (!searchResult.matches().isEmpty()) {
            EmbeddingMatch<TextSegment> bestMatch = searchResult.matches().get(0);
            log.info("🎯 最佳匹配分数: {}", String.format("%.4f", bestMatch.score()));
        }
        
        log.info("✅ 真实Excel文件测试完成");
    }

    @Test
    @Order(4)
    @DisplayName("4. 测试不同类型的Excel内容")
    void testDifferentExcelContent() throws Exception {
        log.info("🧪 开始测试不同类型的Excel内容");
        
        // 创建包含不同类型数据的Excel
        byte[] complexExcelContent = createComplexExcelFile();
        
        // 解析文档
        Document document = tikaParsingStrategy.parseDocument(complexExcelContent, "复杂数据表.xlsx");
        String content = document.text();
        
        log.info("📄 复杂Excel解析内容长度: {} 字符", content.length());
        log.info("📄 内容预览: {}", content.substring(0, Math.min(500, content.length())));
        
        // 测试是否包含各种类型的数据
        Assertions.assertTrue(content.contains("产品") || content.contains("销售") || content.contains("数量"), 
                "应包含产品销售相关内容");
        Assertions.assertTrue(content.contains("2024") || content.contains("1月") || content.contains("2月"), 
                "应包含日期相关内容");
        
        // 测试向量化和搜索
        Embedding embedding = embeddingModel.embed(content).content();
        TextSegment segment = TextSegment.from(content);
        String vectorId = luceneEmbeddingStore.add(embedding, segment);
        
        // 测试多个搜索查询
        String[] queries = {"产品销售", "销售数据", "月度报告", "业绩统计"};
        
        for (String query : queries) {
            EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
                    .queryEmbedding(embeddingModel.embed(query).content())
                    .maxResults(3)
                    .minScore(0.1)
                    .build();
            
            EmbeddingSearchResult<TextSegment> searchResult = luceneEmbeddingStore.search(searchRequest);
            log.info("🔍 查询 '{}' 找到 {} 个结果", query, searchResult.matches().size());
        }
        
        log.info("✅ 不同类型Excel内容测试完成");
    }

    /**
     * 创建测试Excel文件
     */
    private static byte[] createTestExcelFile() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("工作进度表");
        
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("任务名称");
        headerRow.createCell(1).setCellValue("负责人");
        headerRow.createCell(2).setCellValue("开始时间");
        headerRow.createCell(3).setCellValue("结束时间");
        headerRow.createCell(4).setCellValue("进度");
        headerRow.createCell(5).setCellValue("状态");
        
        // 创建数据行
        String[][] data = {
                {"系统设计", "张三", "2024-01-01", "2024-01-15", "100%", "已完成"},
                {"数据库设计", "李四", "2024-01-10", "2024-01-25", "90%", "进行中"},
                {"前端开发", "王五", "2024-01-20", "2024-02-10", "60%", "进行中"},
                {"后端开发", "赵六", "2024-01-25", "2024-02-15", "40%", "进行中"},
                {"测试工作", "钱七", "2024-02-01", "2024-02-20", "20%", "计划中"},
                {"部署上线", "孙八", "2024-02-15", "2024-02-25", "0%", "计划中"}
        };
        
        for (int i = 0; i < data.length; i++) {
            Row row = sheet.createRow(i + 1);
            for (int j = 0; j < data[i].length; j++) {
                row.createCell(j).setCellValue(data[i][j]);
            }
        }
        
        // 转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();
        
        return outputStream.toByteArray();
    }

    /**
     * 创建复杂的Excel文件
     */
    private static byte[] createComplexExcelFile() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("产品销售数据");
        
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("产品名称");
        headerRow.createCell(1).setCellValue("销售数量");
        headerRow.createCell(2).setCellValue("单价");
        headerRow.createCell(3).setCellValue("销售额");
        headerRow.createCell(4).setCellValue("销售月份");
        
        // 创建数据行
        Object[][] data = {
                {"笔记本电脑", 150, 5999.0, 899850.0, "2024年1月"},
                {"台式机", 80, 3999.0, 319920.0, "2024年1月"},
                {"显示器", 200, 1299.0, 259800.0, "2024年1月"},
                {"键盘", 300, 199.0, 59700.0, "2024年2月"},
                {"鼠标", 350, 99.0, 34650.0, "2024年2月"},
                {"音响", 120, 599.0, 71880.0, "2024年2月"}
        };
        
        for (int i = 0; i < data.length; i++) {
            Row row = sheet.createRow(i + 1);
            for (int j = 0; j < data[i].length; j++) {
                Cell cell = row.createCell(j);
                Object value = data[i][j];
                if (value instanceof String) {
                    cell.setCellValue((String) value);
                } else if (value instanceof Integer) {
                    cell.setCellValue((Integer) value);
                } else if (value instanceof Double) {
                    cell.setCellValue((Double) value);
                }
            }
        }
        
        // 转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();
        
        return outputStream.toByteArray();
    }
}
