package com.zibbava.edgemind.cortex.store;

import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.TokenStream;
import org.apache.lucene.analysis.cn.smart.SmartChineseAnalyzer;
import org.apache.lucene.analysis.standard.StandardAnalyzer;
import org.apache.lucene.analysis.tokenattributes.CharTermAttribute;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 测试中文分析器的效果
 */
public class ChineseAnalyzerTest {

    @Test
    @DisplayName("比较StandardAnalyzer和SmartChineseAnalyzer的中文分词效果")
    public void testChineseAnalyzers() throws IOException {
        String[] testTexts = {
            "青岛啤酒销售合同",
            "中华人民共和国",
            "北京大学计算机科学与技术学院",
            "人工智能和机器学习技术",
            "这是一个测试文档的内容"
        };

        System.out.println("=== 中文分析器对比测试 ===\n");

        for (String text : testTexts) {
            System.out.println("原文: " + text);
            
            // 测试StandardAnalyzer
            List<String> standardTokens = analyzeText(new StandardAnalyzer(), text);
            System.out.println("StandardAnalyzer: " + standardTokens);
            
            // 测试SmartChineseAnalyzer
            List<String> smartTokens = analyzeText(new SmartChineseAnalyzer(), text);
            System.out.println("SmartChineseAnalyzer: " + smartTokens);
            
            System.out.println("---");
        }
    }

    private List<String> analyzeText(Analyzer analyzer, String text) throws IOException {
        List<String> tokens = new ArrayList<>();
        
        try (TokenStream tokenStream = analyzer.tokenStream("content", text)) {
            CharTermAttribute termAttribute = tokenStream.addAttribute(CharTermAttribute.class);
            tokenStream.reset();
            
            while (tokenStream.incrementToken()) {
                tokens.add(termAttribute.toString());
            }
            
            tokenStream.end();
        }
        
        return tokens;
    }
}
