package com.zibbava.edgemind.cortex.store;

import dev.langchain4j.store.embedding.filter.Filter;
import dev.langchain4j.store.embedding.filter.MetadataFilterBuilder;
import org.junit.jupiter.api.Test;

/**
 * 测试langchain4j中可用的过滤器类型
 */
public class FilterTypeTest {

    @Test
    public void testAvailableFilterTypes() {
        try {
            // 测试 isEqualTo
            Filter equalToFilter = MetadataFilterBuilder.metadataKey("test").isEqualTo("value");
            System.out.println("isEqualTo filter type: " + equalToFilter.getClass().getSimpleName());

            // 测试 containsString
            try {
                Filter containsFilter = MetadataFilterBuilder.metadataKey("test").containsString("value");
                System.out.println("containsString filter type: " + containsFilter.getClass().getSimpleName());
            } catch (Exception e) {
                System.out.println("containsString method not available: " + e.getMessage());
            }

            // 测试其他可能的方法
            try {
                Filter inFilter = MetadataFilterBuilder.metadataKey("test").isIn("value1", "value2");
                System.out.println("isIn filter type: " + inFilter.getClass().getSimpleName());
            } catch (Exception e) {
                System.out.println("isIn method not available: " + e.getMessage());
            }

            // 测试 isNotEqualTo
            try {
                Filter notEqualToFilter = MetadataFilterBuilder.metadataKey("test").isNotEqualTo("value");
                System.out.println("isNotEqualTo filter type: " + notEqualToFilter.getClass().getSimpleName());
            } catch (Exception e) {
                System.out.println("isNotEqualTo method not available: " + e.getMessage());
            }

            // 测试逻辑组合
            try {
                Filter andFilter = MetadataFilterBuilder.metadataKey("test1").isEqualTo("value1")
                    .and(MetadataFilterBuilder.metadataKey("test2").isEqualTo("value2"));
                System.out.println("and filter type: " + andFilter.getClass().getSimpleName());
            } catch (Exception e) {
                System.out.println("and method not available: " + e.getMessage());
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
