### **《解构本地AI知识库》(4/7)：从“大海捞针”到“精准制导”：RAG检索的两次进化**

**（导语）**

经过前几篇的铺垫，我们已经为AI知识库准备好了高质量的“食材”（经过智能切分的文本块）。现在，我们面临一个核心问题：当用户提问时，系统如何从成千上万的文本块中，快速且准确地找到最相关的那几个？这就是RAG中“R”（Retrieval，检索）的使命。今天，我们将探讨检索技术经历的两次关键进化。

---

#### **第一次进化：向量检索——让AI“理解”语义**

传统的关键词搜索，只能匹配字面上的内容。比如你搜“苹果”，它无法知道你找的是水果还是手机。为了解决这个问题，现代RAG系统引入了**向量检索**。

**1. 什么是向量检索？**

在数据处理阶段，我们用一个“嵌入模型”（Embedding Model）将每个文本块都转换成了一个由几百甚至上千个数字组成的“向量”。你可以把这个向量，想象成这个文本块在多维空间中的一个“语义坐标”。

- 意思相近的文本块，它们的“坐标”在空间中也更接近。
- 比如，“公司的财务状况”和“企业的盈利能力”这两个文本块，它们的向量坐标就会离得很近。

当用户提问时，系统同样会将问题转换成一个向量，然后去向量数据库中，计算所有文本块的坐标与问题坐标之间的“距离”，找出距离最近的几个文本块。

**2. 向量检索的优势与局限**

- **优势**：它超越了字面匹配，能够真正理解用户的“意图”，找到语义上相关的内容。这是RAG技术的基石。
- **局限**：“最相似”不等于“最相关”。向量检索有时会找回一些虽然包含相似关键词，但并非用户真正想要的答案。例如，用户问“A产品的缺点”，它可能会找回一篇仅仅是提到了“A产品”和“缺点”这两个词，但实际在讨论B产品的文章。

---

#### **第二次进化：重排模型——优中选优的“精算师”**

为了弥补向量检索的不足，我们团队在RAG流程中引入了第二次进化——**重排模型（Reranker）**。

**1. 什么是重排？**

如果说向量检索是“广撒网”的海选，那么重排就是“精挑细选”的复赛。

它的工作流程是：

1.  **初步召回（Recall）**：我们先通过向量检索，快速地从海量文档中找出可能相关的、范围较大的一个候选集，比如20个文本块。
2.  **精准重排（Rerank）**：然后，我们不再使用计算成本较低的向量相似度，而是启动一个更强大、更“昂贵”的**交叉编码器（Cross-encoder）重排模型**。这个模型会把用户的问题，与每一个候选文本块**成对地**进行深度分析，然后给出一个更精准的“相关性得分”。
3.  **最终筛选**：最后，我们根据这个精准得分，从20个候选块中，选出得分最高的3到5个，再交给大模型去生成最终答案。

**2. 为什么重排效果更好？**

重排模型之所以更精准，是因为它不仅仅是比较两个“坐标”的距离，而是对问题和候选文本块的内部结构、词语关系进行深度的交叉分析。它更能理解细微的语境差异，从而判断出哪个文本块是真正回答了用户的问题。

---

**（结语与预告）**

通过“向量检索”+“重排模型”这两次进化，我们的RAG系统实现了从“能找到”到“找得准”的飞跃。这确保了我们最终交给大模型的，是经过层层筛选的、最高质量的“参考资料”。

现在，有了最精准的资料，如何让大模型更好地理解它们，并说出我们想要的答案呢？在下一篇文章中，我们将进入生成环节，**聊聊如何通过提示词工程（Prompt Engineering），与AI进行高效沟通**。

**关注我们，持续解锁《解构本地AI知识库》系列，下一篇更精彩！**

#解构本地AI知识库 #RAG #向量检索 #重排 #Reranker