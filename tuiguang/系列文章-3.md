### **《解构本地AI知识库》(3/7)：精准的第一道门：我们如何处理“文本切块”**

**（导语）**

在上一篇中，我们了解了RAG的“开卷考试”模型。今天，我们来聊聊这场考试的第一个，也是最容易被忽视的关键环节——**考前资料的整理，即“文本切块”（Text Splitting）**。如果资料整理得乱七八糟，再聪明的考生也难以发挥。可以说，文本切块的质量，直接决定了RAG系统精准度的下限。

---

#### **一、为什么不能“一刀切”？——粗暴切分的陷阱**

最简单的切块方法，就是按固定字数或字符数来切分，比如每500个字切成一块。这种方法实现起来最容易，但效果也最差。

想象一下，如果一句话“我们的产品最大的缺点是价格太高”，正好在“缺点是”和“价格太高”之间被切开了，会发生什么？

- **语义割裂**：上半句的文本块失去了核心信息，下半句的文本块也变得没头没尾。
- **检索失效**：当用户提问“产品有什么缺点”时，AI可能因为找不到一个同时包含“产品”和“缺点”的完整语义块，而无法检索到正确答案。

这种“一刀切”的方式，会产生大量毫无价值的“信息碎片”，严重干扰后续的检索和生成环节。

---

#### **二、我们的核心策略：面向“语义”的智能切分**

为了解决这个问题，我们团队在开发中，放弃了简单的固定长度切分，而是设计了一套**基于文档结构和自然语言处理的“智能语义切块”引擎**。

我们的核心理念是：**切分出的每一个文本块（Chunk），都应该是一个相对独立、完整的语义单元。**

为此，我们采取了分层处理的策略：

**1. 结构化优先：尊重原文的“呼吸”**

我们会优先识别文档本身就存在的结构化标记，并以此为边界进行切分。这就像是按照作者写文章时自然形成的“段落”来呼吸。

- **对于Markdown文件**：我们会优先根据标题（#、##）、列表（-、*）、代码块（```）等标记来进行切分。一个二级标题下的所有内容，很可能就是一个完整的语义单元。
- **对于PDF和Word文件**：我们会利用工具解析出文档的段落、标题层级、列表等信息，模拟Markdown的结构化切分。

**2. 语义边界探测：让句子保持完整**

在结构化标记不明显的情况下（例如一个很长的大段落），我们就需要动用NLP技术来寻找“语义边界”。

- **递归切分**：我们会尝试按顺序，用不同的“分隔符”去切分文本。我们定义了一个分隔符的优先级列表，比如 `\n\n` (换段) > `\n` (换行) > `。` (句号) > ` ` (空格)。系统会先尝试用最高优先级的符号切分，如果切出来的块还太大，再在那个大块内部，用次一级优先级的符号继续切分，直到块的大小合适为止。
- **句子完整性保护**：我们引入了主流的NLP句子分割模型，确保切分时不会将一个完整的句子从中间断开。

**3. 考虑重叠（Overlap）：为上下文“留一手”**

即便我们保证了每个块的语义完整，但块与块之间的联系也很重要。为了避免两个关联紧密的句子被分到不同的块中，我们在切块时会设置一个“重叠区”。

比如，每个块大小为500字符，我们设置100字符的重叠。那么第二个块的开头，会包含第一个块结尾的100个字符。这样，无论用户的查询命中哪个块，AI都有机会看到其紧邻的上下文信息。

---

**（结语与预告）**

文本切块，看似是RAG流程中最基础的一环，实则充满了细节与权衡。它就像是为AI准备“食材”的过程，食材处理得好坏，直接影响最终“菜品”的质量。我们坚信，在这种“看不见”的地方下足功夫，才是提升产品核心竞争力的关键。

当“食材”准备好后，下一步就是如何让AI能快速地找到它想要的。在下一篇文章中，我们将深入RAG的核心引擎——**聊聊“检索”的两次技术进化**。

**关注我们，持续解锁《解构本地AI知识库》系列，下一篇更精彩！**

#解构本地AI知识库 #RAG #文本切块 #NLP #本地AI