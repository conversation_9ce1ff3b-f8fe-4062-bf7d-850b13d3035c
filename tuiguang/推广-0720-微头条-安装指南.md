之前饱受诟病的Windows一键安装包，我们终于搞定了！

非常抱歉，因为要适配不同Windows系统（Win10/Win11）的兼容性，之前的安装过程确实劝退了不少朋友。但现在，我们重构了安装脚本，绝大部分问题都已解决，安装体验丝滑了很多！

如果你之前安装失败，请一定再试一次这个新版本！下面是详细的“避坑”安装指南：

**第一步：下载并以管理员身份运行**
下载最新的安装包后，务必【右键】->【以管理员身份运行】。这是因为安装程序需要自动为你开启Windows虚拟化平台等系统功能，没有管理员权限会失败。

**[此处应有截图：右键点击安装包，并高亮“以管理员身份运行”选项]**

> **强烈建议**：为避免被误杀，可以暂时关闭360、火绒等安全软件，安装成功后再开启。

**第二步：全自动安装与重启**
程序会自动检查并开启所需功能，这个过程中电脑可能会【重启一次】。别担心，这是正常现象。重启后，安装程序会自动继续执行，开始安装Docker和Ollama环境。

**第三步：下载核心大模型**
安装完成后，系统会自动打开。请先进入【模型中心】页面，根据你的电脑配置，下载一个基础对话模型（如Qwen2-1.7B或7B），这是让AI大脑思考的基础。

**[此处应有截图：模型中心的界面，用箭头指向一个推荐下载的模型]**

**第四步：设置你的知识库路径**
然后，进入【系统设置】页面，找到知识库设置，选择一个你电脑上用来存放文档的【本地文件夹路径】。AI以后就会读取这里的文件来回答你的问题。

**[此处应有截图：系统设置界面，高亮显示设置知识库路径的区域]**

**第五步：开始使用！**
做完以上步骤，恭喜你！你的专属本地AI知识库已经准备就绪。现在，去和你的“数字大脑”对话吧！

再一次感谢大家的耐心和反馈，我们一直在努力让部署和使用变得更简单。快去试试吧！

#本地AI #安装指南 #Windows #端智AI助手 #效率工具 #教程