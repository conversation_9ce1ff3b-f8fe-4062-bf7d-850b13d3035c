### **标题：我做的本地AI知识库火了，后台私信爆炸，今天再揭秘3个“反常识”设计！**

#### **（正文）**

大家好，还是我，那个坚持做本地AI知识库的开发者。

前几天发了篇文章，聊了聊我为了解决“文件上传难、环境配置烦、云服务贵”这三大痛点，做的“一键同步”和“本地模型中心”功能。

没想到文章火了，一天时间，后台收到了几百条私信。有鼓励支持的，有提需求的，还有不少人上来就问：“**你这个知识库，到底跟别人家的有啥不一样？**”

![](https://p3-sign.toutiaoimg.com/tos-cn-i-qvj2lq49k0/e1f2d3c4b5a6f7e8d9c0a1b2c3d4e5f6~tplv-tt-large.image?x-expires=1989292800&x-signature=...)(这里可以配一张后台私信列表的截图，打上马赛克，显得真实)

说实话，市面上的AI知识库，功能看起来都大同小异。但我从一开始就想做个“异类”，**专门解决那些被大厂忽略，但用户又实实在在头疼的问题**。

今天，我就再揭秘我们产品里3个看起来“反常识”，但用过都说爽的设计。

---

### **1. 反常识设计一：我们没有客户端，一个浏览器就够了！**

现在很多软件都喜欢搞个“全家桶”，PC端、Mac端、手机端，让你每个设备都装一遍。

**我偏不。**

我的产品，**没有任何客户端软件**。你只需要一个浏览器。

我们用了先进的B/S架构，把所有功能都搬到了网页上。这意味着什么？

*   **公司电脑用Windows，自己电脑用Mac？** 没问题，打开浏览器就能无缝切换，所有数据和习惯都一样。
*   **IT部门再也不用挨个给同事装软件、催更新了。** 只需要维护好服务端，全公司的人打开网页就能用，管理成本几乎为零。
*   **人在外面，临时要查个资料？** 只要能连上公司内网，用平板甚至手机浏览器，都能随时访问你的知识库。

这个设计，对于追求轻便、跨平台办公的团队和个人来说，体验是颠覆性的。我们追求的不是“占领你的桌面”，而是“随时随地为你服务”。

![](https://p3-sign.toutiaoimg.com/tos-cn-i-qvj2lq49k0/a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6~tplv-tt-large.image?x-expires=1989292800&x-signature=...)(配一张产品在浏览器中运行的截图，最好能体现跨平台，比如Mac和Windows电脑同时打开)

---

### **2. 反常识设计二：你的Office文档，就该用Office的方式打开！**

我发现一个很奇怪的现象：几乎所有的知识库，在处理Word、PPT、Excel这些我们最常用的文件时，都会出问题。

要么是把它们强行转成纯文本，格式、图片全丢了；要么是用自己简陋的渲染引擎，打开后排版错乱，表格变形，根本没法看。

**这简直是反人类！**

所以，我做了一个“固执”的决定：**深度集成了专业的OnlyOffice引擎**。

当你把一个Word文档存入我们的知识库后，点击它，会直接在浏览器里弹出一个你无比熟悉的、**和微软Office几乎一模一样的界面**。

*   **原文什么样，你看就是什么样。** 字体、颜色、表格、图片，100%原汁原味，再也不会有错乱的烦恼。
*   **AI还能帮你写Word！** 你可以让AI帮你生成一篇周报或会议纪要，直接输出成标准的Word格式，在线预览没问题后，下载下来就能直接用。

我们不创造格式，我们只“尊重”格式。这才是对用户文档最基本的负责。

![](https://p3-sign.toutiaoimg.com/tos-cn-i-qvj2lq49k0/9c8b7a6d7e5f4a3e8f9c0a1b2c3d4e5f~tplv-tt-large.image?x-expires=1989292800&x-signature=...)(配一张在浏览器里完美打开Word或Excel文档的产品截图，突出格式的精准还原)

---

### **3. 反常识设计三：你的文件，不应该被“绑架”！**

很多AI产品，你把文件传上去，这些文件就成了它的“私有财产”。你想挪个地儿？想用自己公司的NAS存储？对不起，不支持。

**这不合理，你的数据，凭什么不能自己做主？**

所以，我们从架构层面，就把**应用和文档存储彻底分开了**。

这就像“房产证”和“房子”分离一样。我们的AI系统是“房子”，而你的文件（房产证）放在哪，完全由你决定。

*   你可以把海量文档放在**公司现有的NAS网络存储**上，省下一大笔额外的硬盘钱。
*   你可以把系统部署在云服务器，但把文件存在**更便宜的对象存储**上，成本最优。
*   当文件多到爆炸时，你只需要**单独升级你的硬盘或NAS**，AI系统本身完全不受影响，扩展性极强。

我们把数据的所有权和管理权，真正还给了用户。这在一个越来越封闭的互联网环境里，可能有点“不合时宜”，但我觉得，这才是对用户最长久的尊重。

---

### **写在最后**

做产品，最怕的就是“人云亦云”。别人做什么，我就做什么。

我更喜欢的方式是，**回到原点，去观察用户真正在被什么问题困扰**，然后用最直接、甚至有点“笨”的方法去解决它。

这3个“反常识”的设计，就是这种理念下的产物。它们可能不够酷炫，但每一个，都实实在在地解决了一个核心痛点。

**除了这些，你觉得一个好用的AI知识库，还应该具备哪些特性？欢迎在评论区继续轰炸我！**