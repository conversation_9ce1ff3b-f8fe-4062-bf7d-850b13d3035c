

import platform
import time

def ding(title="提醒", message="时间到！"):
    """
    发送一个桌面通知并播放提示音。
    """
    try:
        # 尝试使用 plyer 发送跨平台通知
        from plyer import notification
        notification.notify(
            title=title,
            message=message,
            app_name='Ding',
            timeout=10  # 通知显示10秒
        )
    except ImportError:
        print("警告：'plyer' 库未安装，无法发送桌面通知。")
        print(f"标题: {title}\n内容: {message}")

    # 根据操作系统播放声音
    os_type = platform.system()
    if os_type == "Windows":
        try:
            import winsound
            # 播放一个 440Hz 的声音，持续 500ms
            winsound.Beep(440, 500)
        except ImportError:
            # winsound 是标准库，通常不会失败
            pass
    elif os_type == "Darwin": # macOS
        try:
            import os
            # 使用系统命令播放默认提示音
            os.system('afplay /System/Library/Sounds/Glass.aiff')
        except Exception as e:
            print(f"在macOS上播放声音失败: {e}")
    else: # Linux
        try:
            import os
            # 尝试使用 aplay 或 paplay 播放声音
            if os.system('which aplay > /dev/null 2>&1') == 0:
                # 创建一个简单的 beep 音频文件（如果不存在）
                # 这里我们简化处理，仅打印提示
                print("\a", end='', flush=True)
            elif os.system('which paplay > /dev/null 2>&1') == 0:
                os.system('paplay /usr/share/sounds/freedesktop/stereo/bell.oga')
            else:
                # 作为最后的备选方案，打印 BEL 字符
                print("\a", end='', flush=True)
        except Exception as e:
            print(f"在Linux上播放声音失败: {e}")


if __name__ == "__main__":
    print("脚本开始运行...")
    ding(title="这是一个测试通知", message="您好，这是一个来自 ding.py 的提醒！")
    print("通知已发送。")

