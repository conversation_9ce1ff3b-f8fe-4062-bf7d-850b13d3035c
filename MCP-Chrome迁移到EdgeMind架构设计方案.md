# MCP-Chrome迁移到EdgeMind架构设计方案

## 1. 迁移可行性分析

### 1.1 技术栈兼容性
- ✅ **Java后端**: edgemind-cortex已有完整的Java Spring Boot架构
- ✅ **AI集成**: 已集成ollama+langchain4j，支持function calling
- ✅ **WebSocket**: 可替代mcp-chrome的原生消息通信
- ✅ **浏览器插件**: edgemind-plugin可承载必要的工具调用功能
- ✅ **数据存储**: H2数据库+Lucene向量库可替代mcp-chrome的本地存储

### 1.2 核心优势
1. **统一架构**: 所有AI逻辑集中在cortex后端，便于管理和扩展
2. **更好的性能**: Java后端处理能力强于Node.js原生服务器
3. **企业级特性**: 支持用户管理、权限控制、数据持久化
4. **简化部署**: 减少组件依赖，降低部署复杂度

## 2. 整体架构设计（基于/wkg上下文）

### 2.1 系统架构图

```mermaid
graph TB
    subgraph "AI助手层"
        A[用户自然语言指令]
    end

    subgraph "EdgeMind-Cortex后端 (/wkg)"
        B[UnifiedChatController<br/>/wkg/api/unified-chat/stream]
        C[BrowserAutomationStrategy]
        D[BrowserToolProvider]
        E[WebSocketManager<br/>/wkg/browser-automation]
        F[OllamaUtils + LangChain4j]
        G[H2数据库]
        H[Lucene向量库]
    end

    subgraph "EdgeMind-Plugin浏览器插件"
        I[Background Script]
        J[Content Scripts]
        K[Inject Scripts]
        L[WebSocket Client<br/>ws://localhost:8080/wkg/browser-automation]
    end

    subgraph "浏览器APIs"
        M[Chrome APIs]
        N[DOM操作]
        O[网络请求]
    end

    A --> B
    B --> C
    C --> D
    D --> F
    F --> E
    E <--> L
    L --> I
    I --> J
    I --> K
    J --> N
    K --> N
    I --> M
    I --> O

    C --> G
    C --> H
```

### 2.2 核心组件设计

#### 2.2.1 BrowserAutomationStrategy (集成到现有策略体系)
```java
@Component
@Slf4j
public class BrowserAutomationStrategy implements ChatStrategy {

    private final BrowserToolProvider browserToolProvider;
    private final WebSocketManager webSocketManager;
    private final ChatService chatService; // 复用现有服务

    @Override
    public boolean supports(ChatContext context) {
        // 检测浏览器相关意图
        String prompt = context.getPrompt().toLowerCase();
        return prompt.contains("浏览器") ||
               prompt.contains("网页") ||
               prompt.contains("打开") ||
               prompt.contains("点击") ||
               prompt.contains("搜索") ||
               detectBrowserIntent(prompt);
    }

    @Override
    public Flux<String> executeChat(ChatContext context) {
        try {
            log.info("🌐 开始执行浏览器自动化对话: conversationId={}, model={}",
                    context.getConversationId(), context.getModelName());

            // 1. 构建消息历史（复用现有逻辑）
            List<OllamaMessage> messages = buildMessageHistory(context);

            // 2. 创建带浏览器工具的Ollama请求
            OllamaChatRequest chatRequest = OllamaChatRequest.builder()
                .model(context.getModelName())
                .messages(messages)
                .stream(true)
                .sessionId("browser_" + context.getUserId() + "_" + context.getConversationId())
                .toolProvider(browserToolProvider) // 关键：添加浏览器工具
                .build();

            // 3. 执行流式聊天（复用现有OllamaUtils）
            return OllamaUtils.streamChat(chatRequest)
                    .handle((String rawToken, SynchronousSink<String> sink) ->
                        chatService.handelSSE(
                            context.getMainContentBuilder(),
                            context.getThinkingContentBuilder(),
                            rawToken,
                            sink,
                            context.getIsInsideThinkingBlock()
                        ))
                    .doOnComplete(() -> {
                        updateConversationWithQuality(context);
                        log.info("✅ 浏览器自动化对话完成: conversationId={}", context.getConversationId());
                    })
                    .doOnError(e -> log.error("❌ 浏览器自动化对话出错: {}", e.getMessage(), e));

        } catch (Exception e) {
            log.error("❌ 执行浏览器自动化对话出错: {}", e.getMessage(), e);
            return Flux.error(e);
        }
    }

    // 复用现有策略的通用方法
    private List<OllamaMessage> buildMessageHistory(ChatContext context) {
        // 复用NormalChatStrategy的消息构建逻辑
        // ...
    }

    private void updateConversationWithQuality(ChatContext context) {
        // 复用现有的会话更新逻辑
        // ...
    }
}
```

#### 2.2.2 BrowserToolProvider (Java)
```java
@Component
public class BrowserToolProvider implements ToolProvider {
    
    private final WebSocketManager webSocketManager;
    
    @Override
    public ToolProviderResult provideTools(ToolProviderRequest request) {
        List<ToolSpecification> tools = Arrays.asList(
            // 浏览器管理工具
            createNavigateTool(),
            createScreenshotTool(),
            createClickTool(),
            createFillTool(),
            createGetContentTool(),
            // 网络监控工具
            createNetworkCaptureTool(),
            // 语义搜索工具
            createSemanticSearchTool()
        );
        
        return ToolProviderResult.builder()
            .tools(tools)
            .toolExecutor(this::executeTool)
            .build();
    }
    
    private String executeTool(String toolName, String arguments) {
        try {
            // 解析工具参数
            BrowserToolRequest toolRequest = parseToolRequest(toolName, arguments);
            
            // 通过WebSocket发送到浏览器插件
            BrowserToolResponse response = webSocketManager
                .sendToolRequest(toolRequest)
                .block(Duration.ofSeconds(30));
                
            return response.toJson();
        } catch (Exception e) {
            log.error("执行浏览器工具失败: {}", e.getMessage(), e);
            return createErrorResponse(e.getMessage());
        }
    }
}
```

#### 2.2.3 WebSocketManager (Java)
```java
@Component
@Slf4j
public class WebSocketManager {
    
    private final Map<String, WebSocketSession> activeSessions = new ConcurrentHashMap<>();
    private final Map<String, CompletableFuture<BrowserToolResponse>> pendingRequests = new ConcurrentHashMap<>();
    
    @EventListener
    public void handleWebSocketConnect(SessionConnectedEvent event) {
        String sessionId = event.getMessage().getHeaders().get("sessionId").toString();
        WebSocketSession session = event.getMessage().getHeaders().get("session");
        activeSessions.put(sessionId, session);
        log.info("浏览器插件连接: {}", sessionId);
    }
    
    public Mono<BrowserToolResponse> sendToolRequest(BrowserToolRequest request) {
        return Mono.fromFuture(() -> {
            String requestId = UUID.randomUUID().toString();
            CompletableFuture<BrowserToolResponse> future = new CompletableFuture<>();
            
            pendingRequests.put(requestId, future);
            
            // 发送到所有活跃的浏览器插件会话
            WebSocketMessage message = new WebSocketMessage(
                "TOOL_CALL", 
                requestId, 
                request
            );
            
            activeSessions.values().forEach(session -> {
                try {
                    session.sendMessage(new TextMessage(message.toJson()));
                } catch (Exception e) {
                    log.error("发送WebSocket消息失败", e);
                }
            });
            
            return future;
        });
    }
    
    @MessageMapping("/tool-response")
    public void handleToolResponse(BrowserToolResponse response) {
        CompletableFuture<BrowserToolResponse> future = pendingRequests.remove(response.getRequestId());
        if (future != null) {
            future.complete(response);
        }
    }
}
```

## 3. 浏览器插件简化设计

### 3.1 EdgeMind-Plugin架构
```javascript
// background.js - 主要协调器
class EdgeMindBackground {
    constructor() {
        this.websocket = null;
        this.toolExecutors = new Map();
        this.initializeTools();
        this.connectToServer();
    }
    
    initializeTools() {
        // 注册核心工具执行器
        this.toolExecutors.set('navigate', new NavigateTool());
        this.toolExecutors.set('click', new ClickTool());
        this.toolExecutors.set('fill', new FillTool());
        this.toolExecutors.set('screenshot', new ScreenshotTool());
        this.toolExecutors.set('getContent', new GetContentTool());
        this.toolExecutors.set('networkCapture', new NetworkCaptureTool());
    }
    
    connectToServer() {
        this.websocket = new WebSocket('ws://localhost:8080/browser-automation');
        
        this.websocket.onmessage = (event) => {
            const message = JSON.parse(event.data);
            if (message.type === 'TOOL_CALL') {
                this.handleToolCall(message);
            }
        };
    }
    
    async handleToolCall(message) {
        const { requestId, toolName, arguments: args } = message;
        
        try {
            const executor = this.toolExecutors.get(toolName);
            if (!executor) {
                throw new Error(`Unknown tool: ${toolName}`);
            }
            
            const result = await executor.execute(args);
            
            this.websocket.send(JSON.stringify({
                type: 'TOOL_RESPONSE',
                requestId,
                success: true,
                data: result
            }));
        } catch (error) {
            this.websocket.send(JSON.stringify({
                type: 'TOOL_RESPONSE',
                requestId,
                success: false,
                error: error.message
            }));
        }
    }
}

new EdgeMindBackground();
```

### 3.2 工具执行器示例
```javascript
// tools/ClickTool.js
class ClickTool {
    async execute(args) {
        const { selector, coordinates, waitForNavigation } = args;
        
        // 获取当前活跃标签页
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (!tab?.id) {
            throw new Error('No active tab found');
        }
        
        // 注入点击脚本
        await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            files: ['scripts/click-helper.js']
        });
        
        // 发送点击指令
        const result = await chrome.tabs.sendMessage(tab.id, {
            action: 'CLICK_ELEMENT',
            selector,
            coordinates,
            waitForNavigation
        });
        
        return {
            success: true,
            elementInfo: result.elementInfo,
            navigationOccurred: result.navigationOccurred
        };
    }
}
```

## 4. 数据流程设计

### 4.1 完整的工具调用流程（基于/wkg路径）
```
用户指令: "打开百度搜索qq邮箱"
    ↓
UnifiedChatController接收请求 (/wkg/api/unified-chat/stream)
    ↓
BrowserAutomationStrategy处理（集成到现有策略管理器）
    ↓
构建包含浏览器工具的Ollama请求
    ↓
AI解析指令，生成function calling序列:
1. browser_navigate(url: "https://www.baidu.com")
2. browser_fill(selector: "#kw", value: "qq邮箱")
3. browser_click(selector: "#su")
    ↓
BrowserToolProvider执行工具调用
    ↓
WebSocketManager通过/wkg/browser-automation发送到浏览器插件
    ↓
EdgeMind-Plugin连接ws://localhost:8080/wkg/browser-automation
    ↓
EdgeMind-Plugin执行浏览器操作
    ↓
返回执行结果到Java后端
    ↓
AI整合结果，生成最终响应
    ↓
通过SSE流式返回给用户 (/wkg/api/unified-chat/stream)
```

### 4.2 错误处理机制
1. **超时处理**: WebSocket请求30秒超时
2. **重试机制**: 关键操作失败时自动重试3次
3. **降级策略**: 插件离线时提示用户手动操作
4. **日志记录**: 完整的操作日志便于调试

## 5. 实现优先级

### Phase 1: 核心架构 (2周)
- [ ] BrowserAutomationStrategy基础实现
- [ ] WebSocket通信机制
- [ ] 基础工具Provider (navigate, click, fill)
- [ ] EdgeMind-Plugin WebSocket客户端

### Phase 2: 工具扩展 (2周)  
- [ ] 截图工具
- [ ] 内容提取工具
- [ ] 网络监控工具
- [ ] 语义搜索集成

### Phase 3: 优化增强 (1周)
- [ ] 错误处理完善
- [ ] 性能优化
- [ ] 用户界面集成
- [ ] 文档和测试

这个架构设计充分利用了edgemind-cortex的现有能力，同时保持了mcp-chrome的核心功能，实现了更好的集成度和可维护性。

## 6. 详细技术实现

### 6.1 Java后端核心代码结构（集成到现有/wkg架构）

```
edgemind-cortex/src/main/java/com/zibbava/edgemind/cortex/
├── browser/                           # 浏览器自动化模块（新增）
│   ├── strategy/
│   │   └── BrowserAutomationStrategy.java  # 集成到现有strategy包
│   ├── tools/
│   │   ├── BrowserToolProvider.java
│   │   ├── BrowserToolRequest.java
│   │   ├── BrowserToolResponse.java
│   │   └── tools/
│   │       ├── NavigateTool.java
│   │       ├── ClickTool.java
│   │       ├── FillTool.java
│   │       ├── ScreenshotTool.java
│   │       └── ContentTool.java
│   ├── websocket/
│   │   ├── WebSocketManager.java
│   │   ├── BrowserWebSocketHandler.java
│   │   └── WebSocketConfig.java        # 配置/wkg路径
│   └── service/
│       └── BrowserAutomationService.java
├── controller/
│   └── UnifiedChatController.java      # 现有控制器，无需修改
└── service/strategy/                   # 现有策略包
    ├── NormalChatStrategy.java         # 现有
    ├── KnowledgeChatStrategy.java      # 现有
    └── BrowserAutomationStrategy.java  # 新增浏览器策略
```

### 6.2 WebSocket配置实现（考虑/wkg上下文路径）

```java
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Autowired
    private BrowserWebSocketHandler browserWebSocketHandler;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 注意：需要考虑/wkg上下文路径
        registry.addHandler(browserWebSocketHandler, "/wkg/browser-automation")
                .setAllowedOrigins("*") // 生产环境需要限制
                .withSockJS(); // 支持SockJS降级
    }
}

@Component
@Slf4j
public class BrowserWebSocketHandler extends TextWebSocketHandler {

    private final WebSocketManager webSocketManager;

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = extractSessionId(session);
        webSocketManager.registerSession(sessionId, session);
        log.info("浏览器插件连接建立: {}", sessionId);
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        try {
            BrowserMessage browserMessage = JsonUtils.fromJson(message.getPayload(), BrowserMessage.class);
            webSocketManager.handleMessage(session, browserMessage);
        } catch (Exception e) {
            log.error("处理WebSocket消息失败", e);
            session.sendMessage(new TextMessage(createErrorMessage(e.getMessage())));
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        String sessionId = extractSessionId(session);
        webSocketManager.unregisterSession(sessionId);
        log.info("浏览器插件连接关闭: {}", sessionId);
    }
}
```

### 6.3 工具定义和执行

```java
@Component
public class BrowserToolProvider implements ToolProvider {

    private final WebSocketManager webSocketManager;
    private final BrowserAutomationService browserService;

    @Override
    public ToolProviderResult provideTools(ToolProviderRequest request) {
        return ToolProviderResult.builder()
            .tools(createBrowserTools())
            .toolExecutor(this::executeTool)
            .build();
    }

    private List<ToolSpecification> createBrowserTools() {
        return Arrays.asList(
            ToolSpecification.builder()
                .name("browser_navigate")
                .description("导航到指定URL")
                .parameters(JsonSchemaProperty.object()
                    .addStringProperty("url", "要访问的URL")
                    .addBooleanProperty("newWindow", "是否在新窗口打开")
                    .addIntegerProperty("width", "窗口宽度")
                    .addIntegerProperty("height", "窗口高度")
                    .required("url")
                    .build())
                .build(),

            ToolSpecification.builder()
                .name("browser_click")
                .description("点击页面元素")
                .parameters(JsonSchemaProperty.object()
                    .addStringProperty("selector", "CSS选择器")
                    .addObjectProperty("coordinates", "点击坐标")
                    .addBooleanProperty("waitForNavigation", "是否等待页面导航")
                    .build())
                .build(),

            ToolSpecification.builder()
                .name("browser_fill")
                .description("填写表单字段")
                .parameters(JsonSchemaProperty.object()
                    .addStringProperty("selector", "CSS选择器")
                    .addStringProperty("value", "要填写的值")
                    .required("selector", "value")
                    .build())
                .build(),

            ToolSpecification.builder()
                .name("browser_screenshot")
                .description("截取页面截图")
                .parameters(JsonSchemaProperty.object()
                    .addStringProperty("selector", "元素选择器")
                    .addBooleanProperty("fullPage", "是否全页截图")
                    .addIntegerProperty("width", "截图宽度")
                    .addIntegerProperty("height", "截图高度")
                    .build())
                .build(),

            ToolSpecification.builder()
                .name("browser_get_content")
                .description("获取页面内容")
                .parameters(JsonSchemaProperty.object()
                    .addStringProperty("format", "内容格式: text|html")
                    .addStringProperty("selector", "元素选择器")
                    .build())
                .build()
        );
    }

    private String executeTool(String toolName, String arguments) {
        try {
            log.info("执行浏览器工具: {} with args: {}", toolName, arguments);

            BrowserToolRequest request = BrowserToolRequest.builder()
                .toolName(toolName)
                .arguments(arguments)
                .requestId(UUID.randomUUID().toString())
                .timestamp(System.currentTimeMillis())
                .build();

            // 通过WebSocket发送到浏览器插件
            BrowserToolResponse response = webSocketManager
                .sendToolRequest(request)
                .timeout(Duration.ofSeconds(30))
                .block();

            if (response == null) {
                throw new RuntimeException("浏览器工具执行超时");
            }

            if (!response.isSuccess()) {
                throw new RuntimeException("浏览器工具执行失败: " + response.getError());
            }

            return JsonUtils.toJson(response.getData());

        } catch (Exception e) {
            log.error("执行浏览器工具失败: {}", e.getMessage(), e);
            return JsonUtils.toJson(Map.of(
                "success", false,
                "error", e.getMessage(),
                "timestamp", System.currentTimeMillis()
            ));
        }
    }
}
```

### 6.4 语义搜索集成

```java
@Component
public class BrowserSemanticSearchTool {

    private final EmbeddingStore<TextSegment> embeddingStore;
    private final EmbeddingModel embeddingModel;
    private final WebSocketManager webSocketManager;

    public String searchTabsContent(String query, int maxResults) {
        try {
            // 1. 首先从浏览器获取所有标签页内容
            BrowserToolRequest contentRequest = BrowserToolRequest.builder()
                .toolName("get_all_tabs_content")
                .arguments(JsonUtils.toJson(Map.of("format", "text")))
                .requestId(UUID.randomUUID().toString())
                .build();

            BrowserToolResponse contentResponse = webSocketManager
                .sendToolRequest(contentRequest)
                .block(Duration.ofSeconds(10));

            if (contentResponse == null || !contentResponse.isSuccess()) {
                throw new RuntimeException("获取标签页内容失败");
            }

            // 2. 解析标签页内容
            List<TabContent> tabContents = parseTabContents(contentResponse.getData());

            // 3. 对查询进行向量化
            Embedding queryEmbedding = embeddingModel.embed(query).content();

            // 4. 在向量数据库中搜索相似内容
            List<EmbeddingMatch<TextSegment>> matches = embeddingStore.findRelevant(
                queryEmbedding,
                maxResults,
                0.7 // 相似度阈值
            );

            // 5. 构建搜索结果
            List<SearchResult> results = matches.stream()
                .map(match -> SearchResult.builder()
                    .tabId(extractTabId(match.embedded()))
                    .title(extractTitle(match.embedded()))
                    .url(extractUrl(match.embedded()))
                    .content(match.embedded().text())
                    .score(match.score())
                    .build())
                .collect(Collectors.toList());

            return JsonUtils.toJson(Map.of(
                "success", true,
                "results", results,
                "query", query,
                "totalResults", results.size()
            ));

        } catch (Exception e) {
            log.error("语义搜索失败", e);
            return JsonUtils.toJson(Map.of(
                "success", false,
                "error", e.getMessage()
            ));
        }
    }
}
```

### 6.5 浏览器插件WebSocket客户端

```javascript
// edgemind-plugin/src/websocket/WebSocketClient.js
class WebSocketClient {
    constructor() {
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.messageHandlers = new Map();
        this.pendingRequests = new Map();

        this.connect();
    }

    connect() {
        try {
            // 注意：需要包含/wkg上下文路径
            this.ws = new WebSocket('ws://localhost:8080/wkg/browser-automation');

            this.ws.onopen = () => {
                console.log('WebSocket连接已建立');
                this.reconnectAttempts = 0;
                this.sendHandshake();
            };

            this.ws.onmessage = (event) => {
                this.handleMessage(JSON.parse(event.data));
            };

            this.ws.onclose = () => {
                console.log('WebSocket连接已关闭');
                this.scheduleReconnect();
            };

            this.ws.onerror = (error) => {
                console.error('WebSocket错误:', error);
            };

        } catch (error) {
            console.error('WebSocket连接失败:', error);
            this.scheduleReconnect();
        }
    }

    handleMessage(message) {
        switch (message.type) {
            case 'TOOL_CALL':
                this.handleToolCall(message);
                break;
            case 'PING':
                this.send({ type: 'PONG', timestamp: Date.now() });
                break;
            default:
                console.warn('未知消息类型:', message.type);
        }
    }

    async handleToolCall(message) {
        const { requestId, toolName, arguments: args } = message;

        try {
            const toolExecutor = this.getToolExecutor(toolName);
            if (!toolExecutor) {
                throw new Error(`未知工具: ${toolName}`);
            }

            const result = await toolExecutor.execute(JSON.parse(args));

            this.send({
                type: 'TOOL_RESPONSE',
                requestId,
                success: true,
                data: result,
                timestamp: Date.now()
            });

        } catch (error) {
            console.error(`工具执行失败 ${toolName}:`, error);

            this.send({
                type: 'TOOL_RESPONSE',
                requestId,
                success: false,
                error: error.message,
                timestamp: Date.now()
            });
        }
    }

    getToolExecutor(toolName) {
        const toolMap = {
            'browser_navigate': new NavigateTool(),
            'browser_click': new ClickTool(),
            'browser_fill': new FillTool(),
            'browser_screenshot': new ScreenshotTool(),
            'browser_get_content': new GetContentTool(),
            'get_all_tabs_content': new GetAllTabsContentTool()
        };

        return toolMap[toolName];
    }

    send(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        } else {
            console.warn('WebSocket未连接，消息发送失败');
        }
    }

    scheduleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
                console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                this.connect();
            }, this.reconnectDelay * this.reconnectAttempts);
        }
    }

    sendHandshake() {
        this.send({
            type: 'HANDSHAKE',
            clientType: 'BROWSER_PLUGIN',
            version: '1.0.0',
            capabilities: [
                'navigate', 'click', 'fill', 'screenshot',
                'getContent', 'networkCapture', 'semanticSearch'
            ],
            timestamp: Date.now()
        });
    }
}

// 全局实例
window.edgemindWebSocket = new WebSocketClient();
```

## 7. 关键技术决策

### 7.1 为什么选择WebSocket而不是HTTP轮询
1. **实时性**: WebSocket提供双向实时通信
2. **效率**: 避免HTTP轮询的开销
3. **状态保持**: 维持长连接，便于会话管理
4. **错误处理**: 更好的连接状态监控

### 7.2 工具执行的异步处理
```java
@Async("browserToolExecutor")
public CompletableFuture<BrowserToolResponse> executeToolAsync(BrowserToolRequest request) {
    return CompletableFuture.supplyAsync(() -> {
        // 工具执行逻辑
        return webSocketManager.sendToolRequest(request).block();
    });
}
```

### 7.3 错误恢复策略
1. **网络中断**: 自动重连机制
2. **工具执行失败**: 重试3次后降级
3. **超时处理**: 30秒超时，返回错误信息
4. **插件离线**: 提示用户手动操作

## 8. 性能优化考虑

### 8.1 连接池管理
```java
@Configuration
public class WebSocketPoolConfig {

    @Bean
    public TaskExecutor webSocketTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("websocket-");
        executor.initialize();
        return executor;
    }
}
```

### 8.2 消息队列缓冲
```java
@Component
public class MessageBuffer {
    private final BlockingQueue<BrowserMessage> messageQueue = new LinkedBlockingQueue<>(1000);

    @Scheduled(fixedDelay = 100)
    public void processMessages() {
        List<BrowserMessage> messages = new ArrayList<>();
        messageQueue.drainTo(messages, 10); // 批量处理

        messages.forEach(this::processMessage);
    }
}
```

## 9. 与现有EdgeMind-Cortex集成要点

### 9.1 策略注册到现有StrategyManager
```java
@Component
public class ChatStrategyManager {

    private final List<ChatStrategy> strategies;

    // 构造函数注入所有策略，包括新的BrowserAutomationStrategy
    public ChatStrategyManager(
            NormalChatStrategy normalChatStrategy,
            KnowledgeChatStrategy knowledgeChatStrategy,
            BrowserAutomationStrategy browserAutomationStrategy) { // 新增
        this.strategies = Arrays.asList(
            browserAutomationStrategy,  // 优先级最高，先检查浏览器意图
            knowledgeChatStrategy,
            normalChatStrategy          // 默认策略，优先级最低
        );
    }

    public ChatStrategy getStrategy(ChatContext context) {
        return strategies.stream()
                .filter(strategy -> strategy.supports(context))
                .findFirst()
                .orElse(normalChatStrategy); // 默认策略
    }
}
```

### 9.2 现有UnifiedChatController无需修改
现有的`UnifiedChatController`完全不需要修改，因为：
1. 它通过`strategyManager.getStrategy(context)`自动选择策略
2. 新的`BrowserAutomationStrategy`会自动被注册和调用
3. 保持了现有的SSE流式响应格式
4. 错误处理机制完全兼容

### 9.3 配置文件调整
```yaml
# application.yml 新增配置
edgemind:
  browser:
    websocket:
      enabled: true
      path: /wkg/browser-automation
      allowed-origins: "*"
    tools:
      timeout: 30000  # 30秒超时
      retry-attempts: 3

# WebSocket相关配置
spring:
  websocket:
    servlet:
      allowed-origins: "*"
```

### 9.4 现有前端集成
现有的聊天界面无需修改，因为：
1. 仍然使用`/wkg/api/unified-chat/stream`接口
2. SSE响应格式保持不变
3. 用户只需在聊天中输入浏览器相关指令即可自动触发

### 9.5 数据库表扩展（可选）
```sql
-- 可选：为浏览器自动化添加专门的日志表
CREATE TABLE browser_automation_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    conversation_id BIGINT,
    tool_name VARCHAR(100) NOT NULL,
    tool_args TEXT,
    execution_result TEXT,
    success BOOLEAN DEFAULT TRUE,
    execution_time_ms INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (conversation_id) REFERENCES chat_conversations(id)
);
```

### 9.6 部署注意事项
1. **端口配置**: 确保WebSocket端口与现有服务端口一致
2. **上下文路径**: 所有WebSocket路径都需要包含`/wkg`前缀
3. **CORS配置**: 浏览器插件需要跨域访问权限
4. **防火墙**: 确保WebSocket端口对浏览器插件开放

### 9.7 测试验证步骤
1. 启动edgemind-cortex服务
2. 安装并启动edgemind-plugin浏览器插件
3. 在现有聊天界面输入："帮我打开百度搜索天气"
4. 验证AI是否自动选择BrowserAutomationStrategy
5. 验证浏览器是否自动执行相应操作
6. 验证结果是否正确返回到聊天界面

这个详细的技术实现方案确保了从mcp-chrome到edgemind架构的平滑迁移，完全兼容现有的`/wkg`上下文路径和架构设计，同时保持了原有功能的完整性和性能。
