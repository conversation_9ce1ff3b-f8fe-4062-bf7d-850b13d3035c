/**
 * 工具执行器管理器
 * 负责管理和执行各种浏览器自动化工具
 */

export class ToolExecutorManager {
    constructor() {
        this.toolExecutors = new Map();
        this.isInitialized = false;
    }
    
    async init() {
        try {
            console.log('🔧 初始化工具执行器管理器');
            
            // 注册所有工具执行器
            this.registerToolExecutors();
            
            this.isInitialized = true;
            console.log(`✅ 工具执行器管理器初始化完成，共注册 ${this.toolExecutors.size} 个工具`);
            
        } catch (error) {
            console.error('❌ 工具执行器管理器初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 注册所有工具执行器
     */
    registerToolExecutors() {
        // 浏览器导航工具
        this.toolExecutors.set('browser_navigate', new NavigateTool());
        this.toolExecutors.set('browser_go_back_forward', new GoBackForwardTool());
        this.toolExecutors.set('browser_close_tabs', new CloseTabsTool());
        this.toolExecutors.set('browser_get_windows_tabs', new GetWindowsTabsTool());
        
        // 页面交互工具
        this.toolExecutors.set('browser_click', new ClickTool());
        this.toolExecutors.set('browser_fill', new FillTool());
        this.toolExecutors.set('browser_keyboard', new KeyboardTool());
        
        // 内容获取工具
        this.toolExecutors.set('browser_get_content', new GetContentTool());
        this.toolExecutors.set('browser_get_interactive_elements', new GetInteractiveElementsTool());
        this.toolExecutors.set('browser_screenshot', new ScreenshotTool());
        
        // 网络监控工具
        this.toolExecutors.set('browser_network_capture', new NetworkCaptureTool());
        this.toolExecutors.set('browser_network_debugger', new NetworkDebuggerTool());
        this.toolExecutors.set('browser_network_request', new NetworkRequestTool());
        
        // 数据管理工具
        this.toolExecutors.set('browser_history', new HistoryTool());
        this.toolExecutors.set('browser_bookmark_search', new BookmarkSearchTool());
        this.toolExecutors.set('browser_bookmark_add', new BookmarkAddTool());
        this.toolExecutors.set('browser_bookmark_delete', new BookmarkDeleteTool());
        
        // 语义搜索工具
        this.toolExecutors.set('browser_search_tabs_content', new SearchTabsContentTool());
    }
    
    /**
     * 执行工具
     */
    async executeTool(toolName, args) {
        if (!this.isInitialized) {
            throw new Error('工具执行器管理器未初始化');
        }
        
        const executor = this.toolExecutors.get(toolName);
        if (!executor) {
            throw new Error(`未知工具: ${toolName}`);
        }
        
        console.log(`🔧 执行工具: ${toolName}`, args);
        
        const startTime = Date.now();
        try {
            const result = await executor.execute(args);
            const executionTime = Date.now() - startTime;
            
            console.log(`✅ 工具执行成功: ${toolName} (${executionTime}ms)`);
            
            return {
                ...result,
                executionTime,
                timestamp: Date.now()
            };
            
        } catch (error) {
            const executionTime = Date.now() - startTime;
            console.error(`❌ 工具执行失败: ${toolName} (${executionTime}ms)`, error);
            throw error;
        }
    }
    
    /**
     * 获取可用工具列表
     */
    getAvailableTools() {
        return Array.from(this.toolExecutors.keys());
    }
    
    /**
     * 检查工具是否存在
     */
    hasTool(toolName) {
        return this.toolExecutors.has(toolName);
    }
}

/**
 * 基础工具类
 */
class BaseTool {
    constructor(name) {
        this.name = name;
    }
    
    async execute(args) {
        throw new Error('子类必须实现execute方法');
    }
    
    /**
     * 获取当前活跃标签页
     */
    async getCurrentTab() {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (!tab) {
            throw new Error('没有找到活跃的标签页');
        }
        return tab;
    }
    
    /**
     * 注入脚本到标签页
     */
    async injectScript(tabId, scriptPath) {
        await chrome.scripting.executeScript({
            target: { tabId },
            files: [scriptPath]
        });
    }
    
    /**
     * 发送消息到内容脚本
     */
    async sendMessageToTab(tabId, message) {
        return new Promise((resolve, reject) => {
            chrome.tabs.sendMessage(tabId, message, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }
}

/**
 * 导航工具
 */
class NavigateTool extends BaseTool {
    constructor() {
        super('browser_navigate');
    }
    
    async execute(args) {
        const { url, newWindow = false, width, height } = args;
        
        if (!url) {
            throw new Error('URL参数是必需的');
        }
        
        if (newWindow) {
            const windowOptions = {
                url,
                type: 'normal'
            };
            
            if (width && height) {
                windowOptions.width = width;
                windowOptions.height = height;
            }
            
            const window = await chrome.windows.create(windowOptions);
            return {
                success: true,
                windowId: window.id,
                tabId: window.tabs[0].id,
                url
            };
        } else {
            const tab = await this.getCurrentTab();
            await chrome.tabs.update(tab.id, { url });
            
            return {
                success: true,
                tabId: tab.id,
                url
            };
        }
    }
}

/**
 * 点击工具
 */
class ClickTool extends BaseTool {
    constructor() {
        super('browser_click');
    }
    
    async execute(args) {
        const { selector, coordinates, waitForNavigation = false } = args;
        
        const tab = await this.getCurrentTab();
        
        // 注入点击辅助脚本
        await this.injectScript(tab.id, 'inject-scripts/click-helper.js');
        
        // 发送点击指令
        const result = await this.sendMessageToTab(tab.id, {
            action: 'CLICK_ELEMENT',
            selector,
            coordinates,
            waitForNavigation
        });
        
        return {
            success: true,
            ...result
        };
    }
}

/**
 * 填写工具
 */
class FillTool extends BaseTool {
    constructor() {
        super('browser_fill');
    }
    
    async execute(args) {
        const { selector, value, clear = true } = args;
        
        if (!selector || value === undefined) {
            throw new Error('selector和value参数是必需的');
        }
        
        const tab = await this.getCurrentTab();
        
        // 注入填写辅助脚本
        await this.injectScript(tab.id, 'inject-scripts/fill-helper.js');
        
        // 发送填写指令
        const result = await this.sendMessageToTab(tab.id, {
            action: 'FILL_ELEMENT',
            selector,
            value,
            clear
        });
        
        return {
            success: true,
            ...result
        };
    }
}

/**
 * 截图工具
 */
class ScreenshotTool extends BaseTool {
    constructor() {
        super('browser_screenshot');
    }
    
    async execute(args) {
        const { selector, fullPage = false, format = 'png' } = args;
        
        const tab = await this.getCurrentTab();
        
        if (fullPage) {
            // 全页面截图需要特殊处理
            return await this.captureFullPage(tab.id, format);
        } else if (selector) {
            // 元素截图
            return await this.captureElement(tab.id, selector, format);
        } else {
            // 可见区域截图
            return await this.captureVisibleTab(tab.id, format);
        }
    }
    
    async captureVisibleTab(tabId, format) {
        const dataUrl = await chrome.tabs.captureVisibleTab(null, { format });
        
        return {
            success: true,
            type: 'visible_area',
            format,
            dataUrl,
            size: dataUrl.length
        };
    }
    
    async captureFullPage(tabId, format) {
        // TODO: 实现全页面截图
        throw new Error('全页面截图功能待实现');
    }
    
    async captureElement(tabId, selector, format) {
        // TODO: 实现元素截图
        throw new Error('元素截图功能待实现');
    }
}

/**
 * 内容获取工具
 */
class GetContentTool extends BaseTool {
    constructor() {
        super('browser_get_content');
    }
    
    async execute(args) {
        const { format = 'text', selector, includeHidden = false } = args;
        
        const tab = await this.getCurrentTab();
        
        // 注入内容获取脚本
        await this.injectScript(tab.id, 'inject-scripts/content-helper.js');
        
        // 发送内容获取指令
        const result = await this.sendMessageToTab(tab.id, {
            action: 'GET_CONTENT',
            format,
            selector,
            includeHidden
        });
        
        return {
            success: true,
            ...result
        };
    }
}

// 其他工具类的简化实现...
class GoBackForwardTool extends BaseTool {
    constructor() { super('browser_go_back_forward'); }
    async execute(args) { throw new Error('待实现'); }
}

class CloseTabsTool extends BaseTool {
    constructor() { super('browser_close_tabs'); }
    async execute(args) { throw new Error('待实现'); }
}

class GetWindowsTabsTool extends BaseTool {
    constructor() { super('browser_get_windows_tabs'); }
    async execute(args) { throw new Error('待实现'); }
}

class KeyboardTool extends BaseTool {
    constructor() { super('browser_keyboard'); }
    async execute(args) { throw new Error('待实现'); }
}

class GetInteractiveElementsTool extends BaseTool {
    constructor() { super('browser_get_interactive_elements'); }
    async execute(args) { throw new Error('待实现'); }
}

class NetworkCaptureTool extends BaseTool {
    constructor() { super('browser_network_capture'); }
    async execute(args) { throw new Error('待实现'); }
}

class NetworkDebuggerTool extends BaseTool {
    constructor() { super('browser_network_debugger'); }
    async execute(args) { throw new Error('待实现'); }
}

class NetworkRequestTool extends BaseTool {
    constructor() { super('browser_network_request'); }
    async execute(args) { throw new Error('待实现'); }
}

class HistoryTool extends BaseTool {
    constructor() { super('browser_history'); }
    async execute(args) { throw new Error('待实现'); }
}

class BookmarkSearchTool extends BaseTool {
    constructor() { super('browser_bookmark_search'); }
    async execute(args) { throw new Error('待实现'); }
}

class BookmarkAddTool extends BaseTool {
    constructor() { super('browser_bookmark_add'); }
    async execute(args) { throw new Error('待实现'); }
}

class BookmarkDeleteTool extends BaseTool {
    constructor() { super('browser_bookmark_delete'); }
    async execute(args) { throw new Error('待实现'); }
}

class SearchTabsContentTool extends BaseTool {
    constructor() { super('browser_search_tabs_content'); }
    async execute(args) { throw new Error('待实现'); }
}
