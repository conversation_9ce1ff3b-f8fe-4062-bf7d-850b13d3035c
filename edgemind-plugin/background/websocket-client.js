/**
 * WebSocket客户端 - 与EdgeMind-Cortex后端通信
 */

export class WebSocketClient {
    constructor(options = {}) {
        this.url = options.url || 'ws://localhost:8080/wkg/browser-automation';
        this.onToolCall = options.onToolCall || (() => {});
        this.onConnect = options.onConnect || (() => {});
        this.onDisconnect = options.onDisconnect || (() => {});
        
        this.ws = null;
        this.connected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // 1秒
        this.heartbeatInterval = null;
        
        this.messageHandlers = new Map();
        this.setupMessageHandlers();
    }
    
    /**
     * 设置消息处理器
     */
    setupMessageHandlers() {
        this.messageHandlers.set('WELCOME', this.handleWelcome.bind(this));
        this.messageHandlers.set('HANDSHAKE_ACK', this.handleHandshakeAck.bind(this));
        this.messageHandlers.set('TOOL_CALL', this.handleToolCall.bind(this));
        this.messageHandlers.set('PING', this.handlePing.bind(this));
        this.messageHandlers.set('ERROR', this.handleError.bind(this));
    }
    
    /**
     * 连接到WebSocket服务器
     */
    async connect() {
        try {
            console.log(`🔗 连接到EdgeMind-Cortex: ${this.url}`);
            
            this.ws = new WebSocket(this.url);
            
            this.ws.onopen = this.handleOpen.bind(this);
            this.ws.onmessage = this.handleMessage.bind(this);
            this.ws.onclose = this.handleClose.bind(this);
            this.ws.onerror = this.handleError.bind(this);
            
            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('WebSocket连接超时'));
                }, 10000);
                
                this.ws.addEventListener('open', () => {
                    clearTimeout(timeout);
                    resolve();
                });
                
                this.ws.addEventListener('error', (error) => {
                    clearTimeout(timeout);
                    reject(error);
                });
            });
            
        } catch (error) {
            console.error('❌ WebSocket连接失败:', error);
            throw error;
        }
    }
    
    /**
     * 处理WebSocket连接打开
     */
    handleOpen() {
        console.log('✅ WebSocket连接已建立');
        this.connected = true;
        this.reconnectAttempts = 0;
        
        // 发送握手消息
        this.sendHandshake();
        
        // 启动心跳
        this.startHeartbeat();
        
        this.onConnect();
    }
    
    /**
     * 处理WebSocket消息
     */
    handleMessage(event) {
        try {
            const message = JSON.parse(event.data);
            const handler = this.messageHandlers.get(message.type);
            
            if (handler) {
                handler(message);
            } else {
                console.warn('⚠️ 未知消息类型:', message.type);
            }
            
        } catch (error) {
            console.error('❌ 处理WebSocket消息失败:', error);
        }
    }
    
    /**
     * 处理WebSocket连接关闭
     */
    handleClose(event) {
        console.log('🔌 WebSocket连接已关闭:', event.code, event.reason);
        this.connected = false;
        
        // 停止心跳
        this.stopHeartbeat();
        
        this.onDisconnect();
        
        // 自动重连
        this.scheduleReconnect();
    }
    
    /**
     * 处理WebSocket错误
     */
    handleError(error) {
        console.error('❌ WebSocket错误:', error);
    }
    
    /**
     * 发送握手消息
     */
    sendHandshake() {
        const handshake = {
            type: 'HANDSHAKE',
            clientType: 'BROWSER_PLUGIN',
            version: chrome.runtime.getManifest().version,
            capabilities: [
                'navigate', 'click', 'fill', 'screenshot', 
                'getContent', 'networkCapture', 'semanticSearch',
                'history', 'bookmarks', 'keyboard', 'debugger'
            ],
            timestamp: Date.now()
        };
        
        this.send(handshake);
    }
    
    /**
     * 处理欢迎消息
     */
    handleWelcome(message) {
        console.log('🤝 收到服务器欢迎消息:', message.message);
    }
    
    /**
     * 处理握手确认
     */
    handleHandshakeAck(message) {
        console.log('✅ 握手成功，服务器版本:', message.serverVersion);
    }
    
    /**
     * 处理工具调用请求
     */
    handleToolCall(message) {
        console.log('🔧 收到工具调用请求:', message.toolName);
        this.onToolCall(message);
    }
    
    /**
     * 处理心跳请求
     */
    handlePing(message) {
        // 响应心跳
        this.send({
            type: 'PONG',
            timestamp: Date.now()
        });
    }
    
    /**
     * 发送工具响应
     */
    sendToolResponse(response) {
        this.send(response);
    }
    
    /**
     * 发送消息
     */
    send(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        } else {
            console.warn('⚠️ WebSocket未连接，消息发送失败');
        }
    }
    
    /**
     * 启动心跳
     */
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.connected) {
                this.send({
                    type: 'HEARTBEAT',
                    timestamp: Date.now()
                });
            }
        }, 30000); // 30秒心跳
    }
    
    /**
     * 停止心跳
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }
    
    /**
     * 安排重连
     */
    scheduleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
            
            console.log(`🔄 ${delay}ms后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            
            setTimeout(() => {
                this.connect().catch(error => {
                    console.error('重连失败:', error);
                });
            }, delay);
        } else {
            console.error('❌ 达到最大重连次数，停止重连');
        }
    }
    
    /**
     * 手动重连
     */
    reconnect() {
        this.reconnectAttempts = 0;
        if (this.ws) {
            this.ws.close();
        }
        this.connect();
    }
    
    /**
     * 断开连接
     */
    disconnect() {
        this.stopHeartbeat();
        if (this.ws) {
            this.ws.close();
        }
    }
    
    /**
     * 检查连接状态
     */
    isConnected() {
        return this.connected && this.ws && this.ws.readyState === WebSocket.OPEN;
    }
}
