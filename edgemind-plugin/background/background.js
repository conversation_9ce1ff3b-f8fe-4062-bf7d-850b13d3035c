/**
 * EdgeMind浏览器自动化插件 - 后台脚本
 * 负责与EdgeMind-Cortex后端通信，执行浏览器自动化任务
 */

class EdgeMindBackground {
    constructor() {
        this.websocketClient = null;
        this.isInitialized = false;
        this.availableTools = [
            'browser_navigate',
            'browser_click',
            'browser_fill',
            'browser_screenshot',
            'browser_get_content'
        ];

        this.init();
    }
    
    async init() {
        try {
            console.log('🚀 EdgeMind浏览器自动化插件启动');

            // 初始化WebSocket客户端
            this.initWebSocket();

            this.isInitialized = true;
            console.log('✅ EdgeMind浏览器自动化插件初始化完成');

        } catch (error) {
            console.error('❌ EdgeMind插件初始化失败:', error);
        }
    }

    /**
     * 初始化WebSocket连接
     */
    initWebSocket() {
        this.websocketUrl = 'ws://localhost:8080/wkg/browser-automation';
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;

        this.connect();
    }

    /**
     * 连接WebSocket
     */
    connect() {
        try {
            console.log(`🔗 连接到EdgeMind-Cortex: ${this.websocketUrl}`);

            this.websocketClient = new WebSocket(this.websocketUrl);

            this.websocketClient.onopen = () => {
                console.log('✅ WebSocket连接已建立');
                this.reconnectAttempts = 0;
                this.sendHandshake();
                this.updateBadge('connected');
            };

            this.websocketClient.onmessage = (event) => {
                this.handleMessage(JSON.parse(event.data));
            };

            this.websocketClient.onclose = () => {
                console.log('🔌 WebSocket连接已关闭');
                this.updateBadge('disconnected');
                this.scheduleReconnect();
            };

            this.websocketClient.onerror = (error) => {
                console.error('❌ WebSocket错误:', error);
                console.error('WebSocket错误详情:', {
                    readyState: this.websocketClient?.readyState,
                    url: this.websocketUrl,
                    error: error
                });
            };

        } catch (error) {
            console.error('❌ WebSocket连接失败:', error);
            this.scheduleReconnect();
        }
    }
    
    /**
     * 发送握手消息
     */
    sendHandshake() {
        const handshake = {
            type: 'HANDSHAKE',
            clientType: 'BROWSER_PLUGIN',
            version: chrome.runtime.getManifest().version,
            capabilities: this.availableTools,
            timestamp: Date.now()
        };

        this.send(handshake);
    }

    /**
     * 处理WebSocket消息
     */
    handleMessage(message) {
        try {
            switch (message.type) {
                case 'WELCOME':
                    console.log('🤝 收到服务器欢迎消息:', message.message);
                    break;
                case 'HANDSHAKE_ACK':
                    console.log('✅ 握手成功，服务器版本:', message.serverVersion);
                    break;
                case 'TOOL_CALL':
                    this.handleToolCall(message);
                    break;
                case 'PING':
                    this.send({ type: 'PONG', timestamp: Date.now() });
                    break;
                case 'ERROR':
                    console.error('❌ 收到服务器错误:', message.error);
                    break;
                default:
                    console.warn('⚠️ 未知消息类型:', message.type);
            }
        } catch (error) {
            console.error('❌ 处理消息失败:', error);
        }
    }

    /**
     * 处理工具调用请求
     */
    async handleToolCall(message) {
        const { requestId, toolName, arguments: args } = message;

        console.log(`🔧 执行工具: ${toolName}`, args);

        try {
            // 执行工具
            const result = await this.executeTool(toolName, JSON.parse(args));

            // 发送成功响应
            this.send({
                type: 'TOOL_RESPONSE',
                requestId,
                success: true,
                data: result,
                toolName,
                executionTimeMs: result.executionTime || 0,
                timestamp: Date.now()
            });

            console.log(`✅ 工具执行成功: ${toolName}`, result);

        } catch (error) {
            console.error(`❌ 工具执行失败: ${toolName}`, error);

            // 发送错误响应
            this.send({
                type: 'TOOL_RESPONSE',
                requestId,
                success: false,
                error: error.message,
                errorCode: error.code || 'EXECUTION_ERROR',
                toolName,
                timestamp: Date.now()
            });
        }
    }
    
    /**
     * 执行工具
     */
    async executeTool(toolName, args) {
        const startTime = Date.now();

        switch (toolName) {
            case 'browser_navigate':
                return await this.executeNavigate(args);
            case 'browser_click':
                return await this.executeClick(args);
            case 'browser_fill':
                return await this.executeFill(args);
            case 'browser_screenshot':
                return await this.executeScreenshot(args);
            case 'browser_get_content':
                return await this.executeGetContent(args);
            case 'browser_get_windows_tabs':
                return await this.executeGetWindowsTabs(args);
            case 'browser_network_capture':
                return await this.executeNetworkCapture(args);
            case 'browser_network_debugger':
                return await this.executeNetworkDebugger(args);
            case 'browser_network_request':
                return await this.executeNetworkRequest(args);
            case 'browser_history':
                return await this.executeHistory(args);
            case 'browser_bookmark_search':
                return await this.executeBookmarkSearch(args);
            case 'browser_keyboard':
                return await this.executeKeyboard(args);
            case 'browser_get_interactive_elements':
                return await this.executeGetInteractiveElements(args);
            case 'browser_search_tabs_content':
                return await this.executeSearchTabsContent(args);
            case 'browser_close_tabs':
                return await this.executeCloseTabs(args);
            case 'browser_go_back_forward':
                return await this.executeGoBackForward(args);
            case 'browser_switch_tab':
                return await this.executeSwitchTab(args);
            default:
                throw new Error(`未知工具: ${toolName}`);
        }
    }

    /**
     * 执行导航
     */
    async executeNavigate(args) {
        const { url, newWindow = false } = args;

        if (!url) {
            throw new Error('URL参数是必需的');
        }

        console.log(`🌐 开始导航到: ${url}, newWindow: ${newWindow}`);

        if (newWindow) {
            const window = await chrome.windows.create({ url, type: 'normal' });
            console.log(`✅ 新窗口创建成功: windowId=${window.id}, tabId=${window.tabs[0].id}`);
            return {
                success: true,
                windowId: window.id,
                tabId: window.tabs[0].id,
                url
            };
        } else {
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            console.log(`🔍 查询到的活跃标签页数量: ${tabs.length}`);

            if (tabs.length === 0) {
                // 如果没有活跃标签页，创建一个新标签页
                console.log(`📝 没有活跃标签页，创建新标签页`);
                const newTab = await chrome.tabs.create({ url });
                console.log(`✅ 新标签页创建成功: tabId=${newTab.id}`);

                return {
                    success: true,
                    tabId: newTab.id,
                    url: newTab.url,
                    originalUrl: url,
                    newTab: true
                };
            }

            const tab = tabs[0];
            console.log(`🔄 更新当前标签页: tabId=${tab.id}, 从 ${tab.url} 导航到 ${url}`);

            await chrome.tabs.update(tab.id, { url });

            // 等待导航完成
            await this.waitForTabComplete(tab.id, 5000);

            // 获取最终URL（可能因重定向而改变）
            const updatedTab = await chrome.tabs.get(tab.id);
            console.log(`✅ 导航完成: 最终URL=${updatedTab.url}`);

            return {
                success: true,
                tabId: tab.id,
                url: updatedTab.url, // 返回实际的最终URL
                originalUrl: url,    // 保留原始请求的URL
                redirected: updatedTab.url !== url
            };
        }
    }

    /**
     * 执行点击
     */
    async executeClick(args) {
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tabs.length === 0) {
            throw new Error('没有找到活跃的标签页');
        }
        const tab = tabs[0];

        return new Promise((resolve, reject) => {
            chrome.tabs.sendMessage(tab.id, {
                action: 'CLICK_ELEMENT',
                ...args
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    /**
     * 执行填写
     */
    async executeFill(args) {
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tabs.length === 0) {
            throw new Error('没有找到活跃的标签页');
        }
        const tab = tabs[0];

        return new Promise((resolve, reject) => {
            chrome.tabs.sendMessage(tab.id, {
                action: 'FILL_ELEMENT',
                ...args
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    /**
     * 执行截图
     */
    async executeScreenshot(args) {
        const {
            format = 'png',
            quality = 90,
            type = 'visible',  // visible, fullpage, element
            selector = null,
            hideElements = [],
            waitTime = 1000
        } = args;

        try {
            const tab = await this.getActiveTab();

            // 等待页面稳定
            if (waitTime > 0) {
                await this.delay(waitTime);
            }

            switch (type) {
                case 'fullpage':
                    return await this.captureFullPage(tab, format, quality, hideElements);
                case 'element':
                    return await this.captureElement(tab, selector, format, quality, hideElements);
                case 'visible':
                default:
                    return await this.captureVisible(tab, format, quality);
            }

        } catch (error) {
            throw new Error(`截图失败: ${error.message}`);
        }
    }

    /**
     * 捕获可见区域截图
     */
    async captureVisible(tab, format, quality) {
        const options = { format };
        if (format === 'jpeg') {
            options.quality = quality;
        }

        const dataUrl = await chrome.tabs.captureVisibleTab(null, options);

        return {
            success: true,
            type: 'visible_area',
            format,
            dataUrl,
            size: dataUrl.length,
            timestamp: Date.now()
        };
    }

    /**
     * 捕获全页面截图（滚动拼接）
     */
    async captureFullPage(tab, format, quality, hideElements) {
        try {
            // 1. 准备页面（隐藏固定元素等）
            await this.preparePageForScreenshot(tab.id, hideElements);

            // 2. 获取页面尺寸信息
            const pageInfo = await this.getPageDimensions(tab.id);

            // 3. 计算需要的截图数量
            const viewportHeight = pageInfo.viewportHeight;
            const totalHeight = pageInfo.scrollHeight;
            const screenshots = [];

            // 4. 逐段截图
            let currentY = 0;
            while (currentY < totalHeight) {
                // 滚动到指定位置
                await this.scrollToPosition(tab.id, currentY);
                await this.delay(500); // 等待滚动完成

                // 截图
                const options = { format };
                if (format === 'jpeg') {
                    options.quality = quality;
                }

                const dataUrl = await chrome.tabs.captureVisibleTab(null, options);
                screenshots.push({
                    dataUrl,
                    y: currentY,
                    height: Math.min(viewportHeight, totalHeight - currentY)
                });

                currentY += viewportHeight;
            }

            // 5. 拼接截图
            const fullScreenshot = await this.stitchScreenshots(screenshots, pageInfo);

            // 6. 恢复页面状态
            await this.restorePageAfterScreenshot(tab.id);

            return {
                success: true,
                type: 'full_page',
                format,
                dataUrl: fullScreenshot,
                size: fullScreenshot.length,
                dimensions: {
                    width: pageInfo.scrollWidth,
                    height: pageInfo.scrollHeight
                },
                timestamp: Date.now()
            };

        } catch (error) {
            // 确保恢复页面状态
            await this.restorePageAfterScreenshot(tab.id);
            throw error;
        }
    }

    /**
     * 捕获元素截图
     */
    async captureElement(tab, selector, format, quality, hideElements) {
        if (!selector) {
            throw new Error('元素截图需要提供selector参数');
        }

        try {
            // 1. 准备页面
            await this.preparePageForScreenshot(tab.id, hideElements);

            // 2. 获取元素位置信息
            const elementInfo = await this.getElementPosition(tab.id, selector);

            // 3. 滚动到元素位置
            await this.scrollToElement(tab.id, selector);
            await this.delay(500);

            // 4. 截图
            const options = { format };
            if (format === 'jpeg') {
                options.quality = quality;
            }

            const dataUrl = await chrome.tabs.captureVisibleTab(null, options);

            // 5. 裁剪元素区域
            const croppedDataUrl = await this.cropImage(dataUrl, elementInfo);

            // 6. 恢复页面状态
            await this.restorePageAfterScreenshot(tab.id);

            return {
                success: true,
                type: 'element',
                format,
                dataUrl: croppedDataUrl,
                size: croppedDataUrl.length,
                element: {
                    selector,
                    position: elementInfo
                },
                timestamp: Date.now()
            };

        } catch (error) {
            await this.restorePageAfterScreenshot(tab.id);
            throw error;
        }
    }

    /**
     * 执行获取内容 - 与mcp-chrome保持一致
     */
    async executeGetContent(args) {
        const {
            url = null,
            htmlContent = true,  // 默认返回HTML内容
            textContent = false, // 默认不返回文本内容
            selector = null
        } = args;

        try {
            let targetTab;

            if (url) {
                // 如果提供了URL，导航到该页面
                const currentTab = await this.getActiveTab();
                await chrome.tabs.update(currentTab.id, { url });

                // 等待页面加载完成
                await this.waitForTabComplete(currentTab.id);
                targetTab = currentTab;
            } else {
                // 使用当前活跃标签页
                targetTab = await this.getActiveTab();
            }

            // 确定内容格式 - 默认HTML
            let format = 'html';
            if (textContent && !htmlContent) {
                format = 'text';
            } else if (htmlContent) {
                format = 'html';
            }

            // 从插件获取原始HTML和元数据
            const rawResponse = await new Promise((resolve, reject) => {
                chrome.tabs.sendMessage(targetTab.id, {
                    action: 'GET_CONTENT',
                    format: 'html', // 总是获取HTML
                    selector,
                    includeHidden: false
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response || { success: false, error: '无响应' });
                    }
                });
            });

            if (!rawResponse.success) {
                throw new Error(rawResponse.error || '获取HTML内容失败');
            }

            // 发送到Java后端进行智能处理
            const processedResult = await this.sendContentProcessRequest({
                htmlContent: rawResponse.content,
                metadata: {
                    ...rawResponse.metadata,
                    requestedFormat: format,
                    includeHidden: false
                }
            });

            return {
                success: true,
                content: processedResult.content,
                contentType: processedResult.contentType,
                length: processedResult.length,
                wordCount: processedResult.wordCount,
                metadata: processedResult.metadata
            };

        } catch (error) {
            throw new Error(`获取内容失败: ${error.message}`);
        }
    }

    /**
     * 等待标签页加载完成
     */
    async waitForTabComplete(tabId, timeout = 10000) {
        return new Promise((resolve) => {
            const startTime = Date.now();

            const checkComplete = () => {
                chrome.tabs.get(tabId, (tab) => {
                    if (chrome.runtime.lastError) {
                        resolve(false);
                        return;
                    }

                    if (tab.status === 'complete') {
                        resolve(true);
                    } else if (Date.now() - startTime > timeout) {
                        resolve(false);
                    } else {
                        setTimeout(checkComplete, 100);
                    }
                });
            };

            checkComplete();
        });
    }

    /**
     * 执行获取窗口和标签页信息
     */
    async executeGetWindowsTabs(args) {
        const { includeContent = false, activeOnly = false } = args;

        try {
            const windows = await chrome.windows.getAll({ populate: true });
            const result = [];

            for (const window of windows) {
                const windowInfo = {
                    id: window.id,
                    focused: window.focused,
                    type: window.type,
                    state: window.state,
                    tabs: []
                };

                for (const tab of window.tabs) {
                    if (activeOnly && !tab.active) {
                        continue;
                    }

                    const tabInfo = {
                        id: tab.id,
                        url: tab.url,
                        title: tab.title,
                        active: tab.active,
                        pinned: tab.pinned,
                        status: tab.status,
                        favIconUrl: tab.favIconUrl
                    };

                    // 如果需要包含内容，获取页面内容摘要
                    if (includeContent && tab.status === 'complete') {
                        try {
                            const content = await new Promise((resolve) => {
                                chrome.tabs.sendMessage(tab.id, {
                                    action: 'GET_CONTENT',
                                    format: 'text',
                                    maxLength: 500
                                }, (response) => {
                                    resolve(response?.content || '');
                                });
                            });
                            tabInfo.contentSummary = content.substring(0, 200);
                        } catch (error) {
                            tabInfo.contentSummary = '';
                        }
                    }

                    windowInfo.tabs.push(tabInfo);
                }

                result.push(windowInfo);
            }

            return {
                success: true,
                windows: result,
                totalWindows: result.length,
                totalTabs: result.reduce((sum, w) => sum + w.tabs.length, 0)
            };

        } catch (error) {
            throw new Error(`获取窗口标签页信息失败: ${error.message}`);
        }
    }

    /**
     * 执行网络捕获
     */
    async executeNetworkCapture(args) {
        const { action, filters = [], maxRequests = 100 } = args;

        if (action === 'start') {
            // 开始网络监控
            this.networkRequests = [];
            this.networkFilters = filters;
            this.maxNetworkRequests = maxRequests;

            // 监听网络请求
            if (!this.networkListenerAttached) {
                chrome.webRequest.onBeforeRequest.addListener(
                    this.handleNetworkRequest.bind(this),
                    { urls: ["<all_urls>"] },
                    ["requestBody"]
                );

                chrome.webRequest.onCompleted.addListener(
                    this.handleNetworkResponse.bind(this),
                    { urls: ["<all_urls>"] },
                    ["responseHeaders"]
                );

                this.networkListenerAttached = true;
            }

            return {
                success: true,
                message: '网络捕获已开始',
                filters: filters,
                maxRequests: maxRequests
            };

        } else if (action === 'stop') {
            // 停止网络监控并返回结果
            const requests = [...(this.networkRequests || [])];

            // 清理监听器
            if (this.networkListenerAttached) {
                chrome.webRequest.onBeforeRequest.removeListener(this.handleNetworkRequest);
                chrome.webRequest.onCompleted.removeListener(this.handleNetworkResponse);
                this.networkListenerAttached = false;
            }

            this.networkRequests = [];

            return {
                success: true,
                message: '网络捕获已停止',
                requests: requests,
                totalRequests: requests.length
            };
        } else {
            throw new Error('无效的action参数，必须是start或stop');
        }
    }

    /**
     * 执行网络调试器
     */
    async executeNetworkDebugger(args) {
        const { action, maxRequests = 100, maxResponseSize = 1048576 } = args; // 1MB limit

        if (action === 'start') {
            try {
                const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

                // 初始化调试器数据
                this.debuggerRequests = [];
                this.maxDebuggerRequests = maxRequests;
                this.maxResponseSize = maxResponseSize;
                this.debuggerTabId = tab.id;

                // 附加调试器
                await chrome.debugger.attach({ tabId: tab.id }, '1.3');

                // 启用网络域
                await chrome.debugger.sendCommand({ tabId: tab.id }, 'Network.enable');

                // 设置事件监听器
                if (!this.debuggerEventListener) {
                    this.debuggerEventListener = this.handleDebuggerEvent.bind(this);
                    chrome.debugger.onEvent.addListener(this.debuggerEventListener);
                }

                return {
                    success: true,
                    message: '网络调试器已启动',
                    tabId: tab.id,
                    maxRequests: maxRequests,
                    maxResponseSize: maxResponseSize
                };

            } catch (error) {
                throw new Error(`启动网络调试器失败: ${error.message}`);
            }

        } else if (action === 'stop') {
            try {
                const requests = [...(this.debuggerRequests || [])];

                // 清理调试器
                if (this.debuggerTabId) {
                    try {
                        await chrome.debugger.sendCommand({ tabId: this.debuggerTabId }, 'Network.disable');
                        await chrome.debugger.detach({ tabId: this.debuggerTabId });
                    } catch (e) {
                        console.warn('清理调试器时出错:', e.message);
                    }
                }

                // 移除事件监听器
                if (this.debuggerEventListener) {
                    chrome.debugger.onEvent.removeListener(this.debuggerEventListener);
                    this.debuggerEventListener = null;
                }

                // 清理数据
                this.debuggerRequests = [];
                this.debuggerTabId = null;

                return {
                    success: true,
                    message: '网络调试器已停止',
                    requests: requests,
                    totalRequests: requests.length
                };

            } catch (error) {
                throw new Error(`停止网络调试器失败: ${error.message}`);
            }
        } else {
            throw new Error('无效的action参数，必须是start或stop');
        }
    }

    /**
     * 处理调试器事件
     */
    handleDebuggerEvent(source, method, params) {
        if (source.tabId !== this.debuggerTabId) return;

        try {
            switch (method) {
                case 'Network.requestWillBeSent':
                    this.handleRequestWillBeSent(params);
                    break;
                case 'Network.responseReceived':
                    this.handleResponseReceived(params);
                    break;
                case 'Network.loadingFinished':
                    this.handleLoadingFinished(params);
                    break;
                case 'Network.loadingFailed':
                    this.handleLoadingFailed(params);
                    break;
            }
        } catch (error) {
            console.error('处理调试器事件失败:', error);
        }
    }

    /**
     * 处理请求发送事件
     */
    handleRequestWillBeSent(params) {
        if (!this.debuggerRequests) return;

        // 限制请求数量
        if (this.debuggerRequests.length >= this.maxDebuggerRequests) {
            this.debuggerRequests.shift();
        }

        const request = {
            requestId: params.requestId,
            url: params.request.url,
            method: params.request.method,
            headers: params.request.headers,
            postData: params.request.postData,
            timestamp: params.timestamp,
            type: params.type,
            initiator: params.initiator,
            status: 'pending'
        };

        this.debuggerRequests.push(request);
    }

    /**
     * 处理响应接收事件
     */
    handleResponseReceived(params) {
        if (!this.debuggerRequests) return;

        const request = this.debuggerRequests.find(req => req.requestId === params.requestId);
        if (request) {
            request.response = {
                status: params.response.status,
                statusText: params.response.statusText,
                headers: params.response.headers,
                mimeType: params.response.mimeType,
                contentLength: params.response.headers['content-length']
            };
            request.status = 'received';
        }
    }

    /**
     * 处理加载完成事件
     */
    async handleLoadingFinished(params) {
        if (!this.debuggerRequests) return;

        const request = this.debuggerRequests.find(req => req.requestId === params.requestId);
        if (request) {
            request.status = 'finished';
            request.encodedDataLength = params.encodedDataLength;

            // 获取响应体（如果大小合适）
            if (params.encodedDataLength <= this.maxResponseSize) {
                try {
                    const response = await chrome.debugger.sendCommand(
                        { tabId: this.debuggerTabId },
                        'Network.getResponseBody',
                        { requestId: params.requestId }
                    );

                    if (response.body) {
                        request.responseBody = response.base64Encoded ?
                            atob(response.body) : response.body;
                    }
                } catch (error) {
                    console.warn(`获取响应体失败 ${params.requestId}:`, error.message);
                }
            }
        }
    }

    /**
     * 处理加载失败事件
     */
    handleLoadingFailed(params) {
        if (!this.debuggerRequests) return;

        const request = this.debuggerRequests.find(req => req.requestId === params.requestId);
        if (request) {
            request.status = 'failed';
            request.errorText = params.errorText;
            request.canceled = params.canceled;
        }
    }

    /**
     * 执行网络请求
     */
    async executeNetworkRequest(args) {
        const {
            url,
            method = 'GET',
            headers = {},
            body = null,
            timeout = 30000,
            followRedirects = true
        } = args;

        if (!url) {
            throw new Error('url参数是必需的');
        }

        try {
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tabs.length === 0) {
                throw new Error('没有找到活跃的标签页');
            }
            const tab = tabs[0];

            // 构建fetch选项
            const fetchOptions = {
                method: method.toUpperCase(),
                headers: headers,
                redirect: followRedirects ? 'follow' : 'manual'
            };

            if (body && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
                fetchOptions.body = typeof body === 'string' ? body : JSON.stringify(body);

                // 设置默认Content-Type
                if (!headers['Content-Type'] && !headers['content-type']) {
                    fetchOptions.headers['Content-Type'] = 'application/json';
                }
            }

            // 在页面上下文中执行请求
            const result = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                func: async (url, options, timeout) => {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), timeout);

                    try {
                        const response = await fetch(url, {
                            ...options,
                            signal: controller.signal
                        });

                        clearTimeout(timeoutId);

                        const responseData = {
                            url: response.url,
                            status: response.status,
                            statusText: response.statusText,
                            headers: Object.fromEntries(response.headers.entries()),
                            ok: response.ok,
                            redirected: response.redirected,
                            type: response.type
                        };

                        // 尝试获取响应体
                        try {
                            const contentType = response.headers.get('content-type') || '';
                            if (contentType.includes('application/json')) {
                                responseData.data = await response.json();
                            } else if (contentType.includes('text/')) {
                                responseData.data = await response.text();
                            } else {
                                responseData.data = '[Binary Data]';
                            }
                        } catch (e) {
                            responseData.data = '[Parse Error]';
                        }

                        return { success: true, response: responseData };

                    } catch (error) {
                        clearTimeout(timeoutId);
                        return {
                            success: false,
                            error: error.message,
                            name: error.name
                        };
                    }
                },
                args: [url, fetchOptions, timeout]
            });

            const scriptResult = result[0].result;

            if (scriptResult.success) {
                return {
                    success: true,
                    request: {
                        url,
                        method: method.toUpperCase(),
                        headers,
                        body
                    },
                    response: scriptResult.response,
                    timestamp: Date.now()
                };
            } else {
                throw new Error(scriptResult.error || '网络请求失败');
            }

        } catch (error) {
            throw new Error(`执行网络请求失败: ${error.message}`);
        }
    }

    /**
     * 处理网络请求
     */
    handleNetworkRequest(details) {
        if (!this.networkRequests) return;

        // 应用过滤器
        if (this.networkFilters.length > 0) {
            const matchesFilter = this.networkFilters.some(filter =>
                details.url.includes(filter) || details.method === filter
            );
            if (!matchesFilter) return;
        }

        // 限制请求数量
        if (this.networkRequests.length >= this.maxNetworkRequests) {
            this.networkRequests.shift(); // 移除最旧的请求
        }

        this.networkRequests.push({
            id: details.requestId,
            url: details.url,
            method: details.method,
            type: details.type,
            timestamp: Date.now(),
            requestBody: details.requestBody
        });
    }

    /**
     * 处理网络响应
     */
    handleNetworkResponse(details) {
        if (!this.networkRequests) return;

        // 找到对应的请求并更新响应信息
        const request = this.networkRequests.find(req => req.id === details.requestId);
        if (request) {
            request.statusCode = details.statusCode;
            request.responseHeaders = details.responseHeaders;
            request.responseTime = Date.now() - request.timestamp;
        }
    }

    /**
     * 执行历史记录搜索
     */
    async executeHistory(args) {
        const { query = '', maxResults = 20, days = 7 } = args;

        try {
            const startTime = Date.now() - (days * 24 * 60 * 60 * 1000);

            const historyItems = await chrome.history.search({
                text: query,
                startTime: startTime,
                maxResults: maxResults
            });

            const results = historyItems.map(item => ({
                id: item.id,
                url: item.url,
                title: item.title,
                lastVisitTime: item.lastVisitTime,
                visitCount: item.visitCount,
                typedCount: item.typedCount
            }));

            return {
                success: true,
                query: query,
                results: results,
                totalResults: results.length,
                searchDays: days
            };

        } catch (error) {
            throw new Error(`搜索历史记录失败: ${error.message}`);
        }
    }

    /**
     * 执行书签搜索
     */
    async executeBookmarkSearch(args) {
        const { query, maxResults = 20 } = args;

        if (!query) {
            throw new Error('query参数是必需的');
        }

        try {
            const bookmarkTree = await chrome.bookmarks.getTree();
            const allBookmarks = this.flattenBookmarks(bookmarkTree);

            // 搜索匹配的书签
            const matchingBookmarks = allBookmarks.filter(bookmark =>
                bookmark.title.toLowerCase().includes(query.toLowerCase()) ||
                (bookmark.url && bookmark.url.toLowerCase().includes(query.toLowerCase()))
            ).slice(0, maxResults);

            const results = matchingBookmarks.map(bookmark => ({
                id: bookmark.id,
                title: bookmark.title,
                url: bookmark.url,
                parentId: bookmark.parentId,
                dateAdded: bookmark.dateAdded,
                dateGroupModified: bookmark.dateGroupModified
            }));

            return {
                success: true,
                query: query,
                results: results,
                totalResults: results.length
            };

        } catch (error) {
            throw new Error(`搜索书签失败: ${error.message}`);
        }
    }

    /**
     * 扁平化书签树
     */
    flattenBookmarks(bookmarkNodes) {
        let bookmarks = [];

        for (const node of bookmarkNodes) {
            if (node.url) {
                // 这是一个书签
                bookmarks.push(node);
            }

            if (node.children) {
                // 这是一个文件夹，递归处理子节点
                bookmarks = bookmarks.concat(this.flattenBookmarks(node.children));
            }
        }

        return bookmarks;
    }

    /**
     * 执行键盘输入
     */
    async executeKeyboard(args) {
        const { keys, text, delay = 100 } = args;

        if (!keys && !text) {
            throw new Error('必须提供keys或text参数');
        }

        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

        return new Promise((resolve, reject) => {
            chrome.tabs.sendMessage(tab.id, {
                action: 'KEYBOARD_INPUT',
                keys,
                text,
                delay
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    /**
     * 执行获取交互元素
     */
    async executeGetInteractiveElements(args) {
        const { types = [], visibleOnly = true, maxElements = 50 } = args;

        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

        return new Promise((resolve, reject) => {
            chrome.tabs.sendMessage(tab.id, {
                action: 'GET_INTERACTIVE_ELEMENTS',
                types,
                visibleOnly,
                maxElements
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    /**
     * 发送消息
     */
    send(message) {
        if (this.websocketClient && this.websocketClient.readyState === WebSocket.OPEN) {
            this.websocketClient.send(JSON.stringify(message));
        } else {
            console.warn('⚠️ WebSocket未连接，消息发送失败');
        }
    }

    /**
     * 安排重连
     */
    scheduleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

            console.log(`🔄 ${delay}ms后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

            setTimeout(() => {
                this.connect();
            }, delay);
        } else {
            console.error('❌ 达到最大重连次数，停止重连');
        }
    }

    /**
     * 手动重连
     */
    reconnect() {
        this.reconnectAttempts = 0;
        if (this.websocketClient) {
            this.websocketClient.close();
        }
        this.connect();
    }

    /**
     * 执行语义搜索标签页内容
     */
    async executeSearchTabsContent(args) {
        const { query, maxResults = 10, minScore = 0.7, includeContent = true } = args;

        if (!query) {
            throw new Error('query参数是必需的');
        }

        try {
            // 1. 获取所有标签页
            const windows = await chrome.windows.getAll({ populate: true });
            const allTabs = windows.flatMap(window => window.tabs);

            // 2. 收集标签页内容
            const tabContents = [];
            for (const tab of allTabs) {
                if (tab.status === 'complete' && !tab.url.startsWith('chrome://')) {
                    try {
                        const content = await this.getTabContent(tab.id);
                        if (content && content.success) {
                            tabContents.push({
                                tabId: tab.id,
                                url: tab.url,
                                title: tab.title,
                                content: content.content || '',
                                timestamp: Date.now()
                            });
                        }
                    } catch (error) {
                        console.warn(`获取标签页 ${tab.id} 内容失败:`, error.message);
                    }
                }
            }

            // 3. 发送到后端进行语义搜索
            const searchRequest = {
                action: 'SEMANTIC_SEARCH',
                query: query,
                maxResults: maxResults,
                minScore: minScore,
                includeContent: includeContent,
                tabContents: tabContents
            };

            // 通过WebSocket发送搜索请求
            const searchResult = await this.sendSemanticSearchRequest(searchRequest);

            return {
                success: true,
                query: query,
                results: searchResult.results || [],
                totalResults: searchResult.totalResults || 0,
                searchTime: searchResult.searchTime || 0,
                tabsSearched: tabContents.length
            };

        } catch (error) {
            throw new Error(`语义搜索失败: ${error.message}`);
        }
    }

    /**
     * 获取标签页内容
     */
    async getTabContent(tabId) {
        return new Promise((resolve) => {
            chrome.tabs.sendMessage(tabId, {
                action: 'GET_CONTENT',
                format: 'text',
                includeHidden: false
            }, (response) => {
                if (chrome.runtime.lastError) {
                    resolve({ success: false, error: chrome.runtime.lastError.message });
                } else {
                    resolve(response || { success: false });
                }
            });
        });
    }

    /**
     * 发送语义搜索请求到后端
     */
    async sendSemanticSearchRequest(request) {
        return new Promise((resolve, reject) => {
            if (!this.websocketClient || this.websocketClient.readyState !== WebSocket.OPEN) {
                reject(new Error('WebSocket连接未建立'));
                return;
            }

            const requestId = 'search_' + Date.now();
            const message = {
                type: 'SEMANTIC_SEARCH_REQUEST',
                requestId: requestId,
                ...request
            };

            // 设置响应监听器
            const responseHandler = (event) => {
                const data = JSON.parse(event.data);
                if (data.type === 'SEMANTIC_SEARCH_RESPONSE' && data.requestId === requestId) {
                    this.websocketClient.removeEventListener('message', responseHandler);
                    if (data.success) {
                        resolve(data);
                    } else {
                        reject(new Error(data.error || '语义搜索失败'));
                    }
                }
            };

            this.websocketClient.addEventListener('message', responseHandler);

            // 发送请求
            this.send(message);

            // 设置超时
            setTimeout(() => {
                this.websocketClient.removeEventListener('message', responseHandler);
                reject(new Error('语义搜索请求超时'));
            }, 30000); // 30秒超时
        });
    }

    /**
     * 获取活跃标签页（带错误处理）
     */
    async getActiveTab() {
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tabs.length === 0) {
            throw new Error('没有找到活跃的标签页');
        }
        return tabs[0];
    }

    /**
     * 延迟执行
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 准备页面进行截图
     */
    async preparePageForScreenshot(tabId, hideElements) {
        return new Promise((resolve) => {
            chrome.tabs.sendMessage(tabId, {
                action: 'PREPARE_SCREENSHOT',
                hideElements: hideElements || []
            }, (response) => {
                resolve(response || {});
            });
        });
    }

    /**
     * 恢复页面截图后状态
     */
    async restorePageAfterScreenshot(tabId) {
        return new Promise((resolve) => {
            chrome.tabs.sendMessage(tabId, {
                action: 'RESTORE_AFTER_SCREENSHOT'
            }, (response) => {
                resolve(response || {});
            });
        });
    }

    /**
     * 获取页面尺寸信息
     */
    async getPageDimensions(tabId) {
        return new Promise((resolve, reject) => {
            chrome.tabs.sendMessage(tabId, {
                action: 'GET_PAGE_DIMENSIONS'
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else if (response && response.success) {
                    resolve(response.dimensions);
                } else {
                    reject(new Error('获取页面尺寸失败'));
                }
            });
        });
    }

    /**
     * 滚动到指定位置
     */
    async scrollToPosition(tabId, y) {
        return new Promise((resolve) => {
            chrome.tabs.sendMessage(tabId, {
                action: 'SCROLL_TO_POSITION',
                y: y
            }, (response) => {
                resolve(response || {});
            });
        });
    }

    /**
     * 获取元素位置信息
     */
    async getElementPosition(tabId, selector) {
        return new Promise((resolve, reject) => {
            chrome.tabs.sendMessage(tabId, {
                action: 'GET_ELEMENT_POSITION',
                selector: selector
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else if (response && response.success) {
                    resolve(response.position);
                } else {
                    reject(new Error(response?.error || '获取元素位置失败'));
                }
            });
        });
    }

    /**
     * 滚动到元素位置
     */
    async scrollToElement(tabId, selector) {
        return new Promise((resolve) => {
            chrome.tabs.sendMessage(tabId, {
                action: 'SCROLL_TO_ELEMENT',
                selector: selector
            }, (response) => {
                resolve(response || {});
            });
        });
    }

    /**
     * 拼接截图
     */
    async stitchScreenshots(screenshots, pageInfo) {
        // 创建canvas进行图片拼接
        return new Promise((resolve, reject) => {
            try {
                const canvas = new OffscreenCanvas(pageInfo.scrollWidth, pageInfo.scrollHeight);
                const ctx = canvas.getContext('2d');

                let loadedCount = 0;
                const totalCount = screenshots.length;

                screenshots.forEach((screenshot, index) => {
                    const img = new Image();
                    img.onload = () => {
                        ctx.drawImage(img, 0, screenshot.y);
                        loadedCount++;

                        if (loadedCount === totalCount) {
                            canvas.convertToBlob().then(blob => {
                                const reader = new FileReader();
                                reader.onload = () => resolve(reader.result);
                                reader.readAsDataURL(blob);
                            });
                        }
                    };
                    img.onerror = () => reject(new Error(`加载截图 ${index} 失败`));
                    img.src = screenshot.dataUrl;
                });

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 裁剪图片
     */
    async cropImage(dataUrl, elementInfo) {
        return new Promise((resolve, reject) => {
            try {
                const img = new Image();
                img.onload = () => {
                    const canvas = new OffscreenCanvas(elementInfo.width, elementInfo.height);
                    const ctx = canvas.getContext('2d');

                    ctx.drawImage(
                        img,
                        elementInfo.x, elementInfo.y, elementInfo.width, elementInfo.height,
                        0, 0, elementInfo.width, elementInfo.height
                    );

                    canvas.convertToBlob().then(blob => {
                        const reader = new FileReader();
                        reader.onload = () => resolve(reader.result);
                        reader.readAsDataURL(blob);
                    });
                };
                img.onerror = () => reject(new Error('加载图片失败'));
                img.src = dataUrl;

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 执行关闭标签页
     */
    async executeCloseTabs(args) {
        const {
            tabIds = [],
            pattern = null,
            keepActive = true,
            confirmClose = false
        } = args;

        try {
            let tabsToClose = [];

            if (tabIds.length > 0) {
                // 关闭指定的标签页
                tabsToClose = tabIds;
            } else if (pattern) {
                // 根据模式匹配标签页
                const allTabs = await chrome.tabs.query({});
                tabsToClose = allTabs
                    .filter(tab => {
                        if (keepActive && tab.active) return false;
                        return tab.url.includes(pattern) || tab.title.includes(pattern);
                    })
                    .map(tab => tab.id);
            } else {
                throw new Error('必须提供tabIds或pattern参数');
            }

            if (tabsToClose.length === 0) {
                return {
                    success: true,
                    message: '没有找到匹配的标签页',
                    closedTabs: []
                };
            }

            // 确认关闭（如果需要）
            if (confirmClose && tabsToClose.length > 1) {
                // 这里可以添加确认逻辑
                console.log(`准备关闭 ${tabsToClose.length} 个标签页`);
            }

            // 批量关闭标签页
            const closedTabs = [];
            for (const tabId of tabsToClose) {
                try {
                    await chrome.tabs.remove(tabId);
                    closedTabs.push(tabId);
                } catch (error) {
                    console.warn(`关闭标签页 ${tabId} 失败:`, error.message);
                }
            }

            return {
                success: true,
                message: `成功关闭 ${closedTabs.length} 个标签页`,
                closedTabs: closedTabs,
                totalRequested: tabsToClose.length
            };

        } catch (error) {
            throw new Error(`关闭标签页失败: ${error.message}`);
        }
    }

    /**
     * 执行前进后退导航
     */
    async executeGoBackForward(args) {
        const { action, steps = 1 } = args;

        if (!['back', 'forward'].includes(action)) {
            throw new Error('action参数必须是back或forward');
        }

        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            // 执行导航
            for (let i = 0; i < steps; i++) {
                if (action === 'back') {
                    await chrome.tabs.goBack(tab.id);
                } else {
                    await chrome.tabs.goForward(tab.id);
                }

                // 等待导航完成
                await this.delay(500);
            }

            // 获取更新后的标签页信息
            const updatedTab = await chrome.tabs.get(tab.id);

            return {
                success: true,
                action: action,
                steps: steps,
                currentUrl: updatedTab.url,
                currentTitle: updatedTab.title,
                tabId: tab.id
            };

        } catch (error) {
            throw new Error(`导航失败: ${error.message}`);
        }
    }

    /**
     * 执行切换标签页
     */
    async executeSwitchTab(args) {
        const {
            tabId = null,
            direction = null,
            pattern = null,
            index = null
        } = args;

        try {
            let targetTab = null;

            if (tabId) {
                // 切换到指定ID的标签页
                targetTab = await chrome.tabs.get(tabId);
            } else if (direction) {
                // 按方向切换标签页
                const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
                const allTabs = await chrome.tabs.query({ currentWindow: true });

                const currentIndex = allTabs.findIndex(tab => tab.id === currentTab.id);
                let targetIndex;

                if (direction === 'next') {
                    targetIndex = (currentIndex + 1) % allTabs.length;
                } else if (direction === 'prev') {
                    targetIndex = currentIndex === 0 ? allTabs.length - 1 : currentIndex - 1;
                } else {
                    throw new Error('direction参数必须是next或prev');
                }

                targetTab = allTabs[targetIndex];
            } else if (pattern) {
                // 根据模式匹配标签页
                const allTabs = await chrome.tabs.query({ currentWindow: true });
                targetTab = allTabs.find(tab =>
                    tab.url.includes(pattern) || tab.title.includes(pattern)
                );

                if (!targetTab) {
                    throw new Error(`找不到匹配模式 "${pattern}" 的标签页`);
                }
            } else if (index !== null) {
                // 切换到指定索引的标签页
                const allTabs = await chrome.tabs.query({ currentWindow: true });
                if (index < 0 || index >= allTabs.length) {
                    throw new Error(`索引 ${index} 超出范围 (0-${allTabs.length - 1})`);
                }
                targetTab = allTabs[index];
            } else {
                throw new Error('必须提供tabId、direction、pattern或index参数之一');
            }

            // 激活目标标签页
            await chrome.tabs.update(targetTab.id, { active: true });

            // 确保窗口也被激活
            await chrome.windows.update(targetTab.windowId, { focused: true });

            return {
                success: true,
                switchedTo: {
                    tabId: targetTab.id,
                    url: targetTab.url,
                    title: targetTab.title,
                    index: targetTab.index
                },
                message: `已切换到标签页: ${targetTab.title}`
            };

        } catch (error) {
            throw new Error(`切换标签页失败: ${error.message}`);
        }
    }

    /**
     * 发送内容处理请求到后端
     */
    async sendContentProcessRequest(request) {
        return new Promise((resolve, reject) => {
            if (!this.websocketClient || this.websocketClient.readyState !== WebSocket.OPEN) {
                reject(new Error('WebSocket连接未建立'));
                return;
            }

            const requestId = 'content_' + Date.now();
            const message = {
                type: 'CONTENT_PROCESS_REQUEST',
                requestId: requestId,
                ...request
            };

            // 设置响应监听器
            const responseHandler = (event) => {
                const data = JSON.parse(event.data);
                if (data.type === 'CONTENT_PROCESS_RESPONSE' && data.requestId === requestId) {
                    this.websocketClient.removeEventListener('message', responseHandler);
                    if (data.success) {
                        resolve(data);
                    } else {
                        reject(new Error(data.error || '内容处理失败'));
                    }
                }
            };

            this.websocketClient.addEventListener('message', responseHandler);

            // 发送请求
            this.send(message);

            // 设置超时
            setTimeout(() => {
                this.websocketClient.removeEventListener('message', responseHandler);
                reject(new Error('内容处理请求超时'));
            }, 15000); // 15秒超时
        });
    }

    /**
     * 更新插件图标徽章
     */
    updateBadge(status) {
        const badgeConfig = {
            connected: { text: '●', color: '#4CAF50' },
            disconnected: { text: '●', color: '#F44336' },
            working: { text: '⚡', color: '#FF9800' }
        };
        
        const config = badgeConfig[status] || badgeConfig.disconnected;
        
        chrome.action.setBadgeText({ text: config.text });
        chrome.action.setBadgeBackgroundColor({ color: config.color });
    }
    
    /**
     * 获取插件状态
     */
    getStatus() {
        return {
            initialized: this.isInitialized,
            connected: this.websocketClient?.readyState === WebSocket.OPEN,
            availableTools: this.availableTools,
            version: chrome.runtime.getManifest().version
        };
    }
}

// 创建全局实例
const edgemindBackground = new EdgeMindBackground();

// 监听标签页更新事件，用于调试导航问题
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'loading' && changeInfo.url) {
        console.log(`📍 标签页开始加载: tabId=${tabId}, url=${changeInfo.url}`);
    } else if (changeInfo.status === 'complete') {
        console.log(`✅ 标签页加载完成: tabId=${tabId}, url=${tab.url}`);
    }
});

// 监听来自popup和content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'getStatus') {
        sendResponse(edgemindBackground.getStatus());
    } else if (request.action === 'reconnect') {
        edgemindBackground.reconnect();
        sendResponse({ success: true });
    } else if (request.action === 'INJECT_SCRIPT') {
        // 处理脚本注入请求
        handleScriptInjection(request, sender, sendResponse);
    }
    return true; // 保持消息通道开放
});

/**
 * 处理脚本注入
 */
async function handleScriptInjection(request, sender, sendResponse) {
    try {
        const { scriptName } = request;
        const tabId = sender.tab.id;

        await chrome.scripting.executeScript({
            target: { tabId },
            files: [`inject-scripts/${scriptName}`]
        });

        sendResponse({ success: true });
    } catch (error) {
        console.error('❌ 脚本注入失败:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// 监听标签页更新事件
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        console.log(`📄 标签页更新: ${tab.title} - ${tab.url}`);
    }
});

// 监听标签页激活事件
chrome.tabs.onActivated.addListener((activeInfo) => {
    console.log(`🎯 标签页激活: ${activeInfo.tabId}`);
});

// 插件安装/更新事件
chrome.runtime.onInstalled.addListener((details) => {
    if (details.reason === 'install') {
        console.log('🎉 EdgeMind浏览器自动化插件已安装');
    } else if (details.reason === 'update') {
        console.log('🔄 EdgeMind浏览器自动化插件已更新');
    }
});

// 导出全局实例（用于调试）
globalThis.edgemindBackground = edgemindBackground;
