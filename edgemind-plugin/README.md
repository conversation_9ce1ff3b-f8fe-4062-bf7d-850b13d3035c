# EdgeMind浏览器自动化插件

EdgeMind浏览器自动化插件是一个Chrome扩展，用于与EdgeMind-Cortex后端通信，实现AI驱动的浏览器自动化操作。

## 功能特性

### 🌐 核心功能
- **智能导航**: 自动打开网页和管理标签页
- **元素交互**: 智能点击、填写表单、键盘输入
- **内容获取**: 提取页面文本和HTML内容
- **截图功能**: 捕获页面或元素截图
- **网络监控**: 监控和分析网络请求

### 🔧 支持的工具
- `browser_navigate` - 导航到指定URL
- `browser_click` - 点击页面元素
- `browser_fill` - 填写表单字段
- `browser_screenshot` - 截取页面截图
- `browser_get_content` - 获取页面内容
- `browser_get_interactive_elements` - 获取可交互元素
- `browser_keyboard` - 模拟键盘输入
- `browser_history` - 搜索浏览器历史
- `browser_bookmark_*` - 书签管理
- `browser_search_tabs_content` - 语义搜索标签页内容

## 安装方法

### 开发者模式安装

1. **打开Chrome扩展管理页面**
   ```
   chrome://extensions/
   ```

2. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

3. **加载未打包的扩展程序**
   - 点击"加载已解压的扩展程序"
   - 选择 `edgemind-plugin` 文件夹

4. **验证安装**
   - 扩展程序列表中应该出现"EdgeMind Browser Automation Plugin"
   - 浏览器工具栏会显示EdgeMind图标

## 使用方法

### 1. 启动EdgeMind-Cortex后端

确保EdgeMind-Cortex服务正在运行：

```bash
cd edgemind-cortex
mvn spring-boot:run
```

服务默认运行在 `http://localhost:8080`

### 2. 连接插件

1. 点击浏览器工具栏中的EdgeMind图标
2. 查看连接状态，应该显示"已连接"
3. 如果显示"未连接"，点击"重新连接"按钮

### 3. 使用AI对话

在EdgeMind-Cortex的聊天界面中，输入浏览器相关的指令：

```
打开百度搜索天气
```

```
帮我填写这个表单
```

```
截取当前页面的截图
```

```
获取这个页面的主要内容
```

## 插件架构

```
edgemind-plugin/
├── manifest.json              # 扩展清单文件
├── background/
│   └── background.js          # 后台脚本，处理WebSocket通信
├── content/
│   └── content.js             # 内容脚本，页面交互桥梁
├── inject-scripts/            # 注入脚本，执行具体操作
│   ├── click-helper.js        # 点击操作
│   ├── fill-helper.js         # 填写操作
│   └── content-helper.js      # 内容获取
├── popup/                     # 弹出界面
│   ├── popup.html
│   └── popup.js
├── icons/                     # 图标文件
└── utils/
    └── common.js              # 通用工具函数
```

## 通信流程

1. **用户输入**: 在EdgeMind-Cortex聊天界面输入指令
2. **AI解析**: 后端AI解析指令并生成工具调用
3. **WebSocket通信**: 通过WebSocket发送工具调用到浏览器插件
4. **工具执行**: 插件执行相应的浏览器操作
5. **结果返回**: 执行结果通过WebSocket返回给后端
6. **AI响应**: 后端AI整合结果并返回给用户

## 开发调试

### 启用调试模式

在浏览器控制台中执行：

```javascript
localStorage.setItem('edgemind_debug', 'true');
```

### 查看日志

1. **后台脚本日志**:
   - 打开 `chrome://extensions/`
   - 找到EdgeMind插件，点击"检查视图"中的"背景页"

2. **内容脚本日志**:
   - 在目标页面按F12打开开发者工具
   - 查看Console标签页

3. **弹出界面日志**:
   - 右键点击插件图标，选择"检查弹出内容"

## 故障排除

### 连接问题

1. **检查后端服务**
   - 确保EdgeMind-Cortex服务正在运行
   - 访问 `http://localhost:8080/wkg/api/browser/status` 检查状态

2. **检查WebSocket连接**
   - 在后台脚本控制台查看连接日志
   - 确认WebSocket URL: `ws://localhost:8080/wkg/browser-automation`

3. **防火墙设置**
   - 确保端口8080未被防火墙阻止

### 工具执行问题

1. **权限问题**
   - 确保插件有足够的权限访问目标网站
   - 检查manifest.json中的permissions配置

2. **脚本注入失败**
   - 某些网站可能阻止脚本注入
   - 查看内容脚本控制台的错误信息

3. **元素定位失败**
   - CSS选择器可能不正确
   - 页面结构可能已发生变化

## 安全注意事项

1. **仅在可信网站使用**: 插件具有强大的页面操作能力
2. **定期更新**: 保持插件和后端服务的最新版本
3. **权限管理**: 根据需要调整插件权限
4. **数据隐私**: 插件不会收集或存储个人数据

## 版本信息

- **当前版本**: 1.0.0
- **兼容性**: Chrome 88+
- **Manifest版本**: V3

## 支持与反馈

如有问题或建议，请联系EdgeMind团队。
