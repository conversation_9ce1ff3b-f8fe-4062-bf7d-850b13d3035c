{"manifest_version": 3, "name": "EdgeMind Browser Automation Plugin", "version": "1.0.0", "description": "EdgeMind浏览器自动化插件，支持AI驱动的浏览器操作", "permissions": ["tabs", "activeTab", "scripting", "storage", "webRequest", "debugger", "history", "bookmarks", "webNavigation"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background/background.js", "type": "module"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content/content.js"], "run_at": "document_end", "all_frames": true}], "action": {"default_popup": "popup/popup.html", "default_title": "EdgeMind浏览器自动化", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["inject-scripts/*.js", "utils/*.js"], "matches": ["<all_urls>"]}], "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self';"}}