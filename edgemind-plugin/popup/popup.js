/**
 * EdgeMind浏览器自动化插件 - Popup脚本
 */

document.addEventListener('DOMContentLoaded', async function() {
    console.log('🎨 Popup界面已加载');
    
    // 获取DOM元素
    const elements = {
        loading: document.getElementById('loading'),
        content: document.getElementById('content'),
        connectionIndicator: document.getElementById('connectionIndicator'),
        connectionStatus: document.getElementById('connectionStatus'),
        serverVersion: document.getElementById('serverVersion'),
        pluginVersion: document.getElementById('pluginVersion'),
        toolCount: document.getElementById('toolCount'),
        toolsGrid: document.getElementById('toolsGrid'),
        reconnectBtn: document.getElementById('reconnectBtn'),
        settingsBtn: document.getElementById('settingsBtn')
    };
    
    // 显示加载状态
    showLoading();
    
    try {
        // 获取插件状态
        const status = await getPluginStatus();
        
        // 更新界面
        updateUI(status);
        
        // 隐藏加载状态
        hideLoading();
        
    } catch (error) {
        console.error('❌ 获取插件状态失败:', error);
        showError('无法获取插件状态');
    }
    
    // 绑定事件监听器
    bindEventListeners();
    
    /**
     * 获取插件状态
     */
    async function getPluginStatus() {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({ action: 'getStatus' }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }
    
    /**
     * 更新界面
     */
    function updateUI(status) {
        // 更新连接状态
        updateConnectionStatus(status.connected);
        
        // 更新版本信息
        elements.pluginVersion.textContent = status.version || '未知';
        elements.serverVersion.textContent = status.serverVersion || '未连接';
        
        // 更新工具信息
        const toolCount = status.availableTools ? status.availableTools.length : 0;
        elements.toolCount.textContent = toolCount;
        
        // 更新工具网格
        updateToolsGrid(status.availableTools || []);
    }
    
    /**
     * 更新连接状态
     */
    function updateConnectionStatus(connected) {
        if (connected) {
            elements.connectionIndicator.className = 'status-indicator status-connected';
            elements.connectionStatus.textContent = '已连接';
        } else {
            elements.connectionIndicator.className = 'status-indicator status-disconnected';
            elements.connectionStatus.textContent = '未连接';
        }
    }
    
    /**
     * 更新工具网格
     */
    function updateToolsGrid(tools) {
        elements.toolsGrid.innerHTML = '';
        
        const coreTools = [
            { name: 'browser_navigate', label: '导航', icon: '🧭' },
            { name: 'browser_click', label: '点击', icon: '👆' },
            { name: 'browser_fill', label: '填写', icon: '✏️' },
            { name: 'browser_screenshot', label: '截图', icon: '📸' },
            { name: 'browser_get_content', label: '内容', icon: '📄' },
            { name: 'browser_search_tabs_content', label: '搜索', icon: '🔍' }
        ];
        
        coreTools.forEach(tool => {
            const isAvailable = tools.includes(tool.name);
            const toolElement = createToolElement(tool, isAvailable);
            elements.toolsGrid.appendChild(toolElement);
        });
    }
    
    /**
     * 创建工具元素
     */
    function createToolElement(tool, isAvailable) {
        const div = document.createElement('div');
        div.className = 'tool-item';
        div.style.opacity = isAvailable ? '1' : '0.5';
        
        div.innerHTML = `
            <div style="font-size: 16px; margin-bottom: 4px;">${tool.icon}</div>
            <div>${tool.label}</div>
        `;
        
        if (isAvailable) {
            div.style.cursor = 'pointer';
            div.addEventListener('click', () => {
                showToolInfo(tool);
            });
        }
        
        return div;
    }
    
    /**
     * 显示工具信息
     */
    function showToolInfo(tool) {
        alert(`工具: ${tool.label}\n名称: ${tool.name}\n状态: 可用`);
    }
    
    /**
     * 绑定事件监听器
     */
    function bindEventListeners() {
        // 重新连接按钮
        elements.reconnectBtn.addEventListener('click', async () => {
            elements.reconnectBtn.textContent = '连接中...';
            elements.reconnectBtn.disabled = true;
            
            try {
                await reconnect();
                
                // 重新获取状态
                const status = await getPluginStatus();
                updateUI(status);
                
                elements.reconnectBtn.textContent = '重新连接';
                elements.reconnectBtn.disabled = false;
                
            } catch (error) {
                console.error('❌ 重新连接失败:', error);
                elements.reconnectBtn.textContent = '连接失败';
                setTimeout(() => {
                    elements.reconnectBtn.textContent = '重新连接';
                    elements.reconnectBtn.disabled = false;
                }, 2000);
            }
        });
        
        // 设置按钮
        elements.settingsBtn.addEventListener('click', () => {
            chrome.runtime.openOptionsPage();
        });
    }
    
    /**
     * 重新连接
     */
    async function reconnect() {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({ action: 'reconnect' }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else if (response.success) {
                    resolve();
                } else {
                    reject(new Error('重新连接失败'));
                }
            });
        });
    }
    
    /**
     * 显示加载状态
     */
    function showLoading() {
        elements.loading.style.display = 'block';
        elements.content.style.display = 'none';
    }
    
    /**
     * 隐藏加载状态
     */
    function hideLoading() {
        elements.loading.style.display = 'none';
        elements.content.style.display = 'block';
    }
    
    /**
     * 显示错误
     */
    function showError(message) {
        elements.loading.innerHTML = `
            <div style="color: #F44336; text-align: center;">
                <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                <div>${message}</div>
            </div>
        `;
    }
    
    // 定期更新状态
    setInterval(async () => {
        try {
            const status = await getPluginStatus();
            updateConnectionStatus(status.connected);
        } catch (error) {
            updateConnectionStatus(false);
        }
    }, 5000); // 每5秒更新一次
});
