<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EdgeMind浏览器自动化</title>
    <style>
        body {
            width: 350px;
            min-height: 400px;
            margin: 0;
            padding: 16px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .subtitle {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            backdrop-filter: blur(10px);
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .status-item:last-child {
            margin-bottom: 0;
        }
        
        .status-label {
            font-weight: 500;
        }
        
        .status-value {
            font-family: monospace;
            font-size: 12px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-connected {
            background-color: #4CAF50;
        }
        
        .status-disconnected {
            background-color: #F44336;
        }
        
        .status-working {
            background-color: #FF9800;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .tools-section {
            margin-bottom: 16px;
        }
        
        .section-title {
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }
        
        .tool-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            padding: 8px;
            text-align: center;
            font-size: 11px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .actions {
            display: flex;
            gap: 8px;
        }
        
        .btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .btn-secondary {
            background: rgba(0, 0, 0, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn-secondary:hover {
            background: rgba(0, 0, 0, 0.3);
        }
        
        .footer {
            text-align: center;
            font-size: 11px;
            opacity: 0.7;
            margin-top: 16px;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 2px solid white;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🌐 EdgeMind</div>
        <div class="subtitle">浏览器自动化插件</div>
    </div>
    
    <div class="loading" id="loading">
        <div class="spinner"></div>
        <div>正在连接...</div>
    </div>
    
    <div id="content" style="display: none;">
        <div class="status-card">
            <div class="status-item">
                <span class="status-label">
                    <span class="status-indicator" id="connectionIndicator"></span>
                    连接状态
                </span>
                <span class="status-value" id="connectionStatus">检查中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">服务器版本</span>
                <span class="status-value" id="serverVersion">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">插件版本</span>
                <span class="status-value" id="pluginVersion">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">可用工具</span>
                <span class="status-value" id="toolCount">-</span>
            </div>
        </div>
        
        <div class="tools-section">
            <div class="section-title">核心工具</div>
            <div class="tools-grid" id="toolsGrid">
                <!-- 工具列表将动态生成 -->
            </div>
        </div>
        
        <div class="actions">
            <button class="btn btn-primary" id="reconnectBtn">重新连接</button>
            <button class="btn btn-secondary" id="settingsBtn">设置</button>
        </div>
    </div>
    
    <div class="footer">
        <div>EdgeMind AI 浏览器自动化</div>
        <div>© 2024 EdgeMind Team</div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
