/**
 * EdgeMind浏览器自动化插件 - 内容脚本
 * 负责在页面中执行具体的自动化操作，作为background script和页面之间的桥梁
 */

(function() {
    'use strict';
    
    console.log('🔗 EdgeMind内容脚本已加载');
    
    /**
     * 监听来自background script的消息
     */
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        console.log('📨 内容脚本收到消息:', request);
        
        switch (request.action) {
            case 'CLICK_ELEMENT':
                handleClickElement(request, sendResponse);
                break;
                
            case 'FILL_ELEMENT':
                handleFillElement(request, sendResponse);
                break;
                
            case 'GET_CONTENT':
                handleGetContent(request, sendResponse);
                break;
                
            case 'GET_INTERACTIVE_ELEMENTS':
                handleGetInteractiveElements(request, sendResponse);
                break;
                
            case 'KEYBOARD_INPUT':
                handleKeyboardInput(request, sendResponse);
                break;

            case 'PREPARE_SCREENSHOT':
                handleScreenshotAction(request, sendResponse, 'PREPARE_SCREENSHOT_REQUEST', { hideElements: request.hideElements });
                break;

            case 'RESTORE_AFTER_SCREENSHOT':
                handleScreenshotAction(request, sendResponse, 'RESTORE_AFTER_SCREENSHOT_REQUEST');
                break;

            case 'GET_PAGE_DIMENSIONS':
                handleScreenshotAction(request, sendResponse, 'GET_PAGE_DIMENSIONS_REQUEST');
                break;

            case 'SCROLL_TO_POSITION':
                handleScreenshotAction(request, sendResponse, 'SCROLL_TO_POSITION_REQUEST', { y: request.y });
                break;

            case 'GET_ELEMENT_POSITION':
                handleScreenshotAction(request, sendResponse, 'GET_ELEMENT_POSITION_REQUEST', { selector: request.selector });
                break;

            case 'SCROLL_TO_ELEMENT':
                handleScreenshotAction(request, sendResponse, 'SCROLL_TO_ELEMENT_REQUEST', { selector: request.selector });
                break;

            default:
                sendResponse({
                    success: false,
                    error: `未知操作: ${request.action}`
                });
        }
        
        // 保持消息通道开放
        return true;
    });
    
    /**
     * 处理点击元素请求
     */
    async function handleClickElement(request, sendResponse) {
        try {
            const { selector, coordinates, waitForNavigation } = request;
            
            // 确保点击辅助脚本已加载
            await ensureScriptLoaded('click-helper.js', 'edgemindClickHelperLoaded');
            
            // 设置结果监听器
            const resultListener = (event) => {
                if (event.data.type === 'CLICK_RESULT') {
                    window.removeEventListener('message', resultListener);
                    sendResponse(event.data.result);
                }
            };
            window.addEventListener('message', resultListener);
            
            // 发送点击请求到注入脚本
            window.postMessage({
                type: 'CLICK_ELEMENT_REQUEST',
                selector,
                coordinates,
                waitForNavigation
            }, '*');
            
        } catch (error) {
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }
    
    /**
     * 处理填写元素请求
     */
    async function handleFillElement(request, sendResponse) {
        try {
            const { selector, value, clear } = request;
            
            // 确保填写辅助脚本已加载
            await ensureScriptLoaded('fill-helper.js', 'edgemindFillHelperLoaded');
            
            // 设置结果监听器
            const resultListener = (event) => {
                if (event.data.type === 'FILL_RESULT') {
                    window.removeEventListener('message', resultListener);
                    sendResponse(event.data.result);
                }
            };
            window.addEventListener('message', resultListener);
            
            // 发送填写请求到注入脚本
            window.postMessage({
                type: 'FILL_ELEMENT_REQUEST',
                selector,
                value,
                clear
            }, '*');
            
        } catch (error) {
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }
    
    /**
     * 处理获取内容请求
     */
    function handleGetContent(request, sendResponse) {
        try {
            const { format, selector, includeHidden } = request;
            
            let element = document;
            if (selector) {
                element = document.querySelector(selector);
                if (!element) {
                    throw new Error(`找不到元素: ${selector}`);
                }
            }
            
            let content;
            if (format === 'html') {
                content = element.innerHTML || element.outerHTML;
            } else {
                content = getTextContent(element, includeHidden);
            }
            
            sendResponse({
                success: true,
                content,
                format,
                selector,
                length: content.length,
                url: window.location.href,
                title: document.title
            });
            
        } catch (error) {
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }
    
    /**
     * 处理获取交互元素请求
     */
    function handleGetInteractiveElements(request, sendResponse) {
        try {
            const { types, visibleOnly, maxElements } = request;
            
            const elements = findInteractiveElements(types, visibleOnly, maxElements);
            
            sendResponse({
                success: true,
                elements,
                count: elements.length,
                url: window.location.href
            });
            
        } catch (error) {
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }
    
    /**
     * 处理键盘输入请求
     */
    function handleKeyboardInput(request, sendResponse) {
        try {
            const { keys, text, delay } = request;
            
            if (keys) {
                simulateKeySequence(keys, delay);
            } else if (text) {
                simulateTextInput(text, delay);
            } else {
                throw new Error('必须提供keys或text参数');
            }
            
            sendResponse({
                success: true,
                keys,
                text,
                delay
            });
            
        } catch (error) {
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }
    
    /**
     * 确保脚本已加载
     */
    function ensureScriptLoaded(scriptName, globalFlag) {
        return new Promise((resolve, reject) => {
            if (window[globalFlag]) {
                resolve();
                return;
            }

            // 使用chrome.scripting API注入脚本
            chrome.runtime.sendMessage({
                action: 'INJECT_SCRIPT',
                scriptName: scriptName
            }, (response) => {
                if (response && response.success) {
                    // 等待脚本初始化
                    setTimeout(() => {
                        if (window[globalFlag]) {
                            resolve();
                        } else {
                            reject(new Error(`脚本 ${scriptName} 加载失败`));
                        }
                    }, 100);
                } else {
                    reject(new Error(`无法加载脚本 ${scriptName}`));
                }
            });
        });
    }
    
    /**
     * 获取文本内容
     */
    function getTextContent(element, includeHidden) {
        if (element.nodeType === Node.TEXT_NODE) {
            return element.textContent;
        }
        
        let text = '';
        for (let child of element.childNodes) {
            if (child.nodeType === Node.TEXT_NODE) {
                text += child.textContent;
            } else if (child.nodeType === Node.ELEMENT_NODE) {
                const style = window.getComputedStyle(child);
                const isHidden = style.display === 'none' || 
                               style.visibility === 'hidden' || 
                               style.opacity === '0';
                
                if (includeHidden || !isHidden) {
                    text += getTextContent(child, includeHidden);
                }
            }
        }
        
        return text;
    }
    
    /**
     * 查找交互元素
     */
    function findInteractiveElements(types, visibleOnly, maxElements) {
        const selectors = {
            button: 'button, input[type="button"], input[type="submit"], input[type="reset"]',
            input: 'input, textarea',
            link: 'a[href]',
            select: 'select',
            checkbox: 'input[type="checkbox"]',
            radio: 'input[type="radio"]'
        };
        
        let query = '';
        if (types && types.length > 0) {
            query = types.map(type => selectors[type]).filter(Boolean).join(', ');
        } else {
            query = Object.values(selectors).join(', ');
        }
        
        const elements = Array.from(document.querySelectorAll(query));
        
        return elements
            .filter(element => {
                if (visibleOnly) {
                    const rect = element.getBoundingClientRect();
                    const style = window.getComputedStyle(element);
                    return style.display !== 'none' &&
                           style.visibility !== 'hidden' &&
                           style.opacity !== '0' &&
                           rect.width > 0 &&
                           rect.height > 0;
                }
                return true;
            })
            .slice(0, maxElements || 50)
            .map(element => {
                const rect = element.getBoundingClientRect();
                return {
                    tagName: element.tagName,
                    type: element.type,
                    id: element.id,
                    className: element.className,
                    text: element.textContent?.trim().substring(0, 100),
                    href: element.href,
                    value: element.value,
                    rect: {
                        top: rect.top,
                        left: rect.left,
                        width: rect.width,
                        height: rect.height
                    },
                    selector: generateSelector(element)
                };
            });
    }
    
    /**
     * 生成CSS选择器
     */
    function generateSelector(element) {
        if (element.id) {
            return `#${element.id}`;
        }
        
        if (element.className) {
            const classes = element.className.split(' ').filter(Boolean);
            if (classes.length > 0) {
                return `.${classes.join('.')}`;
            }
        }
        
        // 生成基于路径的选择器
        const path = [];
        let current = element;
        
        while (current && current !== document.body) {
            let selector = current.tagName.toLowerCase();
            
            if (current.id) {
                selector += `#${current.id}`;
                path.unshift(selector);
                break;
            }
            
            if (current.className) {
                const classes = current.className.split(' ').filter(Boolean);
                if (classes.length > 0) {
                    selector += `.${classes[0]}`;
                }
            }
            
            // 添加nth-child选择器
            const siblings = Array.from(current.parentNode?.children || []);
            const index = siblings.indexOf(current);
            if (index > 0) {
                selector += `:nth-child(${index + 1})`;
            }
            
            path.unshift(selector);
            current = current.parentNode;
        }
        
        return path.join(' > ');
    }
    
    /**
     * 模拟按键序列
     */
    function simulateKeySequence(keys, delay = 100) {
        const keySequence = keys.split('+');
        
        keySequence.forEach((key, index) => {
            setTimeout(() => {
                const event = new KeyboardEvent('keydown', {
                    key: key.trim(),
                    bubbles: true,
                    cancelable: true
                });
                document.activeElement?.dispatchEvent(event);
            }, index * delay);
        });
    }
    
    /**
     * 模拟文本输入
     */
    function simulateTextInput(text, delay = 50) {
        const activeElement = document.activeElement;
        if (!activeElement || !['INPUT', 'TEXTAREA'].includes(activeElement.tagName)) {
            throw new Error('没有活跃的输入元素');
        }
        
        for (let i = 0; i < text.length; i++) {
            setTimeout(() => {
                const char = text[i];
                activeElement.value += char;
                
                activeElement.dispatchEvent(new Event('input', { bubbles: true }));
            }, i * delay);
        }
    }

    /**
     * 处理截图相关操作
     */
    async function handleScreenshotAction(request, sendResponse, messageType, data = {}) {
        try {
            // 确保截图辅助脚本已加载
            await ensureScriptLoaded('screenshot-helper.js', 'edgemindScreenshotHelperLoaded');

            // 设置响应监听器
            const responseHandler = (event) => {
                if (event.source !== window) return;

                const expectedResponseType = messageType.replace('_REQUEST', '_RESULT');
                if (event.data.type === expectedResponseType) {
                    window.removeEventListener('message', responseHandler);
                    sendResponse(event.data.result);
                }
            };

            window.addEventListener('message', responseHandler);

            // 发送请求到注入脚本
            window.postMessage({
                type: messageType,
                ...data
            }, '*');

            // 设置超时
            setTimeout(() => {
                window.removeEventListener('message', responseHandler);
                sendResponse({
                    success: false,
                    error: '截图操作超时'
                });
            }, 10000); // 10秒超时

        } catch (error) {
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }

})();
