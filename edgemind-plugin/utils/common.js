/**
 * EdgeMind浏览器自动化插件 - 通用工具函数
 */

/**
 * 延迟执行
 */
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 重试执行函数
 */
async function retry(fn, maxAttempts = 3, delayMs = 1000) {
    let lastError;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            return await fn();
        } catch (error) {
            lastError = error;
            console.warn(`尝试 ${attempt}/${maxAttempts} 失败:`, error.message);
            
            if (attempt < maxAttempts) {
                await delay(delayMs * attempt); // 指数退避
            }
        }
    }
    
    throw lastError;
}

/**
 * 安全的JSON解析
 */
function safeJsonParse(str, defaultValue = null) {
    try {
        return JSON.parse(str);
    } catch (error) {
        console.warn('JSON解析失败:', error.message);
        return defaultValue;
    }
}

/**
 * 安全的JSON字符串化
 */
function safeJsonStringify(obj, defaultValue = '{}') {
    try {
        return JSON.stringify(obj);
    } catch (error) {
        console.warn('JSON字符串化失败:', error.message);
        return defaultValue;
    }
}

/**
 * 生成唯一ID
 */
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * 格式化时间戳
 */
function formatTimestamp(timestamp) {
    return new Date(timestamp).toLocaleString('zh-CN');
}

/**
 * 获取错误信息
 */
function getErrorMessage(error) {
    if (typeof error === 'string') {
        return error;
    }
    
    if (error && error.message) {
        return error.message;
    }
    
    return '未知错误';
}

/**
 * 验证URL
 */
function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

/**
 * 清理URL
 */
function cleanUrl(url) {
    if (!url) return '';
    
    // 如果没有协议，添加https://
    if (!url.match(/^https?:\/\//)) {
        url = 'https://' + url;
    }
    
    return url;
}

/**
 * 获取域名
 */
function getDomain(url) {
    try {
        return new URL(url).hostname;
    } catch (error) {
        return '';
    }
}

/**
 * 截断文本
 */
function truncateText(text, maxLength = 100) {
    if (!text || text.length <= maxLength) {
        return text;
    }
    
    return text.substring(0, maxLength) + '...';
}

/**
 * 防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 深拷贝对象
 */
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    
    if (obj instanceof Array) {
        return obj.map(item => deepClone(item));
    }
    
    if (typeof obj === 'object') {
        const cloned = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                cloned[key] = deepClone(obj[key]);
            }
        }
        return cloned;
    }
    
    return obj;
}

/**
 * 合并对象
 */
function mergeObjects(target, ...sources) {
    if (!target) target = {};
    
    sources.forEach(source => {
        if (source) {
            Object.keys(source).forEach(key => {
                if (source[key] !== null && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    target[key] = mergeObjects(target[key] || {}, source[key]);
                } else {
                    target[key] = source[key];
                }
            });
        }
    });
    
    return target;
}

/**
 * 检查对象是否为空
 */
function isEmpty(obj) {
    if (obj == null) return true;
    if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;
    if (typeof obj === 'object') return Object.keys(obj).length === 0;
    return false;
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 获取文件扩展名
 */
function getFileExtension(filename) {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
}

/**
 * 检查是否为移动设备
 */
function isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

/**
 * 获取浏览器信息
 */
function getBrowserInfo() {
    const ua = navigator.userAgent;
    let browser = 'Unknown';
    let version = 'Unknown';
    
    if (ua.indexOf('Chrome') > -1) {
        browser = 'Chrome';
        version = ua.match(/Chrome\/(\d+)/)?.[1] || 'Unknown';
    } else if (ua.indexOf('Firefox') > -1) {
        browser = 'Firefox';
        version = ua.match(/Firefox\/(\d+)/)?.[1] || 'Unknown';
    } else if (ua.indexOf('Safari') > -1) {
        browser = 'Safari';
        version = ua.match(/Version\/(\d+)/)?.[1] || 'Unknown';
    } else if (ua.indexOf('Edge') > -1) {
        browser = 'Edge';
        version = ua.match(/Edge\/(\d+)/)?.[1] || 'Unknown';
    }
    
    return { browser, version };
}

/**
 * 日志记录器
 */
class Logger {
    constructor(prefix = 'EdgeMind') {
        this.prefix = prefix;
    }
    
    log(message, ...args) {
        console.log(`[${this.prefix}] ${message}`, ...args);
    }
    
    info(message, ...args) {
        console.info(`[${this.prefix}] ℹ️ ${message}`, ...args);
    }
    
    warn(message, ...args) {
        console.warn(`[${this.prefix}] ⚠️ ${message}`, ...args);
    }
    
    error(message, ...args) {
        console.error(`[${this.prefix}] ❌ ${message}`, ...args);
    }
    
    debug(message, ...args) {
        if (this.isDebugMode()) {
            console.debug(`[${this.prefix}] 🐛 ${message}`, ...args);
        }
    }
    
    isDebugMode() {
        return localStorage.getItem('edgemind_debug') === 'true';
    }
}

// 导出所有函数（用于模块化环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        delay,
        retry,
        safeJsonParse,
        safeJsonStringify,
        generateId,
        formatTimestamp,
        getErrorMessage,
        isValidUrl,
        cleanUrl,
        getDomain,
        truncateText,
        debounce,
        throttle,
        deepClone,
        mergeObjects,
        isEmpty,
        formatFileSize,
        getFileExtension,
        isMobile,
        getBrowserInfo,
        Logger
    };
}

// 全局可用（用于浏览器环境）
if (typeof window !== 'undefined') {
    window.EdgeMindUtils = {
        delay,
        retry,
        safeJsonParse,
        safeJsonStringify,
        generateId,
        formatTimestamp,
        getErrorMessage,
        isValidUrl,
        cleanUrl,
        getDomain,
        truncateText,
        debounce,
        throttle,
        deepClone,
        mergeObjects,
        isEmpty,
        formatFileSize,
        getFileExtension,
        isMobile,
        getBrowserInfo,
        Logger
    };
}
