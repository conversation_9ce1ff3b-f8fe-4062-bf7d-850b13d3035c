/**
 * 截图辅助脚本
 * 在页面中注入，用于截图相关的页面操作
 */

(function() {
    'use strict';
    
    // 存储隐藏的元素，用于恢复
    let hiddenElements = [];
    let originalStyles = new Map();
    
    /**
     * 准备页面进行截图
     */
    function preparePageForScreenshot(hideElements) {
        try {
            // 隐藏指定元素
            if (hideElements && hideElements.length > 0) {
                hideElements.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {
                        // 保存原始样式
                        originalStyles.set(element, {
                            display: element.style.display,
                            visibility: element.style.visibility
                        });
                        
                        // 隐藏元素
                        element.style.display = 'none';
                        hiddenElements.push(element);
                    });
                });
            }
            
            // 隐藏常见的固定元素
            const commonFixedSelectors = [
                '[style*="position: fixed"]',
                '[style*="position:fixed"]',
                '.fixed',
                '.sticky',
                '.floating',
                '.popup',
                '.modal',
                '.tooltip',
                '.notification',
                '.cookie-banner',
                '.gdpr-banner'
            ];
            
            commonFixedSelectors.forEach(selector => {
                try {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {
                        const computedStyle = window.getComputedStyle(element);
                        if (computedStyle.position === 'fixed' || computedStyle.position === 'sticky') {
                            originalStyles.set(element, {
                                display: element.style.display,
                                visibility: element.style.visibility
                            });
                            element.style.visibility = 'hidden';
                            hiddenElements.push(element);
                        }
                    });
                } catch (e) {
                    // 忽略选择器错误
                }
            });
            
            // 停止所有动画
            const style = document.createElement('style');
            style.id = 'edgemind-screenshot-style';
            style.textContent = `
                *, *::before, *::after {
                    animation-duration: 0s !important;
                    animation-delay: 0s !important;
                    transition-duration: 0s !important;
                    transition-delay: 0s !important;
                }
            `;
            document.head.appendChild(style);
            
            window.postMessage({
                type: 'SCREENSHOT_PREPARE_RESULT',
                result: { success: true }
            }, '*');
            
        } catch (error) {
            window.postMessage({
                type: 'SCREENSHOT_PREPARE_RESULT',
                result: { success: false, error: error.message }
            }, '*');
        }
    }
    
    /**
     * 恢复页面截图后状态
     */
    function restorePageAfterScreenshot() {
        try {
            // 恢复隐藏的元素
            hiddenElements.forEach(element => {
                const originalStyle = originalStyles.get(element);
                if (originalStyle) {
                    element.style.display = originalStyle.display;
                    element.style.visibility = originalStyle.visibility;
                }
            });
            
            // 清理
            hiddenElements = [];
            originalStyles.clear();
            
            // 移除动画停止样式
            const style = document.getElementById('edgemind-screenshot-style');
            if (style) {
                style.remove();
            }
            
            window.postMessage({
                type: 'SCREENSHOT_RESTORE_RESULT',
                result: { success: true }
            }, '*');
            
        } catch (error) {
            window.postMessage({
                type: 'SCREENSHOT_RESTORE_RESULT',
                result: { success: false, error: error.message }
            }, '*');
        }
    }
    
    /**
     * 获取页面尺寸信息
     */
    function getPageDimensions() {
        try {
            const dimensions = {
                scrollWidth: Math.max(
                    document.body.scrollWidth,
                    document.documentElement.scrollWidth,
                    document.body.offsetWidth,
                    document.documentElement.offsetWidth,
                    document.body.clientWidth,
                    document.documentElement.clientWidth
                ),
                scrollHeight: Math.max(
                    document.body.scrollHeight,
                    document.documentElement.scrollHeight,
                    document.body.offsetHeight,
                    document.documentElement.offsetHeight,
                    document.body.clientHeight,
                    document.documentElement.clientHeight
                ),
                viewportWidth: window.innerWidth,
                viewportHeight: window.innerHeight,
                devicePixelRatio: window.devicePixelRatio || 1
            };
            
            window.postMessage({
                type: 'PAGE_DIMENSIONS_RESULT',
                result: { success: true, dimensions }
            }, '*');
            
        } catch (error) {
            window.postMessage({
                type: 'PAGE_DIMENSIONS_RESULT',
                result: { success: false, error: error.message }
            }, '*');
        }
    }
    
    /**
     * 滚动到指定位置
     */
    function scrollToPosition(y) {
        try {
            window.scrollTo(0, y);
            
            window.postMessage({
                type: 'SCROLL_POSITION_RESULT',
                result: { success: true, y }
            }, '*');
            
        } catch (error) {
            window.postMessage({
                type: 'SCROLL_POSITION_RESULT',
                result: { success: false, error: error.message }
            }, '*');
        }
    }
    
    /**
     * 获取元素位置信息
     */
    function getElementPosition(selector) {
        try {
            const element = document.querySelector(selector);
            if (!element) {
                throw new Error(`找不到元素: ${selector}`);
            }
            
            const rect = element.getBoundingClientRect();
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
            
            const position = {
                x: rect.left + scrollLeft,
                y: rect.top + scrollTop,
                width: rect.width,
                height: rect.height,
                viewportX: rect.left,
                viewportY: rect.top,
                visible: rect.width > 0 && rect.height > 0 && 
                        rect.bottom > 0 && rect.right > 0 &&
                        rect.top < window.innerHeight && rect.left < window.innerWidth
            };
            
            window.postMessage({
                type: 'ELEMENT_POSITION_RESULT',
                result: { success: true, position }
            }, '*');
            
        } catch (error) {
            window.postMessage({
                type: 'ELEMENT_POSITION_RESULT',
                result: { success: false, error: error.message }
            }, '*');
        }
    }
    
    /**
     * 滚动到元素位置
     */
    function scrollToElement(selector) {
        try {
            const element = document.querySelector(selector);
            if (!element) {
                throw new Error(`找不到元素: ${selector}`);
            }
            
            // 滚动到元素位置，使其在视口中央
            element.scrollIntoView({
                behavior: 'instant',
                block: 'center',
                inline: 'center'
            });
            
            window.postMessage({
                type: 'SCROLL_ELEMENT_RESULT',
                result: { success: true, selector }
            }, '*');
            
        } catch (error) {
            window.postMessage({
                type: 'SCROLL_ELEMENT_RESULT',
                result: { success: false, error: error.message }
            }, '*');
        }
    }
    
    // 监听来自content script的消息
    window.addEventListener('message', function(event) {
        if (event.source !== window) return;
        
        switch (event.data.type) {
            case 'PREPARE_SCREENSHOT_REQUEST':
                preparePageForScreenshot(event.data.hideElements);
                break;
            case 'RESTORE_AFTER_SCREENSHOT_REQUEST':
                restorePageAfterScreenshot();
                break;
            case 'GET_PAGE_DIMENSIONS_REQUEST':
                getPageDimensions();
                break;
            case 'SCROLL_TO_POSITION_REQUEST':
                scrollToPosition(event.data.y);
                break;
            case 'GET_ELEMENT_POSITION_REQUEST':
                getElementPosition(event.data.selector);
                break;
            case 'SCROLL_TO_ELEMENT_REQUEST':
                scrollToElement(event.data.selector);
                break;
        }
    });
    
    // 标记脚本已加载
    window.edgemindScreenshotHelperLoaded = true;
    
})();
