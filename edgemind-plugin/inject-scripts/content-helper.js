/**
 * 内容获取辅助脚本
 * 在页面中注入，用于获取页面内容
 */

(function() {
    'use strict';
    
    /**
     * 获取页面内容
     */
    function getContent(format, selector, includeHidden) {
        try {
            let content;
            let metadata = {};

            if (selector) {
                // 如果指定了选择器，获取对应元素
                const element = document.querySelector(selector);
                if (!element) {
                    throw new Error(`找不到元素: ${selector}`);
                }

                // 统一返回HTML，让Java后端处理
                content = element.outerHTML;
                metadata.selector = selector;
                metadata.isElement = true;
            } else {
                // 获取整个页面HTML
                content = document.documentElement.outerHTML;
                metadata.isElement = false;
            }

            // 获取页面元数据
            metadata.url = window.location.href;
            metadata.title = document.title;
            metadata.description = getMetaContent('description') || getMetaContent('og:description');
            metadata.author = getMetaContent('author') || getMetaContent('article:author');
            metadata.siteName = getMetaContent('og:site_name');
            metadata.keywords = getMetaContent('keywords');
            metadata.publishedTime = getMetaContent('article:published_time');
            metadata.modifiedTime = getMetaContent('article:modified_time');
            metadata.lang = document.documentElement.lang || 'unknown';
            metadata.charset = document.characterSet || 'UTF-8';

            // 添加结构化数据
            metadata.jsonLd = extractJsonLdData();

            // 请求的格式信息
            metadata.requestedFormat = format;
            metadata.includeHidden = includeHidden;
            metadata.timestamp = Date.now();
            
            // 如果是特定元素，获取元素信息
            if (selector && element !== document) {
                metadata.element = getElementInfo(element);
            }
            
            window.postMessage({
                type: 'CONTENT_RESULT',
                result: {
                    success: true,
                    content,
                    format,
                    selector,
                    includeHidden,
                    metadata
                }
            }, '*');
            
        } catch (error) {
            window.postMessage({
                type: 'CONTENT_RESULT',
                result: {
                    success: false,
                    error: error.message
                }
            }, '*');
        }
    }
    
    // 文本提取逻辑已迁移到Java后端

    // 智能内容提取已迁移到Java后端，这里不再需要

    // 文本处理和结构化数据提取已迁移到Java后端

    /**
     * 提取JSON-LD数据（简化版）
     */
    function extractJsonLdData() {
        try {
            const scripts = document.querySelectorAll('script[type="application/ld+json"]');
            const jsonLdData = [];

            for (const script of scripts) {
                try {
                    const jsonText = script.textContent.replace(/^\s*<!\[CDATA\[|\]\]>\s*$/g, '');
                    const parsed = JSON.parse(jsonText);
                    jsonLdData.push(parsed);
                } catch (e) {
                    // 忽略JSON解析错误
                }
            }

            return jsonLdData;
        } catch (error) {
            console.warn('提取JSON-LD数据失败:', error);
            return [];
        }
    }

    /**
     * 获取meta标签内容
     */
    function getMetaContent(name) {
        const meta = document.querySelector(`meta[name="${name}"], meta[property="${name}"]`);
        return meta ? meta.getAttribute('content') : '';
    }

    /**
     * 检查元素是否隐藏
     */
    function isElementHidden(element) {
        const style = window.getComputedStyle(element);
        return style.display === 'none' ||
               style.visibility === 'hidden' ||
               style.opacity === '0' ||
               element.hidden;
    }
    
    /**
     * 获取元素信息
     */
    function getElementInfo(element) {
        const rect = element.getBoundingClientRect();
        const style = window.getComputedStyle(element);
        
        return {
            tagName: element.tagName,
            id: element.id,
            className: element.className,
            rect: {
                top: rect.top,
                left: rect.left,
                width: rect.width,
                height: rect.height
            },
            style: {
                display: style.display,
                visibility: style.visibility,
                opacity: style.opacity
            },
            textLength: element.textContent?.length || 0,
            childElementCount: element.childElementCount
        };
    }
    
    /**
     * 获取页面结构信息
     */
    function getPageStructure() {
        try {
            const structure = {
                title: document.title,
                url: window.location.href,
                domain: window.location.hostname,
                protocol: window.location.protocol,
                
                // 页面元素统计
                elements: {
                    total: document.querySelectorAll('*').length,
                    headings: document.querySelectorAll('h1, h2, h3, h4, h5, h6').length,
                    paragraphs: document.querySelectorAll('p').length,
                    links: document.querySelectorAll('a[href]').length,
                    images: document.querySelectorAll('img').length,
                    forms: document.querySelectorAll('form').length,
                    inputs: document.querySelectorAll('input, textarea, select').length,
                    buttons: document.querySelectorAll('button, input[type="button"], input[type="submit"]').length
                },
                
                // 页面内容统计
                content: {
                    textLength: document.body?.textContent?.length || 0,
                    wordCount: (document.body?.textContent || '').split(/\s+/).filter(Boolean).length,
                    hasVideo: document.querySelectorAll('video').length > 0,
                    hasAudio: document.querySelectorAll('audio').length > 0,
                    hasCanvas: document.querySelectorAll('canvas').length > 0
                },
                
                // 技术信息
                tech: {
                    hasJQuery: typeof window.jQuery !== 'undefined',
                    hasReact: typeof window.React !== 'undefined',
                    hasVue: typeof window.Vue !== 'undefined',
                    hasAngular: typeof window.angular !== 'undefined',
                    frameworks: detectFrameworks()
                },
                
                // 页面性能
                performance: getPerformanceInfo(),
                
                timestamp: Date.now()
            };
            
            window.postMessage({
                type: 'PAGE_STRUCTURE_RESULT',
                result: {
                    success: true,
                    structure
                }
            }, '*');
            
        } catch (error) {
            window.postMessage({
                type: 'PAGE_STRUCTURE_RESULT',
                result: {
                    success: false,
                    error: error.message
                }
            }, '*');
        }
    }
    
    /**
     * 检测前端框架
     */
    function detectFrameworks() {
        const frameworks = [];
        
        // 检测常见框架
        if (typeof window.jQuery !== 'undefined') frameworks.push('jQuery');
        if (typeof window.React !== 'undefined') frameworks.push('React');
        if (typeof window.Vue !== 'undefined') frameworks.push('Vue.js');
        if (typeof window.angular !== 'undefined') frameworks.push('AngularJS');
        if (typeof window.ng !== 'undefined') frameworks.push('Angular');
        if (document.querySelector('[data-reactroot]')) frameworks.push('React');
        if (document.querySelector('[data-server-rendered]')) frameworks.push('Vue.js (SSR)');
        
        return frameworks;
    }
    
    /**
     * 获取性能信息
     */
    function getPerformanceInfo() {
        try {
            const navigation = performance.getEntriesByType('navigation')[0];
            return {
                loadTime: navigation ? Math.round(navigation.loadEventEnd - navigation.fetchStart) : null,
                domContentLoaded: navigation ? Math.round(navigation.domContentLoadedEventEnd - navigation.fetchStart) : null,
                firstPaint: getFirstPaint(),
                resourceCount: performance.getEntriesByType('resource').length
            };
        } catch (error) {
            return null;
        }
    }
    
    /**
     * 获取首次绘制时间
     */
    function getFirstPaint() {
        try {
            const paintEntries = performance.getEntriesByType('paint');
            const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
            return firstPaint ? Math.round(firstPaint.startTime) : null;
        } catch (error) {
            return null;
        }
    }
    
    // 监听来自content script的消息
    window.addEventListener('message', function(event) {
        if (event.source !== window) return;
        
        if (event.data.type === 'GET_CONTENT_REQUEST') {
            const { format, selector, includeHidden } = event.data;
            getContent(format, selector, includeHidden);
        } else if (event.data.type === 'GET_PAGE_STRUCTURE_REQUEST') {
            getPageStructure();
        }
    });
    
    // 标记脚本已加载
    window.edgemindContentHelperLoaded = true;
    
})();
