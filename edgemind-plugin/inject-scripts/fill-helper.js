/**
 * 填写辅助脚本
 * 在页面中注入，用于执行表单填写操作
 */

(function() {
    'use strict';
    
    /**
     * 填写元素
     */
    function fillElement(selector, value, clear) {
        try {
            const element = document.querySelector(selector);
            if (!element) {
                throw new Error(`找不到元素: ${selector}`);
            }
            
            // 检查元素是否可填写
            if (!isElementFillable(element)) {
                throw new Error('元素不可填写');
            }
            
            // 滚动到元素位置
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            // 等待滚动完成
            setTimeout(() => {
                // 聚焦元素
                element.focus();
                
                // 清空现有内容
                if (clear) {
                    element.value = '';
                    element.textContent = '';
                }
                
                // 填写内容
                if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                    // 输入框和文本域
                    fillInputElement(element, value);
                } else if (element.tagName === 'SELECT') {
                    // 下拉选择框
                    fillSelectElement(element, value);
                } else if (element.isContentEditable) {
                    // 可编辑元素
                    fillContentEditableElement(element, value);
                } else {
                    throw new Error('不支持的元素类型');
                }
                
                // 触发change事件
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));
                
                // 获取元素信息
                const elementInfo = getElementInfo(element);
                
                window.postMessage({
                    type: 'FILL_RESULT',
                    result: {
                        success: true,
                        elementInfo,
                        filledValue: element.value || element.textContent
                    }
                }, '*');
                
            }, 300);
            
        } catch (error) {
            window.postMessage({
                type: 'FILL_RESULT',
                result: {
                    success: false,
                    error: error.message
                }
            }, '*');
        }
    }
    
    /**
     * 填写输入元素
     */
    function fillInputElement(element, value) {
        const inputType = element.type?.toLowerCase();
        
        switch (inputType) {
            case 'text':
            case 'email':
            case 'password':
            case 'search':
            case 'url':
            case 'tel':
            case 'textarea':
                // 文本输入
                simulateTyping(element, value);
                break;
                
            case 'number':
            case 'range':
                // 数字输入
                element.value = value;
                break;
                
            case 'checkbox':
            case 'radio':
                // 复选框和单选框
                element.checked = Boolean(value);
                break;
                
            case 'date':
            case 'datetime-local':
            case 'time':
                // 日期时间输入
                element.value = value;
                break;
                
            case 'file':
                throw new Error('文件输入不支持直接填写');
                
            default:
                // 默认处理
                element.value = value;
        }
    }
    
    /**
     * 填写选择框
     */
    function fillSelectElement(element, value) {
        // 尝试按值匹配
        for (let option of element.options) {
            if (option.value === value || option.text === value) {
                option.selected = true;
                return;
            }
        }
        
        // 尝试模糊匹配
        for (let option of element.options) {
            if (option.text.toLowerCase().includes(value.toLowerCase())) {
                option.selected = true;
                return;
            }
        }
        
        throw new Error(`找不到匹配的选项: ${value}`);
    }
    
    /**
     * 填写可编辑元素
     */
    function fillContentEditableElement(element, value) {
        element.textContent = value;
        
        // 设置光标到末尾
        const range = document.createRange();
        const selection = window.getSelection();
        range.selectNodeContents(element);
        range.collapse(false);
        selection.removeAllRanges();
        selection.addRange(range);
    }
    
    /**
     * 模拟打字
     */
    function simulateTyping(element, text) {
        element.value = '';
        
        for (let i = 0; i < text.length; i++) {
            setTimeout(() => {
                element.value += text[i];
                
                // 触发键盘事件
                element.dispatchEvent(new KeyboardEvent('keydown', {
                    key: text[i],
                    bubbles: true
                }));
                element.dispatchEvent(new KeyboardEvent('keyup', {
                    key: text[i],
                    bubbles: true
                }));
            }, i * 50); // 50ms间隔模拟真实打字
        }
    }
    
    /**
     * 检查元素是否可填写
     */
    function isElementFillable(element) {
        const fillableTags = ['INPUT', 'TEXTAREA', 'SELECT'];
        
        // 检查标签类型
        if (fillableTags.includes(element.tagName)) {
            return true;
        }
        
        // 检查是否可编辑
        if (element.isContentEditable) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取元素信息
     */
    function getElementInfo(element) {
        const rect = element.getBoundingClientRect();
        const style = window.getComputedStyle(element);
        
        return {
            tagName: element.tagName,
            type: element.type,
            id: element.id,
            name: element.name,
            className: element.className,
            placeholder: element.placeholder,
            value: element.value,
            text: element.textContent?.trim().substring(0, 100),
            rect: {
                top: rect.top,
                left: rect.left,
                width: rect.width,
                height: rect.height
            },
            style: {
                display: style.display,
                visibility: style.visibility,
                opacity: style.opacity
            },
            attributes: Array.from(element.attributes).reduce((acc, attr) => {
                acc[attr.name] = attr.value;
                return acc;
            }, {})
        };
    }
    
    // 监听来自content script的消息
    window.addEventListener('message', function(event) {
        if (event.source !== window) return;
        
        if (event.data.type === 'FILL_ELEMENT_REQUEST') {
            const { selector, value, clear } = event.data;
            fillElement(selector, value, clear);
        }
    });
    
    // 标记脚本已加载
    window.edgemindFillHelperLoaded = true;
    
})();
