/**
 * 点击辅助脚本
 * 在页面中注入，用于执行元素点击操作
 */

(function() {
    'use strict';
    
    /**
     * 点击元素
     */
    function clickElement(selector, coordinates, waitForNavigation) {
        try {
            let element;
            
            if (selector) {
                // 使用CSS选择器查找元素
                element = document.querySelector(selector);
                if (!element) {
                    throw new Error(`找不到元素: ${selector}`);
                }
            } else if (coordinates) {
                // 使用坐标查找元素
                element = document.elementFromPoint(coordinates.x, coordinates.y);
                if (!element) {
                    throw new Error(`坐标 (${coordinates.x}, ${coordinates.y}) 处没有元素`);
                }
            } else {
                throw new Error('必须提供selector或coordinates参数');
            }
            
            // 检查元素是否可见和可点击
            if (!isElementVisible(element)) {
                throw new Error('元素不可见');
            }
            
            if (!isElementClickable(element)) {
                console.warn('元素可能不可点击，但仍尝试点击');
            }
            
            // 滚动到元素位置
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            // 等待滚动完成
            setTimeout(() => {
                // 获取元素信息
                const elementInfo = getElementInfo(element);
                
                // 执行点击
                const clickEvent = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true,
                    clientX: elementInfo.rect.left + elementInfo.rect.width / 2,
                    clientY: elementInfo.rect.top + elementInfo.rect.height / 2
                });
                
                element.dispatchEvent(clickEvent);
                
                // 如果需要等待导航
                if (waitForNavigation) {
                    // 监听页面变化
                    const originalUrl = window.location.href;
                    const checkNavigation = () => {
                        if (window.location.href !== originalUrl) {
                            return {
                                success: true,
                                elementInfo,
                                navigationOccurred: true,
                                newUrl: window.location.href
                            };
                        }
                        return {
                            success: true,
                            elementInfo,
                            navigationOccurred: false
                        };
                    };
                    
                    // 等待一段时间检查导航
                    setTimeout(() => {
                        window.postMessage({
                            type: 'CLICK_RESULT',
                            result: checkNavigation()
                        }, '*');
                    }, 1000);
                } else {
                    // 立即返回结果
                    window.postMessage({
                        type: 'CLICK_RESULT',
                        result: {
                            success: true,
                            elementInfo,
                            navigationOccurred: false
                        }
                    }, '*');
                }
            }, 300);
            
        } catch (error) {
            window.postMessage({
                type: 'CLICK_RESULT',
                result: {
                    success: false,
                    error: error.message
                }
            }, '*');
        }
    }
    
    /**
     * 检查元素是否可见
     */
    function isElementVisible(element) {
        const style = window.getComputedStyle(element);
        const rect = element.getBoundingClientRect();
        
        return style.display !== 'none' &&
               style.visibility !== 'hidden' &&
               style.opacity !== '0' &&
               rect.width > 0 &&
               rect.height > 0 &&
               rect.top < window.innerHeight &&
               rect.bottom > 0 &&
               rect.left < window.innerWidth &&
               rect.right > 0;
    }
    
    /**
     * 检查元素是否可点击
     */
    function isElementClickable(element) {
        const clickableTags = ['A', 'BUTTON', 'INPUT', 'SELECT', 'TEXTAREA'];
        const clickableTypes = ['button', 'submit', 'reset', 'checkbox', 'radio'];
        
        // 检查标签类型
        if (clickableTags.includes(element.tagName)) {
            return true;
        }
        
        // 检查input类型
        if (element.tagName === 'INPUT' && clickableTypes.includes(element.type)) {
            return true;
        }
        
        // 检查是否有点击事件监听器
        const style = window.getComputedStyle(element);
        if (style.cursor === 'pointer') {
            return true;
        }
        
        // 检查onclick属性
        if (element.onclick || element.getAttribute('onclick')) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取元素信息
     */
    function getElementInfo(element) {
        const rect = element.getBoundingClientRect();
        const style = window.getComputedStyle(element);
        
        return {
            tagName: element.tagName,
            id: element.id,
            className: element.className,
            text: element.textContent?.trim().substring(0, 100),
            rect: {
                top: rect.top,
                left: rect.left,
                width: rect.width,
                height: rect.height
            },
            style: {
                display: style.display,
                visibility: style.visibility,
                opacity: style.opacity,
                cursor: style.cursor
            },
            attributes: Array.from(element.attributes).reduce((acc, attr) => {
                acc[attr.name] = attr.value;
                return acc;
            }, {})
        };
    }
    
    // 监听来自content script的消息
    window.addEventListener('message', function(event) {
        if (event.source !== window) return;
        
        if (event.data.type === 'CLICK_ELEMENT_REQUEST') {
            const { selector, coordinates, waitForNavigation } = event.data;
            clickElement(selector, coordinates, waitForNavigation);
        }
    });
    
    // 标记脚本已加载
    window.edgemindClickHelperLoaded = true;
    
})();
