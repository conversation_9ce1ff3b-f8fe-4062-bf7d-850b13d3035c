<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 
          http://maven.apache.org/xsd/settings-1.0.0.xsd">

    <!-- 本地仓库路径 -->
    <localRepository>${user.home}/.m2/repository</localRepository>

    <!-- 镜像配置 - 使用阿里云镜像加速 -->
    <mirrors>
        <!-- 阿里云中央仓库镜像 -->
        <mirror>
            <id>aliyun-central</id>
            <mirrorOf>central</mirrorOf>
            <name>Aliyun Central Repository</name>
            <url>https://maven.aliyun.com/repository/central</url>
        </mirror>
        
        <!-- 阿里云公共仓库镜像 -->
        <mirror>
            <id>aliyun-public</id>
            <mirrorOf>*</mirrorOf>
            <name>Aliyun Public Repository</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </mirror>
    </mirrors>

    <!-- 配置文件激活 -->
    <profiles>
        <profile>
            <id>aliyun</id>
            <repositories>
                <!-- 阿里云中央仓库 -->
                <repository>
                    <id>aliyun-central</id>
                    <name>Aliyun Central Repository</name>
                    <url>https://maven.aliyun.com/repository/central</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                
                <!-- 阿里云公共仓库 -->
                <repository>
                    <id>aliyun-public</id>
                    <name>Aliyun Public Repository</name>
                    <url>https://maven.aliyun.com/repository/public</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                
                <!-- 阿里云Spring仓库 -->
                <repository>
                    <id>aliyun-spring</id>
                    <name>Aliyun Spring Repository</name>
                    <url>https://maven.aliyun.com/repository/spring</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                
                <!-- 阿里云Spring插件仓库 -->
                <repository>
                    <id>aliyun-spring-plugin</id>
                    <name>Aliyun Spring Plugin Repository</name>
                    <url>https://maven.aliyun.com/repository/spring-plugin</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
            </repositories>
            
            <pluginRepositories>
                <!-- 阿里云插件仓库 -->
                <pluginRepository>
                    <id>aliyun-plugin</id>
                    <name>Aliyun Plugin Repository</name>
                    <url>https://maven.aliyun.com/repository/central</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>

    <!-- 激活配置文件 -->
    <activeProfiles>
        <activeProfile>aliyun</activeProfile>
    </activeProfiles>

</settings>