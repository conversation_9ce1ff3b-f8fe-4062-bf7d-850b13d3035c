package com.zibbava.edgemind.server.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.Set;

/**
 * 简单的爬虫检测工具类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
public class BotDetectionUtil {

    // 常见爬虫User-Agent关键词（不区分大小写）
    private static final Set<String> BOT_KEYWORDS = Set.of(
        // 搜索引擎爬虫
        "googlebot", "bingbot", "slurp", "duckduckbot", "baiduspider", "yandexbot",
        "facebookexternalhit", "twitterbot", "linkedinbot", "whatsapp", "telegrambot",
        
        // 通用爬虫
        "spider", "crawler", "bot", "scraper", "fetcher", "parser", "extractor",
        "harvester", "indexer", "scanner", "monitor", "checker", "validator",
        
        // 具体爬虫工具
        "curl", "wget", "httpie", "postman", "insomnia", "python-requests",
        "java", "go-http-client", "okhttp", "apache-httpclient", "node-fetch",
        
        // 自动化工具
        "selenium", "phantomjs", "headlesschrome", "puppeteer", "playwright",
        "webdriver", "chromedriver", "geckodriver",
        
        // 监控工具
        "pingdom", "uptimerobot", "statuscake", "newrelic", "datadog",
        "nagios", "zabbix", "prometheus",
        
        // 安全扫描
        "nmap", "masscan", "zmap", "nikto", "sqlmap", "burpsuite",
        "owasp", "w3af", "skipfish", "arachni",
        
        // 其他常见爬虫
        "ahrefsbot", "semrushbot", "mj12bot", "dotbot", "rogerbot",
        "exabot", "facebot", "ia_archiver", "archive.org_bot"
    );

    /**
     * 检测User-Agent是否为爬虫
     *
     * @param userAgent User-Agent字符串
     * @return true-是爬虫，false-不是爬虫
     */
    public static boolean isBot(String userAgent) {
        if (!StringUtils.hasText(userAgent)) {
            // 没有User-Agent，可能是爬虫
            return true;
        }

        String userAgentLower = userAgent.toLowerCase();

        // 检查是否包含爬虫关键词
        for (String keyword : BOT_KEYWORDS) {
            if (userAgentLower.contains(keyword)) {
                log.debug("检测到爬虫User-Agent: {} (关键词: {})", userAgent, keyword);
                return true;
            }
        }

        return false;
    }

    /**
     * 检测User-Agent是否为爬虫（带详细信息）
     *
     * @param userAgent User-Agent字符串
     * @return 检测结果信息
     */
    public static BotDetectionResult detectBot(String userAgent) {
        if (!StringUtils.hasText(userAgent)) {
            return new BotDetectionResult(true, "缺少User-Agent");
        }

        String userAgentLower = userAgent.toLowerCase();

        // 检查是否包含爬虫关键词
        for (String keyword : BOT_KEYWORDS) {
            if (userAgentLower.contains(keyword)) {
                return new BotDetectionResult(true, "包含爬虫关键词: " + keyword);
            }
        }

        return new BotDetectionResult(false, "正常User-Agent");
    }

    /**
     * 爬虫检测结果
     */
    public static class BotDetectionResult {
        private final boolean isBot;
        private final String reason;

        public BotDetectionResult(boolean isBot, String reason) {
            this.isBot = isBot;
            this.reason = reason;
        }

        public boolean isBot() {
            return isBot;
        }

        public String getReason() {
            return reason;
        }

        @Override
        public String toString() {
            return String.format("BotDetectionResult{isBot=%s, reason='%s'}", isBot, reason);
        }
    }
}
