package com.zibbava.edgemind.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zibbava.edgemind.server.dto.DownloadStatisticsDTO;
import com.zibbava.edgemind.server.entity.DownloadStatistics;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 下载统计服务接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface DownloadStatisticsService extends IService<DownloadStatistics> {

    /**
     * 记录下载统计
     *
     * @param request HTTP请求
     * @param fileName 文件名
     * @param fileSize 文件大小
     * @param downloadStatus 下载状态
     */
    void recordDownload(HttpServletRequest request, String fileName, Long fileSize, Integer downloadStatus);

    /**
     * 获取下载统计概览
     *
     * @return 下载统计概览
     */
    DownloadStatisticsDTO getStatisticsOverview();

    /**
     * 获取总下载次数
     *
     * @return 总下载次数
     */
    Long getTotalDownloads();

    /**
     * 获取今日下载次数
     *
     * @return 今日下载次数
     */
    Long getTodayDownloads();

    /**
     * 获取本月下载次数
     *
     * @return 本月下载次数
     */
    Long getThisMonthDownloads();

    /**
     * 获取独立IP数量
     *
     * @return 独立IP数量
     */
    Long getUniqueIpCount();

    /**
     * 获取最近7天的下载统计
     *
     * @return 最近7天的下载统计
     */
    List<Map<String, Object>> getRecentSevenDaysStatistics();

    /**
     * 获取最近30天的下载统计
     *
     * @return 最近30天的下载统计
     */
    List<Map<String, Object>> getRecentThirtyDaysStatistics();

    /**
     * 获取指定日期范围的下载统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 下载统计
     */
    List<Map<String, Object>> getStatisticsByDateRange(LocalDate startDate, LocalDate endDate);

    /**
     * 获取客户端真实IP地址
     *
     * @param request HTTP请求
     * @return 客户端IP地址
     */
    String getClientIpAddress(HttpServletRequest request);
}