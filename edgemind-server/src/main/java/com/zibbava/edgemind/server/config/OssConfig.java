package com.zibbava.edgemind.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云OSS配置
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "aliyun.oss")
public class OssConfig {

    /**
     * OSS直接下载地址
     */
    private String downloadUrl = "https://zibbava-jjj.oss-cn-shenzhen.aliyuncs.com/duanzhi.zip";

    /**
     * 获取下载URL
     *
     * @return 下载URL
     */
    public String getDownloadUrl() {
        return downloadUrl;
    }
}
