package com.zibbava.edgemind.server.controller;


import com.zibbava.edgemind.server.config.OssConfig;
import com.zibbava.edgemind.server.service.DownloadStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.servlet.http.HttpServletRequest;

@Slf4j
@Controller
@RequiredArgsConstructor
public class WebController {

    private final DownloadStatisticsService downloadStatisticsService;
    private final OssConfig ossConfig;

    // --- 官网页面路由 ---
    @GetMapping("/")
    public String website(Model model) {
        return "website"; // 返回官网页面
    }

    // 安装指导页面已移除，直接在主页显示免费使用

    // --- 价格页面路由 ---
    @GetMapping("/pricing")
    public String pricing(Model model) {
        return "pricing"; // 返回价格页面
    }

    // --- 文件下载接口（重定向到OSS） ---
    @GetMapping("/download")
    public ResponseEntity<Void> downloadFile(HttpServletRequest request) {
        String fileName = "duanzhi.zip";
        Integer downloadStatus = 1; // 1-成功，0-失败

        try {
            // 获取OSS下载链接
            String ossDownloadUrl = ossConfig.getDownloadUrl();
            log.info("请求下载: {} -> {}", fileName, ossDownloadUrl);

            // 记录下载统计
            String clientIp = downloadStatisticsService.getClientIpAddress(request);
            log.info("记录下载统计: 文件={}, 客户端IP={}, User-Agent={}",
                fileName, clientIp, request.getHeader("User-Agent"));

            // 使用实际文件大小
            long fileSize = 2847 * 1024 * 1024;
            downloadStatisticsService.recordDownload(request, fileName, fileSize, downloadStatus);

            // 重定向到OSS下载链接
            HttpHeaders headers = new HttpHeaders();
            headers.setLocation(java.net.URI.create(ossDownloadUrl));

            log.info("重定向用户到OSS下载: {} -> {}", clientIp, ossDownloadUrl);

            return ResponseEntity.status(302) // 302 Found (临时重定向)
                    .headers(headers)
                    .build();

        } catch (Exception e) {
            log.error("处理下载请求失败", e);
            downloadStatus = 0;
            // 记录下载失败统计
            downloadStatisticsService.recordDownload(request, fileName, 0L, downloadStatus);
            return ResponseEntity.internalServerError().build();
        }
    }


}
