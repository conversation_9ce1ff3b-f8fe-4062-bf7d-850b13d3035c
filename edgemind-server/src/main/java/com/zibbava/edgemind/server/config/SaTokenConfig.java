package com.zibbava.edgemind.server.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token配置
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

    /**
     * 注册Sa-Token拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，打开注解式鉴权功能
        registry.addInterceptor(new SaInterceptor(handle -> {
                    StpUtil.checkLogin();
                }))
                .addPathPatterns("/**") // 拦截所有路径
                .excludePathPatterns(
                        "/", "/admin/login","**.html", "/css/**", "/js/**", "/images/**", "/fonts/**","/error/**",
                        "/api/license-records/free-trial/generate", //免费1个月接口
                        "/captcha/**", //验证码
                        "/pricing", //价格页面
                        "/download"
                )
                // 以下是排除不需要二次认证的流式响应接口
                .excludePathPatterns(
                        "/api/**/stream"
                );
    }
}