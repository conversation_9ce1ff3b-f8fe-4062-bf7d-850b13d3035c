package com.zibbava.edgemind.server.common;

import com.zibbava.edgemind.server.common.enums.ResultCode;
import com.zibbava.edgemind.server.common.excepttion.BusinessException;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 通用响应结果类
 *
 * @param <T> 数据类型
 */
@Data
@Getter
@Setter
public class Result<T> {

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 消息
     */
    private String message;

    /**
     * 数据
     */
    private T data;

    public Result() {
    }

    /**
     * 创建响应结果
     * 
     * @param code 状态码
     * @param message 消息
     * @param data 数据
     */
    private Result(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * 成功
     *
     * @return 不含数据的成功结果
     */
    public static <T> Result<T> success() {
        return new Result<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), null);
    }

    /**
     * 成功
     *
     * @param data 数据
     * @param <T> 数据类型
     * @return 包含数据的成功结果
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), data);
    }

    /**
     * 成功
     *
     * @param message 消息
     * @param data 数据
     * @param <T> 数据类型
     * @return 包含自定义消息和数据的成功结果
     */
    public static <T> Result<T> success(String message, T data) {
        return new Result<>(ResultCode.SUCCESS.getCode(), message, data);
    }

    /**
     * 基于预定义错误码创建失败结果
     * 
     * @param resultCode 错误码枚举
     * @param <T> 数据类型
     * @return 失败结果
     */
    public static <T> Result<T> error(ResultCode resultCode) {
        return new Result<>(resultCode.getCode(), resultCode.getMessage(), null);
    }
    
    /**
     * 基于预定义错误码和自定义消息创建失败结果
     * 
     * @param resultCode 错误码枚举
     * @param message 自定义消息，将覆盖错误码默认消息
     * @param <T> 数据类型
     * @return 失败结果
     */
    public static <T> Result<T> error(ResultCode resultCode, String message) {
        return new Result<>(resultCode.getCode(), message, null);
    }
    
    /**
     * 从业务异常创建失败结果
     * 
     * @param e 业务异常
     * @param <T> 数据类型
     * @return 失败结果
     */
    public static <T> Result<T> error(BusinessException e) {
        return new Result<>(e.getCode(), e.getDetailMessage(), null);
    }

    /**
     * 失败
     *
     * @param message 消息
     * @param <T> 数据类型
     * @return 失败结果
     */
    public static <T> Result<T> error(String message) {
        return new Result<>(ResultCode.OPERATION_FAILED.getCode(), message, null);
    }

    /**
     * 失败
     *
     * @param code 状态码
     * @param message 消息
     * @param <T> 数据类型
     * @return 失败结果
     */
    public static <T> Result<T> error(Integer code, String message) {
        return new Result<>(code, message, null);
    }
}
