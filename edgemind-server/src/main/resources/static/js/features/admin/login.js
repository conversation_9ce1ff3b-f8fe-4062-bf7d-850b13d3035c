/**
 * Login 页面功能模块
 */
class LoginManager {
    constructor() {
        this.init();
    }

    /**
     * 初始化页面
     */
    init() {
        this.bindEvents();
        this.setupKeyboardShortcuts();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 表单提交事件
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // 密码显示切换事件
        const passwordToggle = document.querySelector('.password-toggle');
        if (passwordToggle) {
            passwordToggle.addEventListener('click', () => this.togglePassword());
        }

        // 输入框焦点事件
        this.setupInputFocusEffects();
        
        // 验证码刷新按钮事件
        const captchaRefresh = document.querySelector('.captcha-refresh');
        if (captchaRefresh) {
            captchaRefresh.addEventListener('click', () => this.refreshCaptcha());
        }
        
        // 初始化验证码
        this.refreshCaptcha();
    }

    /**
     * 设置输入框焦点效果
     */
    setupInputFocusEffects() {
        const inputs = document.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });
            
            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
        });
    }

    /**
     * 设置键盘快捷键
     */
    setupKeyboardShortcuts() {
        // 回车键登录
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const loginForm = document.getElementById('loginForm');
                if (loginForm) {
                    loginForm.dispatchEvent(new Event('submit'));
                }
            }
        });
    }

    /**
     * 处理登录表单提交
     */
    async handleLogin(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const captcha = document.getElementById('captcha').value.trim();
        const rememberMe = document.getElementById('rememberMe').checked;
        
        // 表单验证
        if (!this.validateForm(username, password, captcha)) {
            return;
        }
        
        this.setLoading(true);
        
        try {
            const response = await ApiUtils.post('/admin/login', {
                username: username,
                password: password,
                captcha: captcha,
                rememberMe: rememberMe
            }, {
                showLoading: false,
                showError: false
            });
            
            // 延迟跳转，让用户看到成功提示
            setTimeout(() => {
                window.location.href = '/aistudio/admin/main';
            }, 1000);
            
        } catch (error) {
            console.error('登录失败:', error);
            this.showAlert(error.message || '登录失败，请检查输入信息', 'danger');
            // 登录失败时刷新验证码
            this.refreshCaptcha();
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * 表单验证
     */
    validateForm(username, password, captcha) {
        if (!username || !password || !captcha) {
            this.showAlert('请填写完整的登录信息', 'danger');
            return false;
        }

        if (username.length < 3) {
            this.showAlert('用户名长度不能少于3个字符', 'danger');
            return false;
        }

        if (password.length < 6) {
            this.showAlert('密码长度不能少于6个字符', 'danger');
            return false;
        }

        if (captcha.length !== 4) {
            this.showAlert('验证码必须为4位字符', 'danger');
            return false;
        }

        return true;
    }

    /**
     * 显示提示信息
     */
    showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alertContainer');
        if (!alertContainer) {
            console.warn('Alert container not found');
            return;
        }

        alertContainer.innerHTML = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // 自动隐藏成功和信息提示
        if (type === 'success' || type === 'info') {
            setTimeout(() => {
                const alert = alertContainer.querySelector('.alert');
                if (alert && typeof bootstrap !== 'undefined') {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 3000);
        }
    }

    /**
     * 设置加载状态
     */
    setLoading(loading) {
        const loginBtn = document.getElementById('loginBtn');
        const spinner = loginBtn.querySelector('.loading-spinner');
        const btnText = loginBtn.querySelector('.btn-text');
        
        if (!loginBtn || !spinner || !btnText) {
            console.warn('Login button elements not found');
            return;
        }
        
        if (loading) {
            loginBtn.disabled = true;
            spinner.style.display = 'inline-block';
            btnText.textContent = '登录中...';
        } else {
            loginBtn.disabled = false;
            spinner.style.display = 'none';
            btnText.textContent = '登录';
        }
    }

    /**
     * 切换密码显示
     */
    togglePassword() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.getElementById('passwordToggleIcon');
        
        if (!passwordInput || !toggleIcon) {
            console.warn('Password toggle elements not found');
            return;
        }
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'bi bi-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'bi bi-eye';
        }
    }

    /**
     * 清空表单
     */
    clearForm() {
        const form = document.getElementById('loginForm');
        if (form) {
            form.reset();
        }
        
        // 清空提示信息
        const alertContainer = document.getElementById('alertContainer');
        if (alertContainer) {
            alertContainer.innerHTML = '';
        }
    }

    /**
     * 设置表单焦点
     */
    focusFirstInput() {
        const usernameInput = document.getElementById('username');
        if (usernameInput) {
            usernameInput.focus();
        }
    }
    
    /**
     * 刷新验证码
     */
    refreshCaptcha() {
        const captchaImage = document.getElementById('captchaImage');
        if (captchaImage) {
            // 添加时间戳防止缓存
            captchaImage.src = '/aistudio/captcha/image?' + new Date().getTime();
        }
        
        // 清空验证码输入框
        const captchaInput = document.getElementById('captcha');
        if (captchaInput) {
            captchaInput.value = '';
            captchaInput.focus();
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    const loginManager = new LoginManager();
    
    // 设置初始焦点
    loginManager.focusFirstInput();
    
    // 将实例暴露到全局作用域（用于调试）
    window.loginManager = loginManager;
});

// 为了向后兼容，保留全局函数
window.togglePassword = function() {
    if (window.loginManager) {
        window.loginManager.togglePassword();
    }
};