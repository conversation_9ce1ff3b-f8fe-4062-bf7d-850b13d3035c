/* 价格页面专用样式 - 苹果风格设计 */

/* 性能优化 */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.pricing-section * {
    box-sizing: border-box;
}

/* 价格页面主体区域 */
.pricing-section {
    padding-top: 140px;
    padding-bottom: 100px;
    background: var(--light-gray);
    min-height: 100vh;
    transform: translateZ(0); /* 启用硬件加速 */
}

/* 免费活动横幅 - 统一风格 */
.free-promotion-banner {
    display: inline-block;
    background: var(--success-color);
    color: white;
    padding: 16px 32px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
}

.free-promotion-banner:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(16, 185, 129, 0.2);
}

/* 价格卡片容器 - 统一风格 */
.pricing-card {
    background: var(--white);
    border-radius: 12px;
    box-shadow: var(--shadow);
    border: 1px solid var(--medium-gray);
    transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    will-change: transform;
    transform: translateZ(0);
}

.pricing-card:hover {
    transform: translateY(-6px) translateZ(0);
    box-shadow: var(--shadow-lg);
}

/* 推荐徽章已移除 */

/* 企业版卡片样式 - 移除蓝条和特殊边框 */
.enterprise-card {
    /* 与普通卡片样式保持一致 */
}

/* 价格卡片头部 - 统一风格 */
.pricing-header {
    padding: 40px 30px 30px;
    text-align: center;
    background: var(--light-gray);
}

.plan-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 2rem;
    box-shadow: var(--shadow-lg);
    transition: transform 0.15s ease-out;
    will-change: transform;
    transform: translateZ(0);
}

.plan-icon:hover {
    transform: scale(1.05) translateZ(0);
}

.personal-card .plan-icon {
    background: var(--success-color);
}

.plan-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 8px;
}

.plan-subtitle {
    color: var(--secondary-color);
    font-size: 1rem;
    margin-bottom: 24px;
}

/* 价格显示 - 苹果风格 */
.price-container {
    margin-bottom: 32px;
}

.current-price {
    margin-top: 0;
}

.free-badge {
    background: var(--success-color);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    display: inline-block;
    box-shadow: var(--shadow);
}

/* 价格卡片主体 - 统一风格 */
.pricing-body {
    padding: 0 30px 30px;
    flex-grow: 1;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-list li {
    display: flex;
    align-items: center;
    padding: 8px 0;
    font-size: 1rem;
    color: var(--dark-gray);
    line-height: 1.5;
}

.feature-list li i {
    color: var(--success-color);
    font-size: 1.1rem;
    margin-right: 12px;
    flex-shrink: 0;
}

.feature-list li span {
    flex: 1;
}

/* 高亮文本样式已移除 - 统一功能列表样式 */

/* 价格卡片底部 - 统一风格 */
.pricing-footer {
    padding: 30px;
    background: var(--light-gray);
    margin-top: auto;
}

.pricing-footer .btn {
    font-size: 1.1rem;
    font-weight: 600;
    padding: 12px 24px;
    border-radius: 12px;
    background: var(--primary-color);
    border: none;
    color: white;
    box-shadow: var(--shadow);
    transition: transform 0.15s ease-out, box-shadow 0.15s ease-out;
    will-change: transform;
    transform: translateZ(0);
}

.pricing-footer .btn:hover {
    transform: translateY(-2px) translateZ(0);
    box-shadow: var(--shadow-lg);
}

.personal-card .pricing-footer .btn {
    background: var(--success-color);
}

/* 企业版联系信息 */
.enterprise-contact {
    text-align: center;
}

.enterprise-contact p strong {
    color: #1d1d1f;
    font-size: 1.1rem;
}

/* 常见问题样式 - 统一风格 */
.faq-item {
    background: var(--white);
    padding: 24px;
    border-radius: 12px;
    box-shadow: var(--shadow);
    border: 1px solid var(--medium-gray);
    height: 100%;
    transition: transform 0.15s ease-out, box-shadow 0.15s ease-out;
    will-change: transform;
    transform: translateZ(0);
}

.faq-item:hover {
    transform: translateY(-4px) translateZ(0);
    box-shadow: var(--shadow-lg);
}

.faq-question {
    color: var(--dark-gray);
    font-weight: 600;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
}

.faq-question i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.faq-answer {
    color: var(--secondary-color);
    line-height: 1.6;
    margin: 0;
}

/* 联系我们区域 - 统一风格 */
.contact-section {
    background: var(--white);
    padding: 40px;
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--medium-gray);
}

.contact-section h3 {
    color: var(--dark-gray);
    font-weight: 700;
    margin-bottom: 16px;
}

.contact-section p {
    color: var(--secondary-color);
    line-height: 1.6;
}

.contact-buttons .btn {
    font-size: 1.1rem;
    font-weight: 600;
    padding: 12px 24px;
    border-radius: 12px;
    transition: transform 0.15s ease-out, box-shadow 0.15s ease-out;
    will-change: transform;
    transform: translateZ(0);
}

.contact-buttons .btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.contact-buttons .btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px) translateZ(0);
    box-shadow: var(--shadow);
}

.contact-buttons .btn-primary {
    background: var(--primary-color);
    border: none;
    color: white;
    box-shadow: var(--shadow);
}

.contact-buttons .btn-primary:hover {
    transform: translateY(-2px) translateZ(0);
    box-shadow: var(--shadow-lg);
}

/* 响应式设计 - 苹果风格 */
@media (max-width: 768px) {
    .pricing-section {
        padding-top: 120px;
        padding-bottom: 80px;
    }

    .pricing-header {
        padding: 36px 24px 24px;
    }

    .pricing-body {
        padding: 0 24px 24px;
    }

    .pricing-footer {
        padding: 24px;
    }

    .plan-title {
        font-size: 1.7rem;
    }

    .plan-icon {
        width: 72px;
        height: 72px;
        font-size: 1.8rem;
        border-radius: 18px;
    }

    .free-promotion-banner {
        font-size: 1rem;
        padding: 14px 24px;
    }

    .contact-section {
        padding: 36px 24px;
    }

    .contact-section h3 {
        font-size: 1.5rem;
    }

    .contact-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 12px;
        padding: 18px 32px;
    }

    .contact-buttons .btn:last-child {
        margin-bottom: 0;
    }

    .faq-item {
        padding: 24px;
    }

    /* 页脚响应式 */
    .simple-footer {
        padding: 30px 0;
        margin-top: 60px;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .pricing-card {
        margin-bottom: 32px;
        border-radius: 20px;
    }

    .recommended-badge {
        font-size: 0.8rem;
        padding: 8px 36px;
        top: 20px;
        right: -28px;
    }

    .plan-icon {
        width: 64px;
        height: 64px;
        font-size: 1.6rem;
        border-radius: 16px;
    }

    .feature-item {
        font-size: 0.95rem;
        padding: 14px 0;
    }

    .feature-item.highlight {
        padding: 14px 16px;
        margin: 6px -16px;
        border-radius: 10px;
    }

    .pricing-section {
        padding-top: 100px;
    }

    .free-promotion-banner {
        padding: 12px 20px;
        border-radius: 20px;
    }

    /* 移动端页脚优化 */
    .simple-footer {
        padding: 25px 0;
        margin-top: 40px;
    }

    .simple-footer .row {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .footer-link {
        display: block;
        margin: 5px 0 !important;
    }
}

/* 导航栏活跃状态 - 统一风格 */
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
    font-weight: 600;
}

/* 页脚样式 - 与其他页面保持一致 */
.simple-footer {
    background: var(--white);
    color: var(--dark-gray);
    padding: 40px 0;
    margin-top: 80px;
    border-top: 1px solid var(--medium-gray);
}

.simple-footer p {
    color: var(--secondary-color);
    margin: 0;
}

.footer-link {
    color: var(--secondary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: var(--primary-color);
}

/* 页面标题样式 - 统一风格 */
.pricing-section h1 {
    color: var(--dark-gray);
    font-weight: 700;
    margin-bottom: 24px;
}

.pricing-section .lead {
    color: var(--secondary-color);
    font-size: 1.2rem;
    line-height: 1.6;
}

/* 添加苹果风格的微妙动画 */
.pricing-card .plan-icon:hover {
    transform: scale(1.05);
}

.feature-item i {
    color: #34c759;
    font-size: 1.1rem;
}

/* 优化卡片间距 */
.pricing-section .row.justify-content-center {
    gap: 24px;
}

@media (min-width: 992px) {
    .pricing-section .row.justify-content-center {
        gap: 32px;
    }
}
