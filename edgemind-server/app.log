NOTE: Picked up JD<PERSON>_JAVA_OPTIONS: --add-exports java.base/jdk.internal.module=ALL-UNNAMED
[INFO] Scanning for projects...
[INFO] 
[INFO] --------------------< com.zibbava:edgemind-server >---------------------
[INFO] Building edgemind-server 0.0.1-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:3.5.0:run (default-cli) > test-compile @ edgemind-server >>>
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ edgemind-server ---
[INFO] Copying 1 resource from src/main/resources to target/classes
[INFO] Copying 50 resources from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.11.0:compile (default-compile) @ edgemind-server ---
[INFO] Nothing to compile - all classes are up to date
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ edgemind-server ---
[INFO] skip non existing resourceDirectory /Users/<USER>/IdeaProjects/edgemind/edgemind-server/src/test/resources
[INFO] 
[INFO] --- compiler:3.11.0:testCompile (default-testCompile) @ edgemind-server ---
[INFO] Changes detected - recompiling the module! :source
[INFO] Compiling 1 source file with javac [debug release 17] to target/test-classes
[INFO] 
[INFO] <<< spring-boot:3.5.0:run (default-cli) < test-compile @ edgemind-server <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.5.0:run (default-cli) @ edgemind-server ---
[INFO] Attaching agents: []
NOTE: Picked up JDK_JAVA_OPTIONS: --add-exports java.base/jdk.internal.module=ALL-UNNAMED

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.5.0)

2025-08-07T09:36:23.045+08:00  INFO 38763 --- [mcp-server] [           main] c.z.e.server.EdgemindServerApplication   : Starting EdgemindServerApplication using Java 23.0.2 with PID 38763 (/Users/<USER>/IdeaProjects/edgemind/edgemind-server/target/classes started by 10020414 in /Users/<USER>/IdeaProjects/edgemind/edgemind-server)
2025-08-07T09:36:23.046+08:00  INFO 38763 --- [mcp-server] [           main] c.z.e.server.EdgemindServerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-07T09:36:23.362+08:00  INFO 38763 --- [mcp-server] [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-07T09:36:23.363+08:00  INFO 38763 --- [mcp-server] [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-07T09:36:23.376+08:00  INFO 38763 --- [mcp-server] [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 0 Redis repository interfaces.
2025-08-07T09:36:23.638+08:00  INFO 38763 --- [mcp-server] [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8081 (http)
2025-08-07T09:36:23.646+08:00  INFO 38763 --- [mcp-server] [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-07T09:36:23.646+08:00  INFO 38763 --- [mcp-server] [           main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-07T09:36:23.675+08:00  INFO 38763 --- [mcp-server] [           main] o.a.c.c.C.[.[localhost].[/aistudio]      : Initializing Spring embedded WebApplicationContext
2025-08-07T09:36:23.675+08:00  INFO 38763 --- [mcp-server] [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 609 ms
Logging initialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.
Get /************ network interface 
Get network interface info: name:en0 (en0)
Initialization Sequence datacenterId:19 workerId:7
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        3.5.11 
____ ____    ___ ____ _  _ ____ _  _ 
[__  |__| __  |  |  | |_/  |___ |\ | 
___] |  |     |  |__| | \_ |___ | \| 
https://sa-token.cc (v1.42.0)
2025-08-07T09:36:24.456+08:00  INFO 38763 --- [mcp-server] [           main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable tools capabilities, notification: true
2025-08-07T09:36:24.458+08:00  INFO 38763 --- [mcp-server] [           main] o.s.a.m.s.a.McpServerAutoConfiguration   : Registered tools: 3
2025-08-07T09:36:24.458+08:00  INFO 38763 --- [mcp-server] [           main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable resources capabilities, notification: true
2025-08-07T09:36:24.458+08:00  INFO 38763 --- [mcp-server] [           main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable prompts capabilities, notification: true
2025-08-07T09:36:24.458+08:00  INFO 38763 --- [mcp-server] [           main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable completions capabilities
2025-08-07T09:36:24.531+08:00  INFO 38763 --- [mcp-server] [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8081 (http) with context path '/aistudio'
2025-08-07T09:36:24.535+08:00  INFO 38763 --- [mcp-server] [           main] c.z.e.server.EdgemindServerApplication   : Started EdgemindServerApplication in 1.647 seconds (process running for 1.781)
2025-08-07T09:36:49.245+08:00  INFO 38763 --- [mcp-server] [nio-8081-exec-1] o.a.c.c.C.[.[localhost].[/aistudio]      : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-07T09:36:49.246+08:00  INFO 38763 --- [mcp-server] [nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-07T09:36:49.249+08:00  INFO 38763 --- [mcp-server] [nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-08-07T09:37:39.428+08:00  WARN 38763 --- [mcp-server] [nio-8081-exec-1] c.z.e.s.config.GlobalExceptionHandler    : 用户未登录: 未能读取到有效 token
2025-08-07T09:40:06.921+08:00  INFO 38763 --- [mcp-server] [ionShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-08-07T09:40:06.930+08:00  INFO 38763 --- [mcp-server] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
